import csv
import os
import argparse
from pathlib import Path


def create_plugin_module(input_csv_path, output_py_path):
    """
    Reads a CSV of plugins, de-duplicates them, and writes them to a
    Python module file as a list of PluginInfo objects.
    """
    results = []
    seen_plugins = set()
    duplicates_found = 0

    try:
        with open(input_csv_path, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get("Plugin Name", "N/A").strip()
                vendor = row.get("Manufacturer", "N/A").strip()

                if name == "N/A" or vendor == "N/A":
                    continue

                # Normalize for duplicate checking
                plugin_id = (name.lower(), vendor.lower())

                if plugin_id not in seen_plugins:
                    seen_plugins.add(plugin_id)
                    results.append({"name": name, "vendor": vendor})
                else:
                    duplicates_found += 1

    except FileNotFoundError:
        print(f"Error: Input CSV not found at {input_csv_path}")
        return
    except Exception as e:
        print(f"An error occurred while reading the CSV: {e}")
        return

    # Sort the results alphabetically by vendor, then by name
    results.sort(key=lambda x: (x["vendor"].lower(), x["name"].lower()))

    # Determine the variable name from the output file name
    module_name = Path(output_py_path).stem
    list_variable_name = f"{module_name.upper()}"

    # Write the Python module file
    try:
        os.makedirs(os.path.dirname(output_py_path), exist_ok=True)
        with open(output_py_path, "w", encoding="utf-8") as f:
            f.write("#-*- coding: utf-8 -*-\n")
            f.write(
                '"""\nList of plugins in the {} category.\n"""\n'.format(module_name)
            )
            f.write("from ..base import PluginInfo, PluginType\n\n")
            f.write(f"{list_variable_name} = [\n")
            for item in results:
                # Escape quotes in names and vendors
                name_escaped = item["name"].replace("'", "\\'")
                vendor_escaped = item["vendor"].replace("'", "\\'")
                f.write(
                    f"    PluginInfo(name='{name_escaped}', vendor='{vendor_escaped}'),\n"
                )
            f.write("]\n")

        print(f"Successfully created module: {output_py_path}")
        print(f"  - Wrote {len(results)} unique plugins to the list.")
        print(f"  - Found and skipped {duplicates_found} duplicates.")

    except Exception as e:
        print(f"An error occurred while writing the Python module: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Create a plugin data module from a scraped CSV file."
    )
    parser.add_argument(
        "input_csv", help="Path to the input CSV file from the scraper."
    )
    parser.add_argument(
        "output_py", help="Path to the output Python module file to be created."
    )
    args = parser.parse_args()

    create_plugin_module(args.input_csv, args.output_py)
