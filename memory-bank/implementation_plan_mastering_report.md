# Detailed Implementation Plan: Enhanced "Ready for Mastering" Report

## Phase 1: Genre-Aware System & Mastering Report Page (Priority: HIGH)

This phase focuses on creating the basic structure for the "Mastering Report" page and integrating the genre-aware analysis system.

### 1. Create Mastering Report Page Structure

-   **Objective:** Create the basic page structure and navigation for the Mastering Report page.
-   **Tasks:**
    -   Add new route `/mastering` to `App.tsx`
    -   Add "Mastering Report" nav item to `TopNavBar.tsx` (disabled when no session data)
    -   Create new page component: `MasteringReportPage.tsx`
    -   Add basic layout: Header, genre selection area, analysis results sections

### 2. Extend sessionStore for Mastering Data

-   **Objective:** Add the necessary state to the sessionStore for managing mastering analysis data.
-   **Tasks:**
    -   Add to `sessionStore.ts`:
        ```typescript
        interface MasteringAnalysis {
            genre: string;
            strict_mode: boolean;
            plugin_analysis: any; // From existing /api/v1/mastering-analysis
            master_bus_analysis: any;
            session_hygiene: any;
            // Future: gain_staging, file_consistency
        }
        ```

### 3. Create Reusable Components

-   **Objective:** Create reusable components for the Mastering Report page.
-   **Tasks:**
    -   Create `GenreSelectionPanel.tsx` - Genre dropdown + strict mode
    -   Create `AnalysisCard.tsx` - Reusable card for each analysis section
    -   Create `AnalysisSection.tsx` - Expandable section with pass/warn/fail indicators
    -   Create `MasteringExportOptions.tsx` - Export mastering report functionality

### 4. API Integration

-   **Objective:** Integrate the frontend with the existing `/api/v1/mastering-analysis` endpoint.
-   **Tasks:**
    -   Use existing `/api/v1/mastering-analysis` endpoint
    -   Add genre and strict_mode parameters
    -   Follow existing error handling patterns from `DashboardPage.tsx`
    -   Use Axios with Vite proxy (consistent with current approach)

### 5. Name-Based Plugin Analysis & Genre-Aware System

-   **Objective:** Implement comprehensive plugin analysis without requiring binary parameter decoding and integrate the genre-aware system.
    -   Leverage existing code from previous phases.
-   **Tasks:**
    -   **Refactor Plugin Data Structure:**
        -   Organize plugin definitions into `backend/parsing/engines/plugin_data/` directory.
        -   Create `base.py` for `PluginInfo` dataclass.
        -   Create separate modules for plugin categories (e.g., `limiters.py`, `compressors.py`, `eqs.py`, `saturation.py`, `clippers.py`, `metering.py`, `mastering_suites.py`).
        -   Update `patterns.py` to include general keyword and vendor-model patterns for all categories.
        -   Update `__init__.py` in `plugin_data` to unify all plugin data into a central `PLUGIN_DATABASE`.
    -   Create plugin blacklist engine based on plugin names and track positions.
    -   Implement pattern matching for problematic plugins:
        -   **Master Bus Red Flags:** Any limiter (Pro-L, L1/L2/L3, Ozone Maximizer), clippers, aggressive EQs/compressors, mastering suites.
        -   **General Red Flags:** Demo/Trial plugins, aggressive limiters/clippers on individual tracks.
        -   **Context-Aware Analysis:** Same plugin acceptable on track vs. master bus.
    -   Add comprehensive plugin categorization system:
        -   **High Priority:** Limiter, Compressor, EQ, Saturation/Distortion, Clipper, Metering.
        -   **Medium Priority:** Reverb, Delay, Stereo Imaging, Mastering Suites.
    -   Implement severity scoring (warning vs. critical issues).
    -   Implement user-specified genre selection with genre-specific mastering rules:
        -   Create `genre_rules.py` module with base rule system.
        -   Define genre-specific rule sets.
        -   Extend `mastering_analyzer.py` to accept genre parameter.
        -   Implement rule loading system based on genre selection.
        -   Add strict mode toggle for conservative analysis regardless of genre.
        -   Create rule inheritance system (base rules + genre adjustments).

**Deliverables:**

-   New React components for mastering report with genre selection.
-   Enhanced UI with visual indicators, genre-specific context, and detailed breakdowns.
-   Export functionality for sharing with mastering engineers.
-   `backend/parsing/engines/plugin_data/` directory with organized plugin definitions.
-   `plugin_blacklist.py` module with comprehensive rule engine.
-   Expanded plugin categorization and pattern matching system.
-   Integration with existing FX extraction.
-   `genre_rules.py` module with comprehensive rule sets.
-   Updated `mastering_analyzer.py` with genre awareness.
-   Enhanced frontend UI with genre selection.
-   Documentation of genre-specific analysis criteria.

## Phase 2: Item-Level Analysis (Priority: HIGH)

### 2.1 Item Data Extraction

**Goal:** Extract item and take level information for gain staging analysis

**Tasks:**

-   Create `item_extractor.py` module
-   Extract `ITEM` chunks and their properties:
    -   Position, length, volume (`VOLPAN`)
    -   Take information and gain values
    -   Source file paths and types
-   Associate items with their parent tracks
-   Handle multiple takes per item

**Deliverables:**

-   New `Item` and `Take` data models
-   `item_extractor.py` module
-   Integration with existing track extraction

### 2.2 Gain Staging Analysis

**Goal:** Implement comprehensive gain staging checks

**Tasks:**

-   Create `gain_analysis.py` module
-   Implement analysis functions:
    -   Item gain analysis (flag items > +6dB, < -20dB per `.clinerules`)
    -   Take gain analysis
    -   Track volume analysis
    -   Cumulative gain calculation through FX chains
-   Generate gain staging warnings and recommendations

**Deliverables:**

-   `gain_analysis.py` module with analysis functions
-   Integration with "Ready for Mastering" report
-   Configurable thresholds based on `.clinerules`

### 2.3 Media File Validation

**Goal:** Validate media files and detect issues

**Tasks:**

-   Extract `SOURCE` file paths from items
-   Implement file existence checks
-   Sample rate mismatch detection (compare to project sample rate)
-   Bit depth analysis where available
-   Generate missing/problematic media reports

**Deliverables:**

-   Media validation functions in `item_extractor.py`
-   File system integration for existence checks
-   Media issue reporting

## Phase 3: Advanced Project Analysis (Priority: MEDIUM)

### 3.1 Project Structure Analysis

**Goal:** Analyze project completeness and organization

**Tasks:**

-   Extract markers and regions from project
-   Analyze tempo map changes
-   Check for project notes and documentation
-   Evaluate track organization (folders, naming conventions)
-   Detect empty or unused elements

**Deliverables:**

-   `project_structure_analyzer.py` module
-   Project completeness scoring system
-   Integration with hygiene checks

### 3.2 Advanced Routing Analysis

**Goal:** Enhance routing analysis for mastering preparation

**Tasks:**

-   Detect parallel processing chains
-   Identify feedback loops or problematic routing
-   Analyze send levels and destinations
-   Flag tracks not routed to master
-   Detect complex routing that might complicate mastering

**Deliverables:**

-   Enhanced routing analysis in `routing_extractor.py`
-   Routing complexity scoring
-   Mastering-specific routing recommendations

## Phase 4: "Ready for Mastering" Report Enhancement (Priority: HIGH)

### 4.1 Enhanced Backend Logic

**Goal:** Implement comprehensive mastering readiness analysis with
genre-awareness

**Tasks:**

-   Create `mastering_analyzer.py` module
-   Integrate genre-aware analysis from Phase 1.5
-   Implement analysis categories:
    -   **Plugin Analysis:** Blacklisted plugins (genre-specific), finalizer
        detection, parameter analysis
    -   **Gain Staging:** Item/take/track level analysis (genre-specific
        thresholds)
    -   **Session Hygiene:** Naming, organization, empty elements
    -   **Technical Issues:** Missing files, sample rate mismatches
    -   **Master Bus Analysis:** Plugin chain analysis (genre-specific),
        automation detection
-   Generate scored recommendations with severity levels, adjusted by genre
-   Create actionable suggestions for each issue type, with genre-specific
    context
-   Add explanatory text for why certain rules apply to the selected genre

**Deliverables:**

-   `mastering_analyzer.py` with comprehensive and genre-aware analysis
-   Scoring system with pass/warn/fail categories
-   Detailed recommendation engine

### 4.2 Enhanced Frontend UI

**Goal:** Create comprehensive and genre-aware mastering report interface

**Tasks:**

-   Design new "Ready for Mastering" page/component, including genre selection
    UI (from Phase 1.5)
-   Implement visual indicators for each analysis category
-   Show genre-specific analysis results with context (e.g., "For Electronic
    music, this is acceptable...")
-   Display rule explanations based on selected genre
-   Create expandable sections for detailed findings
-   Add export functionality for mastering notes (including selected genre)
-   Implement filtering and sorting of issues by severity

**Deliverables:**

-   New React components for mastering report with genre selection
-   Enhanced UI with visual indicators, genre-specific context, and detailed
    breakdowns
-   Export functionality for sharing with mastering engineers

## Implementation Timeline

**Week 1-2:** Phase 1.1-1.2 (Plugin data investigation and model enhancement)
**Week 3:** Phase 1.3 (Plugin extraction enhancement) **Week 4:** Phase 1.4
(Plugin decoder framework) + **Phase 1.5 (Genre system)** **Week 5-6:** Phase
2.1-2.2 (Item extraction and gain analysis with genre rules) **Week 7:** Phase
2.3 (Media validation) **Week 8:** Phase 4.1 (Backend mastering analyzer with
genre integration) **Week 9:** Phase 4.2 (Frontend enhancement with genre UI)
**Week 10:** Phase 3.1-3.2 (Advanced analysis - if time permits)

## Risk Mitigation

**Technical Risks:**

-   Plugin parameter decoding complexity → Start with simple plugins, build
    incrementally
-   Performance impact of enhanced parsing → Profile and optimize critical paths
-   Backward compatibility → Maintain existing API contracts

**Scope Risks:**

-   Feature creep → Focus on MVP mastering checks first
-   Plugin decoder maintenance → Design extensible framework for community
    contributions
