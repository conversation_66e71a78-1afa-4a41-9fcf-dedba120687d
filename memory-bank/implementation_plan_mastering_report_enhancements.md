# Implementation Plan: Enhanced "Ready for Mastering" Report

## Architecture Assessment for Enhanced Report (Added 2025-08-06)

This assessment reviews the current data extraction and structure to ensure its
fitness for implementing the enhanced "Ready for Mastering" report, as per user
request.

### ✅ **Strengths - What's Working Well**

1.  **Solid Foundation Architecture**:

    -   Clean separation with `ParsingContext`, `BaseExtractor`, and specialized
        engines.
    -   `PluginAnalysisEngine` provides comprehensive plugin analysis including
        categorization, blacklisting, and automation data.
    -   `PluginBlacklistRules` supports genre-aware analysis with confidence
        scoring.
    -   Rich data models in `models.py` already contain extensive flags and
        structures for mastering-related analysis.

2.  **Plugin Analysis Capabilities**:

    -   Effective plugin categorization via `PluginRegistry` and the populated
        `plugin_data` modules.
    -   Parameter decoding for known plugins (e.g., ReaEQ, Pro-L2).
    -   Master bus chain analysis, including limiter detection and order
        checking.
    -   Extraction of automation data for individual plugin parameters.

3.  **Existing Mastering Analysis Features**:
    -   Genre-specific rules defined in `genre_rules.py`, applied via
        `ParsingContext`.
    -   Session hygiene analysis (generic names, duplicate names, folder
        organization).
    -   File reference consistency checks and gain staging analysis.
    -   Detection of automation on the master bus.

### ⚠️ **Gaps for Enhanced Implementation**

Based on the AI research report and the goal of a more comprehensive mastering
report, the following gaps are identified:

#### **Critical Foundational Gaps (Priority for Phase 1):**

1.  **Missing RPP Data Extraction**:

    -   **Record-armed tracks**: The `is_record_armed` status is not currently
        extracted for tracks.
    -   **Item Fade Analysis**: `ItemExtractor` does not currently parse item
        fade-in (`FADEIN`) and fade-out (`FADEOUT`) lengths or shapes from the
        RPP.
    -   **Additional Item Properties**: Key item properties like `REVERSE`
        status and `PLAYRATE` are not extracted. Pitch envelope data is also not
        parsed.
    -   **Project Export Settings**: While `MetadataExtractor` gets bit depth
        and sample rate, it doesn't check for or validate preferred export file
        formats if specified in the RPP.

2.  **Incomplete Analysis Logic (Relying on foundational data):**
    -   **Stereo Balance Check**: No analysis of overall pan distribution across
        tracks to detect significant imbalances.
    -   **Project Boundaries/Silence**: No checks for silence at the
        beginning/end of the timeline (e.g., if the first item starts at 0s with
        no fade-in).
    -   **Master Track Flag Consolidation**: The `has_limiter_on_master` flag on
        the `MasterTrack` model exists but is not yet populated based on the
        plugin analysis results.

#### **Further Gaps (for subsequent phases):**

-   **Advanced Plugin Analysis**: Deeper role classification (EQ, Compressor,
    Exciter), detection of redundant/conflicting FX, plugin chain complexity
    metrics.
-   **Session Structure Analysis**: More detailed tempo/time signature
    consistency checks, automation complexity scoring, orphaned bus detection in
    routing.

### 🔧 **Required Model Extensions (Immediate Priority for Phase 1 Foundational Work)**

To address the critical foundational gaps, `backend/parsing/models.py` needs the
following extensions:

```python
# In backend/parsing/models.py

# Add to Track model:
# is_record_armed: bool = field(default=False)

# Add to Item model:
# fade_in_length: Optional[float] = field(default=None) # Or parse directly from RPP if available
# fade_out_length: Optional[float] = field(default=None)
# fade_in_shape: Optional[int] = field(default=None) # Reaper stores fade shapes
# fade_out_shape: Optional[int] = field(default=None)
# is_reversed: bool = field(default=False)
# playrate: float = field(default=1.0)
# pitch_envelope_details: Optional[Any] = None # Placeholder if detailed pitch env parsing is added later
```

### 📊 **Data Availability Assessment Summary**

-   **Available in RPP but NOT YET EXTRACTED for new checks**: `RECARM` (track
    record arm status), `FADEIN`/`FADEOUT` (item fades), `REVERSE`, `PLAYRATE`
    (item properties). Project export format (if stored). Detailed tempo map
    changes.
-   **Available and EXTRACTED (can be leveraged for new analysis logic)**:
    Plugin details, track parameters (volume, pan, mute, etc.), item
    positions/lengths, automation data.
-   **NOT available in RPP (system limitations)**: Direct audio measurements
    (peak levels, LUFS), real-time plugin states.

### 💡 **Recommendations & Updated Priority**

**Architecture is Fit for Purpose**: The current backend architecture is
well-suited to incorporate these enhancements with targeted modifications rather
than a major overhaul.

**Priority: Foundational Data Extraction and Model Updates**: Addressing the
"Missing RPP Data Extraction" and implementing the "Required Model Extensions"
(as detailed above) is the **top priority**. This ensures the necessary data
points are parsed and available in the data models before building the analysis
logic outlined in Phase 1 of the plan below.

**Updated Implementation Strategy Focus**:

1.  **Models First**: Update `backend/parsing/models.py` with the new fields
    identified above.
2.  **Enhance Extractors**:
    -   Modify `TrackExtractor` to parse `RECARM` flags and populate
        `Track.is_record_armed`.
    -   Modify `ItemExtractor` to parse `FADEIN`, `FADEOUT`, `FADESHAPE`,
        `REVERSE`, `PLAYRATE` and populate the corresponding new fields in the
        `Item` model.
    -   Investigate and, if found, modify `MetadataExtractor` to parse any
        project-level export format settings.
3.  **Integrate into Core Logic**: Ensure `backend/parsing/core.py` correctly
    utilizes these updated extractors and the new data is part of the
    `ParsedSession`.
4.  **Proceed with Phase 1 Analysis Logic**: Once the foundational data is
    reliably extracted and modeled, implement the analysis logic detailed in
    Phase 1 below.

---

## Recommended Analysis Architecture (Backend-Centric) (Added 2025-08-06)

To ensure a robust, maintainable, and performant "Ready for Mastering" report, a
backend-centric analysis architecture is recommended. This approach centralizes
the analytical heavy lifting on the server, providing a clean and comprehensive
data structure to the frontend for presentation.

### Core Principles:

1.  **Backend as the Single Source of Truth**: All analysis rules, issue
    detection logic, scoring, and genre-specific adjustments reside in the
    backend.
2.  **Comprehensive API Response**: The backend API
    (`/api/v1/mastering-analysis`) will return a fully processed analysis,
    including an overall health score, a consolidated list of issues, and
    categorized data for detailed sections.
3.  **Frontend as a Pure Presentation Layer**: Frontend components will focus
    solely on displaying the data provided by the backend, minimizing
    client-side logic for analysis.

### Benefits:

-   **Performance**: Complex calculations do not bog down the user's browser.
-   **Consistency**: Analysis logic is applied uniformly, ensuring consistent
    results.
-   **Maintainability**: Centralized logic is easier to update, test, and debug.
-   **Scalability**: Backend can be scaled independently if analysis becomes
    more complex.
-   **Testability**: Backend analysis modules can be unit-tested in isolation.
-   **Simplified Frontend**: Reduces complexity in frontend components.

### Proposed Backend API Structure (`/api/v1/mastering-analysis`):

The API response should be structured to provide all necessary information for
the frontend dashboard and detailed sections.

```json
{
    "overall_health_score": 85, // Calculated by backend
    "genre": "rock", // Current genre context
    "strict_mode": false, // Current strict_mode context
    "summary_metrics": {
        // For quick display on dashboard
        "critical_issues_count": 2,
        "warning_issues_count": 5,
        "info_issues_count": 3,
        "passed_checks_count": 20
    },
    "issues": [
        // Consolidated list of all detected issues
        {
            "id": "master-limiter-001", // Unique ID for the issue
            "category": "Master Bus Analysis", // e.g., Plugin, Master Bus, Session Hygiene, File Reference, Item Properties
            "severity": "critical", // critical, warning, info, pass
            "message": "Multiple limiters detected on master bus.",
            "recommendation": "Ensure only one final limiter is active on the master bus.",
            "details_key": "master_bus_analysis.multiple_limiters", // Optional: key to link to more detailed data
            "affected_elements": [
                // Optional: GUIDs or names of tracks/items/plugins involved
                {
                    "type": "track",
                    "guid": "guid-master-track",
                    "name": "Master"
                }
            ]
        }
        // ... more issues
    ],
    "detailed_analysis": {
        // Raw and semi-processed data for each section, allowing frontend to display details
        "plugin_analysis": {
            /* ... */
        },
        "master_bus_analysis": {
            /* ... */
        },
        "session_hygiene": {
            /* ... */
        },
        "file_reference_analysis": {
            /* ... */
        },
        "item_property_analysis": {
            /* e.g., fades, record-armed, reversed items */
        },
        "project_settings_analysis": {
            /* e.g., bit depth, sample rate */
        }
        // ... other specific analysis sections
    }
}
```

### Backend Implementation Steps:

1.  **Create/Enhance Mastering Analysis Engine**:
    -   A central engine (e.g.,
        `backend/parsing/engines/mastering_analysis_engine.py`) will orchestrate
        the various analysis sub-modules (plugin, hygiene, file-ref, new Phase 1
        checks).
    -   This engine will be responsible for:
        -   Gathering data from all relevant extractors.
        -   Applying analysis rules (including genre-specific rules from
            `genre_rules.py`).
        -   Generating a consolidated list of `MasteringAnalysisIssue` objects
            (a Pydantic model should be defined for this).
        -   Calculating an `overall_health_score` based on the number and
            severity of issues.
        -   Populating the `summary_metrics` and `detailed_analysis` sections of
            the API response.
2.  **Define `MasteringAnalysisIssue` Model**: Create a Pydantic model in
    `backend/parsing/models.py` for the structure of an individual issue,
    including fields like `id`, `category`, `severity`, `message`,
    `recommendation`, `details_key`, `affected_elements`.
3.  **Refactor Existing Analysis Logic**:
    -   Identify analysis logic currently in frontend components (e.g., how
        `PluginAnalysisSection` determines issues) and move it to the backend
        engine or relevant sub-modules.
    -   Ensure all checks from the "Original Implementation Plan" (Phases 1-3)
        are implemented within this backend engine.
4.  **Update API Endpoint**: Modify the FastAPI endpoint in `backend/main.py`
    for `/api/v1/mastering-analysis` to use the new engine and return the
    comprehensive response structure.
5.  **Testing**: Implement thorough unit tests for the analysis engine and its
    sub-modules.

This backend-centric architecture will provide a more robust and scalable
foundation for the "Ready for Mastering" report.

---

## Original Implementation Plan (Phased Approach)

**Note**: This plan should now be executed within the **Recommended Analysis
Architecture (Backend-Centric)** framework described above. The backend will
perform the analysis and generate the issues and scores.

Based on the AI research report (provided by the user on 2025-08-06) and the
current SessionView architecture, this plan outlines the implementation of
enhancements to the "Ready for Mastering" report. The implementation is
organized into phases, prioritized by impact and feasibility.

**Note**: Phase 1 below assumes the foundational data extraction and model
updates outlined in the "Architecture Assessment" above have been completed as a
prerequisite.

## Phase 1: Critical Gap Fixes (High Impact, Medium Complexity)

**Target: Address the most important missing checks that are feasible with
current data (once foundational data extraction is complete).**

### 1.1 Enhanced Master Bus Analysis

-   **Master-bus limiter detection enhancement**: Expand beyond the current
    blacklist to specifically flag ANY limiter/maximizer plugin found on the
    master track. (Current implementation partially covers this via `is_limiter`
    and `is_blacklisted_master` flags in `PluginAnalysisResult` and `Track`
    models; this task involves ensuring `MasterTrack.has_limiter_on_master` is
    correctly set).
-   **Disabled/Mute/Arm warnings**:
    -   Flag tracks where `track.muted` is true (uses existing `Track.muted`).
    -   Flag tracks containing FX where `plugin.is_bypassed` is true (uses
        existing `PluginAnalysisResult.is_bypassed`).
    -   Flag tracks that are record-armed (uses new `Track.is_record_armed`).
-   **Master fader automation warning**: Enhance detection of volume/pan
    automation on the master track (Currently `track.has_master_bus_automation`
    flag exists, may need refinement or more specific conditions).

### 1.2 Project-Level Quality Checks

-   **High-quality export settings**:
    -   Check `metadata.project_bit_depth` (warn if 16-bit or lower).
    -   Check `metadata.sample_rate` (flag non-standard rates, e.g., outside
        44.1kHz-96kHz).
    -   (Future: Validate project export format if data becomes available).
-   **Silence/headroom at boundaries**:
    -   Check if the first media item (`item.position`) starts exactly at 0.0s
        AND `item.fade_in_length` (new field) is zero or very short (recommend
        pre-roll/silence).
    -   Detect items with zero-length or very short fades using new
        `item.fade_in_length` and `item.fade_out_length` fields.
-   **Stereo balance check**: Analyze pan distribution across all tracks
    (`track.pan`). Flag if there's a severe skew (e.g., a high percentage of
    tracks panned hard to one side with few or none to the other).

### 1.3 Enhanced Session Hygiene

-   **Media item properties analysis**:
    -   Flag items where `item.is_reversed` (new field) is true.
    -   Flag items where `item.playrate` (new field) is not `1.0`.
    -   (Future: Flag items with unusual pitch changes if
        `item.pitch_envelope_details` is populated).
-   **Project metadata completeness**: Check if `metadata.title` and potentially
    `metadata.author` (if Reaper supports/stores it and it's extracted) are
    populated.

## Phase 2: Advanced Analysis Features (High Impact, Higher Complexity)

**Target: Deeper insights using existing data structures and new logic.**

### 2.1 Plugin Chain Intelligence

-   **Master-bus plugin role classification**: Enhance `PluginAnalysisEngine` to
    categorize master bus plugins by common roles (EQ, Compressor, Exciter,
    Stereo Widener, etc.) beyond just "limiter" or "metering".
-   **Redundant/contradictory FX detection**: Implement logic to detect patterns
    like "Limiter followed by Compressor" or multiple similar EQs/Compressors in
    series on the same track.
-   **Plugin count and complexity metrics**: Calculate and report FX chain
    lengths per track. Flag tracks with an unusually high number of plugins.

### 2.2 Session Structure Analysis

-   **Automation complexity analysis**:
    -   Report number of tracks with active volume/pan automation.
    -   Count `len(automation_lane.points)` for key parameters to identify
        highly complex automation.
-   **Tempo/time signature consistency**: Analyze `TEMPO_MAP` data. Flag
    sessions with an excessive number of tempo/time signature changes or highly
    unusual signatures.
-   **Enhanced routing validation**:
    -   Verify all tracks eventually route to the master output.
    -   Detect "orphaned" buses (buses that receive audio but don't send it
        anywhere meaningful).

### 2.3 Enhanced Parameter Analysis

-   **Expand plugin parameter decoding**: Systematically review and enhance
    existing decoders (e.g., `ReaEQDecoder`, `ProL2Decoder`). Add new decoders
    for other common stock or third-party plugins where parameter structure is
    known or can be inferred.
-   **Parameter extreme value detection**: For decoded parameters, define
    typical ranges and flag values that are at extreme ends (e.g., compressor
    threshold at -0.1dB, very high EQ gains).

## Phase 3: Genre-Specific Intelligence (Medium Impact, Medium Complexity)

**Target: Context-aware analysis based on musical genre.**

### 3.1 Genre Detection and Rules Engine

-   **Enhanced genre rules system**: Expand `genre_rules.py` and
    `ParsingContext.genre_rules` to include more nuanced checks and advice based
    on the selected genre.
-   **Track naming pattern analysis**: Implement heuristics in a new
    `TrackNamingAnalyzer` to identify common track types (Kick, Snare, Bass,
    Vocals, etc.) based on `track.name` to feed into genre-specific logic.

### 3.2 Genre-Specific Checks

-   **EDM/Pop**: Check for typical kick/sub-bass separation (e.g., distinct
    tracks named "Kick" and "Sub"). Look for common sidechain compression
    patterns (e.g., compressor on "Bass" track potentially keyed by "Kick").
-   **Rock/Metal**: Heuristically check for drum kit completeness (e.g.,
    presence of tracks named "Kick", "Snare", "Overheads", "Room").
-   **Hip-Hop/R&B**: Check for prominent bass elements. Analyze overall pan
    distribution for mono compatibility (flag if too many elements are
    hard-panned with nothing in the center).
-   **Classical/Jazz**: Flag heavy use of master bus compression/limiting.
    Suggest verifying transparency.

## Phase 4: Frontend UI Enhancement Plan (High Impact, Frontend Focus) (Updated 2025-08-06)

**Target: Create a highly usable, informative, and visually appealing interface
for the "Ready for Mastering" report, leveraging the comprehensive data provided
by the backend-centric analysis.**

This plan assumes the backend API (`/api/v1/mastering-analysis`) provides data
structured similarly to the "Proposed Backend API Structure" outlined in the
"Recommended Analysis Architecture" section.

### Phase A: Core Dashboard & Reusable Components (Largely Completed)

**Objective**: Establish the main dashboard structure and foundational UI
elements.

1.  **`MasteringDashboard.tsx` Component (COMPLETED)**:
    -   Displays overall health score, critical issue count, warning count.
    -   Shows a list of top priority issues (critical/warnings).
    -   Serves as the primary summary view at the top of
        `MasteringReportPage.tsx`.
    -   **Data Source**: `overall_health_score`, `summary_metrics`, and top
        issues from the `issues` array in the API response.
2.  **Reusable UI Components (COMPLETED)**:
    -   `HealthIndicator.tsx`: Visual bar for the health score.
    -   `MetricDisplay.tsx`: Card to show individual metrics (e.g., critical
        issue count).
    -   `IssueCard.tsx`: Standardized card to display individual issues.
3.  **Integration into `MasteringReportPage.tsx` (COMPLETED)**:
    -   `MasteringDashboard` rendered prominently.
    -   Existing sections (`PluginAnalysisSection`, `SessionHygieneSection`,
        etc.) will be refactored or replaced in later phases to consume new API
        data.
4.  **Zustand Store Updates (`sessionStore.ts`) (COMPLETED)**:
    -   Defined `MasteringAnalysisIssue` type.
    -   Updated `MasteringAnalysis` type to expect the new API response
        structure (including `overall_health_score`, `summary_metrics`, `issues`
        array, `detailed_analysis` object).

### Phase B: Enhanced Analysis Display & Section Refactoring

**Objective**: Redesign existing analysis sections to use the new backend data
structure and improve clarity. Create new sections for Phase 1 backend analysis
results.

1.  **Refactor `PluginAnalysisSection.tsx`**:
    -   Consume `detailed_analysis.plugin_analysis` and relevant issues from the
        main `issues` array.
    -   Display plugin-specific issues using `IssueCard.tsx`.
    -   Clearly present data on blacklisted plugins, problematic parameters,
        etc.
2.  **Refactor `SessionHygieneSection.tsx`**:
    -   Consume `detailed_analysis.session_hygiene` and relevant issues.
    -   Display hygiene issues (generic names, duplicates, folder structure)
        using `IssueCard.tsx`.
3.  **Refactor `FileReferenceSection.tsx`**:
    -   Consume `detailed_analysis.file_reference_analysis` and relevant issues.
    -   Clearly list problematic file paths, sample rate/bit depth mismatches.
4.  **New Section: `ItemPropertyAnalysisSection.tsx`**:
    -   Display issues related to item fades (zero-length, abrupt), reversed
        items, non-standard playrates.
    -   Data from `detailed_analysis.item_property_analysis` and relevant
        issues.
5.  **New Section: `ProjectSettingsAnalysisSection.tsx`**:
    -   Display issues related to project bit depth, sample rate, record-armed
        tracks (if considered a project-level setting concern for mastering).
    -   Data from `detailed_analysis.project_settings_analysis` and relevant
        issues.
6.  **New Section: `MasterBusDeepDiveSection.tsx`**:
    -   Focus specifically on master bus chain issues: multiple limiters,
        limiter order, problematic plugins on master, master fader automation.
    -   Data from `detailed_analysis.master_bus_analysis` and relevant issues.
7.  **Interactive Filtering/Sorting**:
    -   Implement controls within the `MasteringReportPage` or
        `MasteringDashboard` to filter the main `issues` list by severity
        (Critical, Warning, Info, Pass) and category.
    -   Allow sorting issues.
8.  **Enhanced Tooltips & Recommendations**:
    -   Ensure all issues displayed via `IssueCard.tsx` have clear, actionable
        recommendations sourced from the backend.

### Phase C: Advanced Visualizations & User Experience Refinements

**Objective**: Introduce more advanced UI elements and further polish the user
experience.

1.  **FX Chain Visualizer (`PluginChainVisualizer.tsx`)**:
    -   Develop a component to visually represent the plugin chain, especially
        for the master bus.
    -   Highlight problematic plugins or chain order issues directly in the
        visualization.
    -   Data primarily from `detailed_analysis.plugin_analysis` for the master
        track.
2.  **Genre-Specific Guidance Panel**:
    -   If a genre is selected (via `GenreSelectionPanel.tsx`), display a
        dedicated panel showing:
        -   Common considerations for that genre.
        -   Checks passed/failed specific to that genre's rules.
    -   This would consume genre-specific flags or advice from the backend's
        analysis.
3.  **Navigation Improvements**:
    -   Implement "Scroll to Section" links from the dashboard or a sidebar for
        quick navigation to detailed analysis sections.
    -   Consider a sticky sub-navigation for the report page on larger screens.
4.  **Report Export/Print Styling**:
    -   Add basic print-friendly CSS for users who want to export or print the
        report.
5.  **Accessibility Review (WCAG)**:
    -   Ensure good color contrast, keyboard navigability, and ARIA attributes
        for all interactive elements.

### General Frontend Considerations:

-   **Error Handling**: Robustly handle API errors or unexpected data
    structures.
-   **Loading States**: Provide clear loading indicators while data is being
    fetched or processed.
-   **Responsiveness**: Ensure the report is usable across different screen
    sizes.
-   **Code Quality**: Maintain component modularity, clear props, and type
    safety with TypeScript.

This phased frontend plan aims to iteratively build a comprehensive and
user-friendly mastering report interface.

## Phase 5: Advanced Features (Lower Priority) (Renumbered from original Phase 5)

**Target: Nice-to-have features for power users.**

### 5.1 Sidechain and Advanced Routing

-   **Sidechain detection**: Enhance RPP parsing to identify external sidechain
    routings between tracks.
-   **Complex routing analysis**: Develop logic to understand and report on more
    complex routing scenarios like parallel processing buses.

### 5.2 Predictive Analysis (Future Consideration)

-   **Pattern recognition**: Explore possibilities for identifying common
    combinations of issues.
-   **Workflow suggestions**: Offer intelligent suggestions for fixing detected
    patterns of problems.

## Implementation Strategy Notes

### Backend Architecture Integration

-   **New Extractors/Engines**: Consider creating new, focused extractor classes
    or enhancing existing ones. For example:
    -   `ProjectSettingsExtractor` (for export settings, project boundaries,
        metadata completeness).
    -   `AdvancedPluginAnalyzer` (for role classification, redundancy checks).
    -   `GenreAnalyzer` (for track naming patterns, genre-specific rule
        application).
-   **Model Updates**: The `backend/parsing/models.py` will need to be updated
    to include new flags and data fields to store the results of these new
    analyses.
-   **Orchestration**: The main parsing orchestrator (`backend/parsing/core.py`)
    will need to integrate these new analysis steps.

### Illustrative Data Model Extensions (Conceptual)

```python
# Example additions to backend/parsing/models.py

@dataclass
class Metadata:
    # ... existing fields ...
    # Project quality flags
    is_suboptimal_bit_depth: bool = field(default=False)
    is_nonstandard_sample_rate: bool = field(default=False)
    has_incomplete_project_info: bool = field(default=False) # e.g. missing title/artist
    stereo_balance_issue_description: Optional[str] = field(default=None)
    first_item_starts_at_project_start: bool = field(default=False)
    project_has_tempo_map_issues: bool = field(default=False)
    project_automation_complexity_rating: Optional[str] = field(default=None) # e.g. "Low", "Medium", "High"

@dataclass
class Track:
    # ... existing fields ...
    is_record_armed: bool = field(default=False) # New field
    fx_chain_complexity_rating: Optional[str] = field(default=None) # e.g. "Low", "Medium", "High"
    # Note: track.muted already exists.
    # Note: track.fx[].is_bypassed can be aggregated.

@dataclass
class Item:
    # ... existing fields ...
    has_zero_length_fade_in: bool = field(default=False)
    has_zero_length_fade_out: bool = field(default=False)
    is_reversed: bool = field(default=False) # New field
    playrate: float = field(default=1.0) # New field (or ensure existing is used)
    # pitch_details: Optional[Any] # If pitch envelope data is parsed

# PluginAnalysisResult might need fields for more detailed role or parameter warnings.
```

### Frontend Integration Points

-   The `MasteringReportPage.tsx` will be the primary page for displaying these
    enhanced results.
-   New React components will be needed as outlined (e.g., `MasteringDashboard`,
    `PluginChainVisualizer`).
-   The `sessionStore.ts` (Zustand) might need to accommodate new parts of the
    analysis data if they need to be globally accessible or trigger reactive
    updates.

This plan provides a roadmap for significantly enhancing the "Ready for
Mastering" report. The next step would be to start implementing Phase 1,
beginning with the foundational data extraction and model updates.
