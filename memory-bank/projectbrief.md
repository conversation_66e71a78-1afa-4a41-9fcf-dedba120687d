# Project Brief

_This document outlines the core requirements and goals of the project. It
serves as the foundational source of truth for the project's scope._

## Project Name

SessionView

## Overview

SessionView is a web-based application for analyzing, documenting, and exporting
configurations from Reaper DAW (`.rpp`) session files.

## Goals

-   Enable music producers, mixers, and engineers to gain insight into their
    projects.
-   Streamline hand-offs between collaborators.
-   Facilitate reuse of configuration elements like FX chains, track templates,
    and color settings.

## Scope

_(Based on MVP definition and planned features)_

-   **In Scope (MVP):**
    -   Upload and parse `.rpp` files.
    -   Visual track overview (name, type, FX, color, volume, pan, mute, solo,
        folder hierarchy).
    -   Plugin summary per track (list, types, categorization).
    -   Routing details visualization (track list and diagram).
    -   Color swatches per track.
    -   Automation lanes visualization.
    -   Export FX Chains (`.RfxChain`) - Placeholder logic initially.
    -   Export Track Templates (`.TrackTemplate`) - Placeholder logic initially.
    -   Export Markdown session summary.
    -   **"Ready for Mastering" Report:** Comprehensive audit of session for
        pre-mastering readiness (includes plugin checks, gain staging, hygiene,
        master bus analysis).
-   **Out of Scope (Post-MVP / Planned):**
    -   PDF session report export.
    -   Modified `.rpp` file export.
    -   Session preset templates.
    -   Track grouping suggestions.
    -   Multi-session comparison.
    -   Team collaboration features (comments, annotations).
    -   User accounts and cloud storage integration.
    -   Tempo/Time Signature Map visualization.
    -   Region/Marker list export/visualization.
    -   MIDI track analysis.
    -   Plugin database.
    -   User-defined color palettes.
    -   Customizable issue detection rules (though rules will be in
        `.clinerules`).

## Key Requirements

_(Core features planned for the application)_

-   Upload and parse `.rpp` files.
-   Visual track overview (type, FX, automation, color, volume, pan, mute, solo,
    folder hierarchy).
-   Plugin/FX summary and usage per track, with categorization.
-   Track color mapping and editing.
-   Track routing visualization (`TrackRoutingDiagram`).
-   Automation lane visualization (`AutomationLaneDisplay`).
-   Export options:
    -   FX chains (`.RfxChain` files).
    -   Track templates (`.TrackTemplate` files).
    -   Markdown session summary.
    -   PDF session report (planned).
    -   Modified `.rpp` file with applied color/Fx changes (planned, high risk).
-   Issue detection:
    -   Missing plugins.
    -   Clipping risks (basic heuristics).
    -   Unused tracks.
    -   Overlapping plugin usage.
    -   **"Ready for Mastering" Audit:** Comprehensive checks including plugin
        blacklists, gain staging, session hygiene, file consistency, and master
        bus chain analysis.

## Stakeholders

-   Music Producers
-   Mixing Engineers
-   Mastering Engineers
-   Collaborators involved in music projects using Reaper.
