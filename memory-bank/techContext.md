# Technical Context

_This document details the technologies used in the project, setup instructions
for the development environment, technical constraints, and key dependencies._

## Technologies Used

-   **Frontend:**
    -   React.js (with TypeScript)
    -   <PERSON><PERSON><PERSON> (State Management)
    -   Tailwind CSS (Styling)
    -   Framer Motion (Animations)
    -   Heroicons/Lucide-react (Icons)
    -   Axios / Fetch API (Backend Communication)
    -   Vite (Build Tool)
    -   (Potential: `react-window` or `tanstack-table` for virtualization)
-   **Backend:**
    -   Python (3.11+)
    -   FastAPI (Web Framework)
    -   Uvicorn (ASGI Server)
    -   `rpp` library by Perlence (or fork) (RPP Parsing)
    -   Aiofiles (Async File Handling)
    -   Python-dotenv (Environment Variables)
    -   Markdown / Mistune (Markdown Generation)
    -   **Plugin Data**: Custom in-code database for plugin definitions (`backend/parsing/engines/plugin_data/`).
    -   (Planned: PDF Lib - `WeasyPrint`, `reportlab`, or `pdfkit`)
-   **Database:**
    -   None currently for MVP.
    -   (Planned: SQLite for presets, Redis for caching/background tasks)
-   **Testing:**
    -   (Recommended: `pytest` for backend, standard React testing tools for
        frontend)

## Development Environment Setup

_(Instructions need to be added based on project READMEs or standard practices
for Python/FastAPI and React/Vite projects. Generally involves cloning, setting
up Python virtual environment, installing backend dependencies
(`pip install -r requirements.txt` - TBC), installing frontend dependencies
(`npm install` in `frontend/`), and running dev servers.)_

## Technical Constraints

-   **RPP Parsing Speed:** Potential performance bottleneck for very large
    `.rpp` files using the Python parser. May require optimization or exploring
    Rust integration post-MVP.
-   **RPP Library Limitations:** The base `rpp` library might not parse all
    required data or handle edge cases correctly, necessitating forking and
    extending. Our custom plugin data structure helps abstract some of these complexities for plugin identification.
-   **Browser Compatibility:** Primarily targeting modern desktop browsers.
    Specific legacy browser support is not an explicit MVP requirement.
-   **Security:** File uploads require careful validation (type, size) and
    sanitization to prevent vulnerabilities. Modifying `.rpp` files (planned)
    carries inherent risks if not done carefully.
-   **Accessibility:** Target WCAG 2.1 AA compliance.

## Key Dependencies

-   **Frontend:**
    -   `react`, `react-dom`
    -   `zustand`
    -   `tailwindcss`
    -   `framer-motion`
    -   `lucide-react` / `@heroicons/react`
    -   `axios` (or Fetch)
-   **Backend:**
    -   `fastapi`
    -   `uvicorn`
    -   `rpp` (Perlence library or fork) - **CRITICAL**
    -   `aiofiles`
    -   `python-dotenv`
    -   `markdown` / `mistune`
