# System Patterns

_This document outlines the key technical decisions, design patterns in use,
component relationships, and system architecture of SessionView._

## Architecture Overview

SessionView follows a **modular, layered architecture** with clear separation of
concerns:

```
Frontend (React/TypeScript)
    ↓ HTTP API
Backend (FastAPI/Python)
    ↓ Parsing Layer
RPP Analysis Engine
    ↓ Data Layer
RPP File Structure
```

## Backend Architecture (Post-Refactoring - June 2025)

### Core Infrastructure Pattern

The backend uses a **unified infrastructure pattern** with dependency injection
through `ParsingContext`:

```
backend/parsing/
├── infrastructure/          # Core framework components
│   ├── parsing_context.py   # Central configuration & shared state
│   ├── base_extractor.py    # Abstract base for all extractors
│   ├── rpp_tree_cache.py    # Performance caching layer
│   ├── exceptions.py        # Structured error hierarchy
│   └── validation_utils.py  # Common validation patterns
├── engines/                 # Specialized analysis engines
│   ├── mastering_analysis_engine.py   # Main "Ready for Mastering" analysis
│   ├── plugin_analysis_engine.py      # Unified plugin analysis
│   ├── plugin_registry.py             # Plugin categorization
│   ├── plugin_blacklist_rules.py      # Genre-aware rules
│   ├── fuzzy_plugin_matcher.py        # Fuzzy matching system (NEW 2025-06-28)
│   └── plugin_data/                   # In-code database for plugin definitions
│       ├── __init__.py                # Unifies plugin data
│       ├── limiters.py                # Limiter plugin definitions (dict format)
│       ├── compressors.py             # Compressor plugin definitions (dict format)
│       ├── eqs.py                     # EQ plugin definitions (dict format)
│       ├── delays.py                  # Delay plugin definitions (dict format)
│       ├── reverbs.py                 # Reverb plugin definitions (dict format)
│       ├── saturation.py              # Saturation plugin definitions (dict format)
│       ├── clippers.py                # Clipper plugin definitions (dict format)
│       ├── metering.py                # Metering plugin definitions (dict format)
│       ├── referencing.py             # Reference plugin definitions (dict format)
│       ├── spectral.py                # Spectral plugin definitions (dict format)
│       ├── stereo_imaging.py          # Stereo imaging plugin definitions (dict format)
│       └── mastering.py               # Mastering plugin definitions (dict format)
├── extractors/              # Data extraction modules
│   ├── metadata_extractor.py
│   ├── track_extractor.py
│   ├── item_extractor.py
│   ├── master_track_extractor.py
│   ├── routing_extractor.py
│   ├── track_automation_extractor.py
│   ├── session_hygiene_extractor.py
│   ├── file_reference_extractor.py
│   └── gain_analysis.py
├── models.py               # Data models with consistent serialization
├── core.py                # Main orchestration logic
└── genre_rules.py         # Genre-specific analysis rules
```

### Key Design Patterns

#### 1. **Dependency Injection via ParsingContext**

All extractors receive a `ParsingContext` that provides:

-   RPP project tree
-   Caching layer (`RPPTreeCache`)
-   Genre-specific rules
-   Error collection
-   Shared configuration

```python
class ParsingContext:
    def __init__(self, project_tree: Element, file_path: str, genre: str = "general"):
        self.project_tree = project_tree
        self.cache = RPPTreeCache(project_tree)
        self.genre_rules = get_rules_for_genre(genre)
        self.warnings: List[str] = []
        self.errors: List[RPPParsingError] = []
```

#### 2. **Abstract Base Extractor Pattern**

All extractors inherit from `BaseExtractor` providing:

-   Consistent error handling
-   Common utility methods
-   Structured data access patterns

```python
class BaseExtractor(ABC):
    def __init__(self, context: ParsingContext):
        self.context = context

    @abstractmethod
    def extract(self) -> Any:
        pass

    def _find_element(self, parent: Element, tag_name: str, required: bool = False):
        # Consistent element finding with error handling

    def _get_typed_value_from_list(self, data_list, index: int, value_type: Type[T]):
        # Safe type conversion with error collection
```

#### 3. **Engine Pattern for Complex Analysis**

The `PluginAnalysisEngine` consolidates all plugin-related analysis:

-   Plugin info extraction with fuzzy matching via `FuzzyPluginMatcher`
-   Categorization via `PluginRegistry` (which now uses unified `plugin_data` modules)
-   Blacklist checking via `PluginBlacklistRules` with extended categories
-   Parameter decoding via `PluginParameterDecoder`
-   Automation analysis

This replaces the previous scattered approach across multiple extractors and
leverages the new structured plugin data with robust fuzzy matching capabilities.

#### 4. **Caching Layer Pattern**

`RPPTreeCache` provides performance optimization:

-   Single tree traversal for common operations
-   Pre-computed mappings (GUID → Track Name)
-   Lazy initialization of expensive operations

```python
class RPPTreeCache:
    def __init__(self, project_tree: Element):
        self._project_tree = project_tree
        self._tracks: List[Element] = []
        self._track_guid_map: Dict[str, str] = {}
        self._initialized = False

    @property
    def tracks(self) -> List[Element]:
        self._initialize_caches()
        return self._tracks
```

#### 5. **Structured Error Handling**

Custom exception hierarchy with context preservation:

```python
class RPPParsingError(Exception):
    """Base class for all RPP parsing errors."""

class MissingElementError(RPPParsingError):
    """Raised when a required RPP element is not found."""

class InvalidDataError(RPPParsingError):
    """Raised when RPP data is malformed or unexpected."""
```

## Data Flow Architecture

### 1. **Request Processing Flow**

```
HTTP Upload Request
    ↓
FastAPI Endpoint (/api/v1/upload)
    ↓
core.parse_rpp_content()
    ↓
ParsingContext Creation
    ↓
Extractor Orchestration
    ↓
Model Population
    ↓
JSON Response
```

### 2. **Extractor Orchestration Pattern**

The `core.py` module orchestrates extraction in a specific order:

1. **Metadata Extraction** - Basic project info
2. **Track Structure** - Track hierarchy and basic properties
3. **Plugin Analysis** - Via unified `PluginAnalysisEngine`
4. **Content Extraction** - Items, automation, routing
5. **Analysis Phases** - Gain staging, hygiene, file references

### 3. **Genre-Aware Analysis Pattern**

Analysis modules use genre-specific rules loaded via `ParsingContext`:

```python
# In ParsingContext
self.genre_rules = get_rules_for_genre(genre, strict_mode)

# In analysis modules
max_gain_threshold = self.context.genre_rules.get("max_gain_threshold", 6.0)
```

## Component Relationships

### Frontend Architecture

```
App.tsx (Router)
├── DashboardPage (File Upload)
├── TracksPage (Track Overview)
├── PluginsPage (Plugin Analysis)
├── MasteringReportPage (Analysis Results)
└── ExportPage (Export Options)
```

### State Management

-   **Zustand Store** (`sessionStore.ts`) for global session state
-   **Component-level state** for UI interactions
-   **API integration** via fetch with error handling

### Key Frontend Components

-   `FileUpload` - Handles RPP file uploads
-   `TrackList` - Displays track hierarchy with folder support
-   `PluginAnalysisSection` - Shows plugin analysis results
-   `AutomationLaneDisplay` - Visualizes automation data
-   `TrackRoutingDiagram` - Displays routing relationships
-   `GenreSelectionPanel` - Genre selection for analysis
-   `ui/Button` - Standard button component
-   `ui/Select` - Standard select component
-   `ui/Checkbox` - Standard checkbox component

## Performance Patterns

### 1. **Caching Strategy**

-   **RPP Tree Caching** - Single traversal, multiple access
-   **Computed Property Caching** - Expensive calculations cached
-   **Lazy Loading** - Initialize only when needed

### 2. **Memory Management**

-   **Structured Data Models** - Consistent serialization patterns
-   **Error Collection** - Centralized error handling without exceptions
-   **Resource Cleanup** - Proper cleanup of large data structures

### 3. **API Optimization**

-   **Single Upload Endpoint** - Complete analysis in one request
-   **Structured Responses** - Consistent JSON format
-   **Error Boundaries** - Graceful degradation on parsing errors

## Security Patterns

### 1. **File Upload Security**

-   **File Type Validation** - Only `.rpp` files accepted
-   **Size Limits** - Reasonable file size restrictions
-   **Content Validation** - RPP format verification

### 2. **Error Information**

-   **Sanitized Error Messages** - No sensitive path information
-   **Structured Logging** - Proper error tracking
-   **Graceful Degradation** - Partial results on non-critical errors

## Integration Patterns

### 1. **RPP Library Integration**

-   **Abstraction Layer** - `BaseExtractor` abstracts RPP library specifics
-   **Error Translation** - RPP library errors → structured exceptions
-   **Compatibility Handling** - Support for different RPP versions

### 2. **Frontend-Backend Integration**

-   **RESTful API** - Standard HTTP patterns
-   **JSON Serialization** - Consistent data format
-   **Error Propagation** - Structured error responses

## Testing Patterns

### 1. **Unit Testing**

-   **Extractor Testing** - Individual component testing
-   **Mock Context** - `ParsingContext` mocking for isolation
-   **Fixture-Based** - Real RPP files for integration testing

### 2. **Integration Testing**

-   **End-to-End Parsing** - Complete parsing flow testing
-   **API Testing** - HTTP endpoint testing
-   **Error Scenario Testing** - Malformed input handling

## Deployment Patterns

### 1. **Development Setup**

-   **Frontend**: Vite dev server with proxy to backend
-   **Backend**: FastAPI with auto-reload
-   **Local Network**: LAN access support for testing

### 2. **Production Considerations**

-   **Static File Serving** - Frontend build artifacts
-   **API Gateway** - Backend service exposure
-   **Error Monitoring** - Structured logging and monitoring

## Fuzzy Plugin Matching Architecture (NEW 2025-06-28)

### 1. **FuzzyPluginMatcher Pattern**

The fuzzy matching system addresses real-world plugin name variations in REAPER:

```python
class FuzzyPluginMatcher:
    def __init__(self, plugin_registry: PluginRegistry):
        self.plugin_registry = plugin_registry
        self._build_search_indices()
    
    def find_plugin_match(self, plugin_name: str, vendor_hint: str = None) -> Optional[PluginMatch]:
        # Smart name normalization and token-based matching
        normalized_name = self._normalize_plugin_name(plugin_name)
        tokens = self._extract_tokens(normalized_name)
        
        # Pre-filtering with token overlap
        candidates = self._get_candidates_by_tokens(tokens)
        
        # Weighted similarity scoring
        best_match = self._find_best_match(normalized_name, candidates, vendor_hint)
        return best_match if best_match.confidence >= self.min_confidence else None
```

**Key Features:**
- **Smart Normalization**: Handles "VST3: Pro-Q 4 (FabFilter)" → "FabFilter Pro-Q 4"
- **Token Extraction**: Extracts meaningful words while filtering noise (VST, AU, etc.)
- **Vendor Awareness**: Uses vendor hints for improved matching accuracy
- **Performance Optimization**: Pre-built search indices with token-based pre-filtering
- **Confidence Scoring**: Weighted combination of sequence and token similarity

### 2. **Enhanced Plugin Analysis Engine**

The plugin analysis engine now integrates fuzzy matching:

```python
def _extract_vendor_hint(self, plugin_name: str) -> str:
    # Handles complex patterns like "(Universal Audio (UADx))"
    vendor_patterns = [
        r'\(([^()]+(?:\([^)]*\))?[^()]*)\)$',  # Nested parentheses
        r'\(([^)]+)\)',                        # Simple parentheses
        r'^([A-Za-z]+):'                       # Prefix patterns
    ]
```

### 3. **Plugin Database Architecture**

All 12 plugin categories use unified dictionary format:

```python
# Standardized structure across all categories
PLUGIN_CATEGORY = {
    "plugin_key": {
        "name": "Display Name",
        "vendor": "Vendor Name", 
        "category": "category_name",
        "aliases": ["Alternative", "Names"],
        "parameters": {...}
    }
}
```

**Benefits:**
- Fast O(1) lookups vs O(n) list searches
- Consistent data structure across all categories
- Efficient memory usage and serialization
- Easy extensibility for new plugin entries

## Evolution and Extensibility

### 1. **Adding New Analysis**

1. Create new extractor inheriting from `BaseExtractor`
2. Add to orchestration in `core.py`
3. Update models if needed
4. Add frontend components for display

### 2. **Supporting New RPP Features**

1. Extend `RPPTreeCache` if needed
2. Add new extraction methods to relevant extractors
3. Update models and serialization
4. Add frontend display components

### 3. **Adding New Plugin Categories**

1. Create new plugin data file in `plugin_data/` using dictionary format
2. Add category to `__init__.py` imports
3. Update `PluginRegistry` to include new category
4. Add corresponding blacklist rules if needed

### 4. **Performance Optimization**

1. Add caching to `RPPTreeCache`
2. Implement lazy loading in extractors
3. Optimize frontend rendering for large datasets
4. Consider background processing for heavy analysis
5. Enhance fuzzy matching search indices for even faster lookups

The architecture is designed for maintainability, extensibility, and performance
while providing a solid foundation for the comprehensive RPP analysis features. The fuzzy matching system ensures robust plugin detection that handles real-world naming variations seamlessly.
