#!/usr/bin/env python3
"""
Automated conversion script to convert plugin data files from list format to dictionary format.

This script converts plugin category files like:
    CATEGORY = [PluginInfo(...), ...]
to:
    CATEGORY = {"plugin_name": PluginInfo(...), ...}
"""

import os
import re
import shutil
from typing import List, Tuple
import ast

def backup_file(file_path: str) -> str:
    """Create a backup of the original file"""
    backup_path = f"{file_path}.backup"
    shutil.copy2(file_path, backup_path)
    print(f"Created backup: {backup_path}")
    return backup_path

def parse_plugin_entries(content: str) -> List[Tuple[str, str]]:
    """
    Parse PluginInfo entries from the file content.
    Returns list of (plugin_name, full_entry_text) tuples.
    """
    entries = []
    
    # More robust pattern to match PluginInfo with proper parentheses balancing
    pattern = r'PluginInfo\('
    
    # Find all PluginInfo starting positions
    matches = list(re.finditer(pattern, content))
    
    for i, match in enumerate(matches):
        start_pos = match.start()
        
        # Find the end of this PluginInfo by counting parentheses
        paren_count = 0
        pos = start_pos + len('PluginInfo')
        in_string = False
        string_char = None
        
        while pos < len(content):
            char = content[pos]
            
            # Handle string literals
            if char in ['"', "'"] and (pos == 0 or content[pos-1] != '\\'):
                if not in_string:
                    in_string = True
                    string_char = char
                elif char == string_char:
                    in_string = False
                    string_char = None
            
            # Count parentheses only when not in string
            if not in_string:
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                    if paren_count == 0:  # Found the end
                        end_pos = pos + 1
                        break
            
            pos += 1
        else:
            # If we reach here, parentheses weren't balanced - use next PluginInfo or end
            if i + 1 < len(matches):
                end_pos = matches[i + 1].start()
            else:
                end_pos = len(content)
        
        entry_text = content[start_pos:end_pos].rstrip(',\n ')
        
        # Extract the name parameter more robustly - handle apostrophes in names
        name_match = re.search(r'name\s*=\s*"([^"]*)"', entry_text, re.DOTALL)
        if not name_match:
            name_match = re.search(r"name\s*=\s*'([^']*)'", entry_text, re.DOTALL)
        if name_match:
            plugin_name = name_match.group(1)
            entries.append((plugin_name, entry_text))
        else:
            print(f"Warning: Could not extract name from entry: {entry_text[:100]}...")
    
    return entries

def convert_to_dict_format(content: str, category_name: str) -> str:
    """Convert the file content from list format to dict format"""
    
    # Parse plugin entries
    entries = parse_plugin_entries(content)
    
    if not entries:
        print(f"No PluginInfo entries found in {category_name}")
        return content
    
    print(f"Found {len(entries)} plugin entries in {category_name}")
    
    # Build the new dictionary format
    dict_entries = []
    for plugin_name, entry_text in entries:
        # Format as dictionary entry with proper indentation
        dict_entry = f'    "{plugin_name}": {entry_text},'
        dict_entries.append(dict_entry)
    
    # Replace the list format with dict format
    list_pattern = rf'{category_name}\s*=\s*\[(.*?)\]'
    dict_content = f'{category_name} = {{\n' + '\n'.join(dict_entries) + '\n}'
    
    new_content = re.sub(list_pattern, dict_content, content, flags=re.DOTALL)
    
    return new_content

def validate_conversion(original_content: str, new_content: str, category_name: str) -> bool:
    """Validate that the conversion preserved all plugins"""
    
    # Count PluginInfo entries in both versions
    original_count = len(re.findall(r'PluginInfo\(', original_content))
    new_count = len(re.findall(r'PluginInfo\(', new_content))
    
    if original_count != new_count:
        print(f"ERROR: Plugin count mismatch in {category_name}!")
        print(f"Original: {original_count}, New: {new_count}")
        return False
    
    # Check that it's now in dict format
    dict_marker = f'{category_name} = {{'
    if dict_marker not in new_content:
        print(f"ERROR: {category_name} not converted to dict format!")
        return False
    
    print(f"✅ Validation passed for {category_name}: {original_count} plugins preserved")
    return True

def convert_category_file(file_path: str) -> bool:
    """Convert a single plugin category file"""
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    # Extract category name from filename
    category_name = os.path.basename(file_path).replace('.py', '').upper()
    
    print(f"\n🔄 Converting {category_name}...")
    
    # Read original content
    with open(file_path, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # Check if already in dict format
    dict_marker = f'{category_name} = {{'
    if dict_marker in original_content:
        print(f"✅ {category_name} already in dict format, skipping")
        return True
    
    # Check if it's in list format
    if f'{category_name} = [' not in original_content:
        print(f"⚠️  {category_name} not in expected list format, skipping")
        return True
    
    # Create backup
    backup_path = backup_file(file_path)
    
    try:
        # Convert to dict format
        new_content = convert_to_dict_format(original_content, category_name)
        
        # Validate conversion
        if not validate_conversion(original_content, new_content, category_name):
            print(f"❌ Validation failed for {category_name}, restoring backup")
            shutil.copy2(backup_path, file_path)
            return False
        
        # Write converted content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ Successfully converted {category_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error converting {category_name}: {e}")
        # Restore backup
        shutil.copy2(backup_path, file_path)
        return False

def main():
    """Main conversion function"""
    
    # Plugin data directory
    plugin_data_dir = "backend/parsing/engines/plugin_data"
    
    if not os.path.exists(plugin_data_dir):
        print(f"Plugin data directory not found: {plugin_data_dir}")
        return
    
    # Files to convert (excluding __init__.py, base.py, patterns.py)
    category_files = [
        "compressors.py",
        "eqs.py", 
        "saturation.py",
        "mastering.py",
        "clippers.py",
        "metering.py",
        "referencing.py",
        "reverbs.py",
        "delays.py",
        "stereo_imaging.py",
        "spectral.py"
    ]
    
    print("🚀 Starting plugin data conversion...")
    print("=" * 50)
    
    successful_conversions = []
    failed_conversions = []
    
    for filename in category_files:
        file_path = os.path.join(plugin_data_dir, filename)
        if convert_category_file(file_path):
            successful_conversions.append(filename)
        else:
            failed_conversions.append(filename)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Conversion Summary:")
    print(f"✅ Successful: {len(successful_conversions)}")
    for f in successful_conversions:
        print(f"   - {f}")
    
    if failed_conversions:
        print(f"❌ Failed: {len(failed_conversions)}")
        for f in failed_conversions:
            print(f"   - {f}")
    else:
        print("🎉 All conversions successful!")
        print("\nNext steps:")
        print("1. Update __init__.py to use dict format")
        print("2. Test plugin detection")
        print("3. Remove backup files when satisfied")

if __name__ == "__main__":
    main()