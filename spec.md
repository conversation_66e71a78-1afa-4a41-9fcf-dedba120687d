# SessionView App Specification (v1.2 – Implementation-Aligned Update, April 2025)

## Overview

**SessionView** is a web-based application for analyzing, documenting, and
exporting configurations from Reaper DAW (`.rpp`) session files. It enables
music producers, mixers, and engineers to gain insight into their projects,
streamline hand-offs, and reuse configuration elements like FX chains, track
templates, and color settings.

---

## Progress Update (April 2025)

-   **Frontend**: Multi-page React app with Dashboard, Tracks, Plugins, and
    Export pages. Dark mode, responsive layout, and modular component structure
    are implemented.
-   **Backend**: FastAPI backend with modular extractor architecture for parsing
    RPP files. Core endpoints for upload, summary, FX chain export, track
    template export, and Markdown export are implemented.
-   **Features Implemented**:
    -   Upload and parse `.rpp` files
    -   Visual track overview (type, FX, automation, color, volume, pan, mute,
        solo)
    -   Plugin/FX summary and categorization
    -   Track color mapping and editing
    -   Track routing visualization
    -   Automation lane display
    -   Export FX chains and track templates (placeholder logic)
    -   Export Markdown session summary
    -   Error handling and user feedback
-   **Pending/Planned**:
    -   PDF export
    -   Modified `.rpp` export
    -   Advanced issue detection
    -   User authentication, collaboration, and cloud features

---

## Features

### Core Features

-   Upload and parse `.rpp` files
-   Visual track overview (type, FX, automation, color, volume, pan, mute, solo,
    folder hierarchy)
-   Plugin/FX summary and usage per track, with categorization
-   Track color mapping and editing
-   Track routing visualization (TrackRoutingDiagram)
-   Automation lane visualization (AutomationLaneDisplay)
-   Export options:
    -   FX chains (`.RfxChain` files)
    -   Track templates (`.TrackTemplate` files)
    -   Markdown session summary
    -   PDF session report (planned)
    -   Modified `.rpp` file with applied color/Fx changes (planned, high risk)
-   Issue detection (planned):
    -   Missing plugins
    -   Clipping risks (basic heuristics)
    -   Unused tracks
    -   Overlapping plugin usage

### Optional Features (Post-MVP)

-   Session preset templates
-   Track grouping suggestions
-   Multi-session comparison (including `.rpp` diff view)
-   Team collaboration (comments/annotations)
-   Account system for saved sessions (via OAuth)
-   Tempo/Time Signature Map visualization
-   Region/Marker list export/visualization
-   MIDI track analysis
-   Plugin database (linking to manufacturer sites, noting CPU usage)
-   User-defined color palettes
-   Customizable issue detection rules
-   Cloud storage integration (Dropbox, Google Drive)
-   Webhooks for notifications

---

## Frontend

### Tech Stack

-   **React.js** (TypeScript, Zustand for state management)
-   **Tailwind CSS**
-   **Framer Motion** (animations)
-   **Heroicons/Lucide-react** (icons)
-   **Axios** (or Fetch API for backend communication)

### UI Components

-   File Upload interface (drag-and-drop / file picker)
-   Track Summary Table (virtualized for large sessions)
    -   Expandable dropdown for routing details
-   Plugin & FX Panel (with categorization)
-   Export Panel/Modal with export options
-   Insights Panel with warnings/suggestions (planned)
-   Dark mode theme
-   Loading indicators and clear error message displays
-   TrackRoutingDiagram (routing visualization)
-   AutomationLaneDisplay (automation envelopes)

### Pages/Views

-   **Dashboard** – Upload and view session analysis (default landing page)
-   **Tracks** – Detailed track list, color, automation, routing
-   **Plugins** – Plugin/FX summary and categorization
-   **Export** – Export options for FX chains, templates, Markdown, etc.
-   **(Planned)**: Session Settings, multi-session comparison, etc.

### Accessibility

-   Target WCAG 2.1 AA compliance (keyboard navigation, screen reader support,
    contrast).

---

## Backend

### Tech Stack

-   **FastAPI** (Python 3.11+)
-   **Uvicorn** (ASGI server)
-   **Python `rpp` library (by Perlence)** – Used as a base, with a "Use, Test,
    Fork/Extend if Needed" strategy.
-   **PDF Export**: Planned – evaluate `WeasyPrint`, `reportlab`, or HTML-to-PDF
    via `pdfkit`.
-   **Markdown**: Python `markdown` or `mistune`.
-   **Aiofiles** (for async file handling)
-   **Python-dotenv** (for environment variables)

### Architecture

-   Modular extractor-based parsing:
    -   `track_extractor.py`: Track info, volume, pan, mute, solo, color, folder
        hierarchy
    -   `fx_extractor.py`: Plugin/FX extraction and categorization
    -   `automation_utils.py`, `track_automation_extractor.py`,
        `plugin_automation_extractor.py`: Automation lanes and envelopes
    -   `routing_extractor.py`: Track routing and connections
    -   `master_track_extractor.py`: Master track handling
    -   `metadata_extractor.py`: Project metadata
-   Central `parser_wrapper.py` orchestrates parsing and data aggregation.

### Key Endpoints (API Versioned: `/api/v1/...`)

| Method | Endpoint                | Description                                      |
| ------ | ----------------------- | ------------------------------------------------ |
| POST   | `/upload`               | Upload, parse `.rpp`, return session data JSON   |
| GET    | `/summary`              | Return parsed session summary (from last upload) |
| POST   | `/export/fxchain`       | Export FX chain for selected track(s)            |
| POST   | `/export/tracktemplate` | Export selected tracks as `.TrackTemplate`       |
| POST   | `/export/markdown`      | Generate session Markdown report                 |
| POST   | `/export/pdf`           | Generate PDF session report (planned)            |
| POST   | `/export/rpp`           | Export modified `.rpp` file (planned)            |

### Optional Services

-   **SQLite** (for preset storage, planned)
-   **Redis + Celery** (for background PDF/heavy processing, planned)
-   **GitHub OAuth** (if user accounts added)

---

## File Processing

### Supported Formats

-   `.rpp` (Reaper project files)

### Outputs

-   `.RfxChain`
-   `.TrackTemplate`
-   `.rpp` (modified - planned)
-   `.md` (Markdown summary)
-   `.pdf` (Session report - planned)
-   `JSON` (API responses)

---

## Minimum Viable Product (MVP) Spec

### Core Features for MVP

-   Upload `.rpp` file (with basic validation: type, size limits)
-   Parse and display using `rpp` library (fork/extend if needed):
    -   Track list (name, type, FX, color, volume, pan, mute, solo, folder
        hierarchy)
    -   Plugin summary per track (list of plugin names, types, categorization)
    -   Routing details (track-to-track sends/receives, shown in track list and
        routing diagram)
    -   Color swatches per track
    -   Automation lanes per track (visualized)
-   Export FX Chains (`.RfxChain`) – Placeholder logic
-   Export Track Templates (`.TrackTemplate`) – Placeholder logic
-   Export Markdown session summary

### MVP UI Scope

-   Multi-page layout (Dashboard, Tracks, Plugins, Export)
-   Responsive layout for desktop use
-   File upload at top, summary panels below
-   Dark mode styling
-   Basic loading/error feedback

---

## Development Considerations

### RPP Parsing Strategy

-   Use the Python `rpp` library (Perlence) as a base.
-   Parsing logic is modularized in dedicated backend extractor modules.
-   Test thoroughly with diverse `.rpp` files during MVP development.
-   If essential MVP data is missing or errors occur, fork the `rpp` library.
-   Use ReaTeam documentation to guide modifications/extensions to the forked
    library.
-   Update the backend to use the fork if necessary.

### Error Handling

-   Robust error handling for:
    -   File uploads (size, type validation)
    -   `.rpp` parsing failures (informative messages)
    -   Backend API errors (network/server)
    -   Frontend state management issues
-   Display clear, user-friendly error messages in the UI.

### Security

-   Validate uploaded file types and implement size limits.
-   Sanitize filenames and any user-derived input used in paths or commands.
-   Use HTTPS for deployment.
-   Consider rate limiting for API endpoints.
-   (Planned) If modifying `.rpp` files, ensure output is valid and doesn't
    introduce vulnerabilities.

### Testing Strategy

-   **Unit Tests:**
    -   Backend: Crucial for parser logic (especially if forked), API endpoint
        logic, utility functions.
    -   Frontend: For individual components, state management logic, utility
        functions.
-   **Integration Tests:**
    -   Test frontend interaction with backend API endpoints.
-   **End-to-End Tests (Optional but Recommended):**
    -   Simulate user flows (upload → view → export).
-   **Sample `.rpp` Files:** Maintain a diverse suite of `.rpp` files (different
    Reaper versions, features, sizes) for manual and automated testing.

### Performance

-   Monitor backend parsing time for large `.rpp` files. Consider Rust parser
    integration post-MVP if Python is too slow.
-   For large sessions, investigate frontend performance bottlenecks and
    consider UI virtualization techniques (e.g., `react-window`,
    `tanstack-table`) for tables/lists.
-   Consider background processing (Celery) for slow operations like PDF
    generation post-MVP.

---

## Dependencies (Summary)

### Frontend

-   `react`, `react-dom`
-   `tailwindcss`, `autoprefixer`, `postcss`
-   `framer-motion`
-   `lucide-react` or `@heroicons/react`
-   `axios` or similar
-   `zustand` (state management)
-   (Virtualization: `react-window` or similar – if needed)

### Backend

-   `fastapi`
-   `uvicorn[standard]`
-   `rpp` (Perlence) – potentially a fork
-   `python-dotenv`
-   `aiofiles`
-   `markdown` or `mistune`
-   (PDF Lib: `weasyprint`/`reportlab`/`pdfkit` – planned)
-   (Testing: `pytest` – recommended)

---

## Stretch Goals

-   User-authenticated preset libraries
-   Save/export color maps
-   Generate DAW-specific mix notes
-   GitHub/GitLab import/export for project storage
-   Collaborator comment threads
-   Deeper analysis features (MIDI, Tempo Maps, etc.)

---

## License

To be determined (likely MIT or GPL if open source).
