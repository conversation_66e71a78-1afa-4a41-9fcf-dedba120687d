#!/usr/bin/env python3
"""
Debug script to test API2500 plugin detection
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from parsing.engines.plugin_registry import PluginRegistry

def test_api2500_detection():
    registry = PluginRegistry()
    
    # Test various possible API2500 plugin names
    test_names = [
        "API 2500",
        "API 2500 Bus Compressor", 
        "UADx API 2500 Bus Compressor",
        "UAD API 2500 Bus Compressor",
        "VST: API 2500 Bus Compressor",
        "AU: API 2500 Bus Compressor (Universal Audio)"
    ]
    
    print("Testing API2500 plugin detection:")
    print("=" * 50)
    print(f"Total plugins in database: {len(registry.plugin_database)}")
    
    for name in test_names:
        print(f"\nTesting: '{name}'")
        plugin_info = registry.get_plugin_info(name)
        print(f"  Category: {plugin_info.get('category', 'Not Found')}")
        print(f"  Confidence: {plugin_info.get('confidence', 0.0)}")
        print(f"  Vendor: {plugin_info.get('vendor', 'Not Found')}")
        if plugin_info.get('detected_via'):
            print(f"  Detected via: {plugin_info['detected_via']}")
        
        # Test cleaned name separately
        clean_name = registry._clean_plugin_name(name)
        if clean_name != name:
            print(f"  Clean name: '{clean_name}'")

if __name__ == "__main__":
    test_api2500_detection()