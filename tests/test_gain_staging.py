import sys
import os

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from backend.parsing.core import parse_rpp_file
from backend.parsing.genre_rules import get_rules_for_genre


def test_gain_staging_analysis():
    """Test that gain staging analysis is working correctly."""
    try:
        # Parse the test file with genre rules
        result = parse_rpp_file("tests/fixtures/parameter_test/parameter_test.RPP")

        # Verify that items are extracted
        assert (
            len(result["tracks"]) > 1
        ), "Should have at least 2 tracks (master + 1 regular)"
        track = result["tracks"][1]  # First regular track
        assert "items" in track, "Track should have items field"
        assert len(track["items"]) > 0, "Track should have at least one item"

        # Verify item structure
        item = track["items"][0]
        assert "name" in item, "Item should have name"
        assert "volume" in item, "Item should have volume"
        assert "takes" in item, "Item should have takes"
        assert len(item["takes"]) > 0, "Item should have at least one take"

        # Verify take structure
        take = item["takes"][0]
        assert "source_file" in take, "Take should have source_file"
        assert "volume_db" in take, "Take should have volume_db from gain analysis"
        assert "gain_staging_flag" in take, "Take should have gain_staging_flag"

        # Test with different genres
        genres = ["general", "electronic", "acoustic"]
        for genre in genres:
            genre_rules = get_rules_for_genre(genre)
            assert genre_rules is not None, f"Should get rules for {genre}"
            assert (
                "gain_staging_item_max_db" in genre_rules
            ), f"Should have gain thresholds for {genre}"

        print("All gain staging tests passed!")

    except Exception as e:
        print(f"Error: {e}")
        assert False, f"Gain staging test failed: {e}"


if __name__ == "__main__":
    test_gain_staging_analysis()
