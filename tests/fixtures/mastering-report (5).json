{"overall_health_score": 0, "genre": "general", "strict_mode": false, "summary_metrics": {"critical_issues_count": 11, "warning_issues_count": 4, "info_issues_count": 33, "passed_checks_count": 2}, "issue_groups": [{"rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "title": "7 plugins with plugin master bus review", "description": "Affects tracks: Master", "count": 7, "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}, {"type": "plugin", "guid": null, "name": "AU: elysia alpha master (Plugin Alliance)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}, {"type": "plugin", "guid": null, "name": "VST3: UADx SSL G Bus Compressor (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}, {"type": "plugin", "guid": null, "name": "VST3: Black Box Analog Design HG-2 (Plugin Alliance)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}, {"type": "plugin", "guid": null, "name": "AU: Ozone 9 Imager (iZotope)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}, {"type": "plugin", "guid": null, "name": "VST3: Chandler Limited Curve Bender (Softube)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}, {"type": "plugin", "guid": null, "name": "VST3: Pro-Q 4 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "sample_issue": {"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}}, {"rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "title": "10 issues: missing media file", "description": "Affects tracks: Guitar AG Vs, Bass and 6 more", "count": 10, "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{306ED79D-A8C4-A246-B56E-6ACC26FEF836}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG Vs", "item_name": "Mid24 - No1 Vox Comp-002.wav", "file_path": "Media/Mid24 - No1 Vox Comp-002.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F46B549D-6169-C149-B41B-BE741F9BB77F}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG Vs", "item_name": "Mid24 - No1 Vox Comp-002.wav", "file_path": "Media/Mid24 - No1 Vox Comp-002.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{C48F8A4D-CC29-D943-ADCC-6410C9DDF582}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG Vs", "item_name": "Mid24 - No1 Vox Comp-002.wav", "file_path": "Media/Mid24 - No1 Vox Comp-002.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{45C2870C-673F-1E4E-B614-43F0247B99EF}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG Brg", "item_name": "Mid24 - No1 Vox Comp-003.wav", "file_path": "Media/Mid24 - No1 Vox Comp-003.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{4DFF50C2-97FC-F140-8094-39BA925BE378}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "file_path": "Media/Mid24 - No1 Vox Comp-004.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{D0DAE426-6EDB-0C43-8EA5-7CC50FFA2C6A}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "file_path": "Media/Mid24 - No1 Vox Comp-004.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{8DD79D73-0384-904E-9BFF-93EEACDC303F}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "file_path": "Media/Mid24 - No1 Vox Comp-004.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON><PERSON><PERSON>-{7D91FF39-01CC-9E45-9AE4-A6B115C792C3}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "file_path": "Media/Mid24 - No1 Vox Comp-004.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{CBBC7F9D-1A0E-D94F-A3DA-2497B83A0C93}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "file_path": "Media/Mid24 - No1 Vox Comp-004.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{8D6AC606-73F1-8543-8FC9-0F7D45A95FA4}-0}", "name": "Take 1", "details": {"track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "file_path": "Media/Mid24 - No1 Vox Comp-004.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{D3FE22ED-E7A6-D94C-9CEB-94B8BE3D58BA}-0}", "name": "Take 1", "details": {"track_name": "Guitar AM RL", "item_name": "Mid24 - No1 Vox Comp-005.wav", "file_path": "Media/Mid24 - No1 Vox Comp-005.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON><PERSON><PERSON>-{87ADB1DA-0137-ED4C-8B81-60CFEA2B04F6}-0}", "name": "Take 1", "details": {"track_name": "Guitar AM RL", "item_name": "Mid24 - No1 Vox Comp-005.wav", "file_path": "Media/Mid24 - No1 Vox Comp-005.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{EB0F3425-8D9F-8A41-8979-B35704099B6E}-0}", "name": "Take 1", "details": {"track_name": "Guitar AM RR", "item_name": "Mid24 - No1 Vox Comp-006.wav", "file_path": "Media/Mid24 - No1 Vox Comp-006.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{56A2C685-AD0E-534D-97C0-426C2FCD20D7}-0}", "name": "Take 1", "details": {"track_name": "Guitar AM Ld", "item_name": "Mid24 - No1 Vox Comp-007.wav", "file_path": "Media/Mid24 - No1 Vox Comp-007.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{5A637FC6-13CC-654A-9849-7DAF7E5E5CC0}-0}", "name": "Take 1", "details": {"track_name": "Guitar AM Ld", "item_name": "Mid24 - No1 Vox Comp-007.wav", "file_path": "Media/Mid24 - No1 Vox Comp-007.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{C100A396-6FB5-8A4F-81C9-F5EC539AB51E}-0}", "name": "Take 1", "details": {"track_name": "Guitar AM Ld", "item_name": "Mid24 - No1 Vox Comp-007.wav", "file_path": "Media/Mid24 - No1 Vox Comp-007.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{FAA7FD72-B246-3341-AEE3-66CAF78E2B1A}-0}", "name": "Take 1", "details": {"track_name": "Guitar AM Ld", "item_name": "Mid24 - No1 Vox Comp-007.wav", "file_path": "Media/Mid24 - No1 Vox Comp-007.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F7E1032D-85A8-6E41-B90B-7C7A1B67A8EA}-0}", "name": "Take 1", "details": {"track_name": "<PERSON><PERSON>", "item_name": " - stem", "file_path": "Media/Mid24 No1 Mix_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{1971DA12-1621-4C4C-87DA-D8F846C72BC3}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{06E5F5CB-5FB0-CB4B-8FBF-7D23EFAF348D}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{388144AD-44FC-1C40-B15A-A793AC39DCDA}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{A0B4495E-2194-7446-A3BD-13BDFBF426F8}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{189CBB19-D3F8-ED41-9BED-78BE20DE62E0}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{965C934F-88FF-1741-98DD-FD4F36DAF0FD}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{D14BEB3C-9289-344B-8B44-12ED1B0778D7}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{A49D63F0-25B9-E141-AC57-C0B6CB88B402}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{1666A564-8F69-BC49-AEE8-698821A8DD82}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{5C6DB6E4-70C2-AC4E-A8BF-989BC1F8C7AB}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{B677A304-ECEB-634C-A2B5-CAC5437C4CCA}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{5C63F7C4-96FD-9E4B-9DC2-ED6609D2E220}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{671592BA-8507-614D-9626-B21B393842A1}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{978C92FF-987A-5245-B709-FBA11EB60403}-0}", "name": "Take 1", "details": {"track_name": "Vocal BV", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2A1D036D-C809-B44A-A490-3669E3C94F79}-0}", "name": "Take 1", "details": {"track_name": "Vocal BV", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0EC97DE1-BF60-CE47-B754-93442AC27522}-0}", "name": "Take 1", "details": {"track_name": "Vocal BV", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{9F944DBD-E047-6B44-857D-00234D576DA3}-0}", "name": "Take 1", "details": {"track_name": "Vocal BV", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{38A32FC3-480F-B84A-A524-E3C95796BC41}-0}", "name": "Take 1", "details": {"track_name": "Vocal Bridge", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{B5C320C2-4D58-E940-9BD7-200627BB0CD6}-0}", "name": "Take 1", "details": {"track_name": "Vocal Bridge", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{5A556023-A1FA-0746-A6BA-B18825CAAA45}-0}", "name": "Take 1", "details": {"track_name": "Vocal Bridge", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{8FF74371-3518-1C48-859C-5AD901EA5B28}-0}", "name": "Take 1", "details": {"track_name": "Vocal Bridge", "item_name": "Mid24 - No1 Vox Comp-008.wav", "file_path": "Media/Mid24 - No1 Vox Comp-008.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{621BF80E-A609-AE46-92A7-E6989A1D1FAE}-0}", "name": "Take 1", "details": {"track_name": "Vocal Lead", "item_name": "Mid24 - 1_stems_vox7-007 - stem", "file_path": "Media/Mid24 No1 Mix_stems_Mid24 - 1_stems_vox7-007.wav"}}], "sample_issue": {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}}, {"rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "title": "11 tracks with hygiene not in folder", "description": "Affects tracks: <PERSON> Pad, <PERSON> Riser, Bass, Drums and 5 more", "count": 11, "is_mastering_critical": false, "affected_elements": [{"type": "track", "guid": "{88652663-91E9-E54F-B601-480FE745A4BE}", "name": "Drums"}, {"type": "track", "guid": "{B4DF1191-5A86-3245-B206-3E934B39726F}", "name": "Bass"}, {"type": "track", "guid": "{0286D29D-D960-A34F-9AF1-511013391EB7}", "name": "FX Pad"}, {"type": "track", "guid": "{865B73A0-BC99-A44F-A591-B1E8C986600E}", "name": "FX Riser"}, {"type": "track", "guid": "{1BE0C274-5278-D84E-BCEA-99B3A17C2DF2}", "name": "FX Riser"}, {"type": "track", "guid": "{1EA392B5-1C34-6449-9260-CA1AF0541282}", "name": "Vocal Lead"}, {"type": "track", "guid": "{4E7FE7FC-0527-8B4E-A37C-964B171944ED}", "name": "Vocal BV"}, {"type": "track", "guid": "{9B1809BD-9F0E-9A45-8EEF-EC3921F5945A}", "name": "Vocal Bridge"}, {"type": "track", "guid": "{EDE251B7-B545-6345-B64B-F371056FDA8D}", "name": "Room Verb"}, {"type": "track", "guid": "{4D174DEB-CF59-814D-8E15-B2D591EF936B}", "name": "Git Ld Delay"}, {"type": "track", "guid": "{F809F7FA-4433-5646-A932-7A132971935C}", "name": "Vx Lng Delay"}], "sample_issue": {"id": "HYGIENE_NOT_IN_FOLDER-22", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Drums' is not part of any folder structure.", "is_mastering_critical": false, "affected_elements": [{"type": "track", "guid": "{88652663-91E9-E54F-B601-480FE745A4BE}", "name": "Drums"}], "recommendation": "Consider organizing track 'Drums' into a folder if appropriate for the project structure."}}, {"rule_id": "HYGIENE_DUPLICATE_TRACK_NAME", "category": "Session Hygiene", "severity": "warning", "title": "2 tracks with hygiene duplicate track name", "description": "Affects tracks: FX Riser", "count": 2, "is_mastering_critical": false, "affected_elements": [{"type": "track", "guid": "{865B73A0-BC99-A44F-A591-B1E8C986600E}", "name": "FX Riser"}, {"type": "track", "guid": "{1BE0C274-5278-D84E-BCEA-99B3A17C2DF2}", "name": "FX Riser"}], "sample_issue": {"id": "HYGIENE_DUPLICATE_TRACK_NAME-26", "rule_id": "HYGIENE_DUPLICATE_TRACK_NAME", "category": "Session Hygiene", "severity": "warning", "message": "Track name 'FX Riser' is duplicated in the project.", "is_mastering_critical": false, "affected_elements": [{"type": "track", "guid": "{865B73A0-BC99-A44F-A591-B1E8C986600E}", "name": "FX Riser"}], "recommendation": "Rename track 'FX Riser' to ensure all track names are unique for clarity."}}, {"rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "title": "8 items with item abrupt start", "description": "Affects tracks: Guitar AG Brg, Bass, Guitar AG Vs, Guitar AM RL, Guitar AM RR and 3 more", "count": 8, "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}, {"type": "item", "guid": "{306ED79D-A8C4-A246-B56E-6ACC26FEF836}", "name": "Mid24 - No1 Vox Comp-002.wav", "details": {"track_name": "Guitar AG Vs"}}, {"type": "item", "guid": "{45C2870C-673F-1E4E-B614-43F0247B99EF}", "name": "Mid24 - No1 Vox Comp-003.wav", "details": {"track_name": "Guitar AG Brg"}}, {"type": "item", "guid": "{D3FE22ED-E7A6-D94C-9CEB-94B8BE3D58BA}", "name": "Mid24 - No1 Vox Comp-005.wav", "details": {"track_name": "Guitar AM RL"}}, {"type": "item", "guid": "{EB0F3425-8D9F-8A41-8979-B35704099B6E}", "name": "Mid24 - No1 Vox Comp-006.wav", "details": {"track_name": "Guitar AM RR"}}, {"type": "item", "guid": "{56A2C685-AD0E-534D-97C0-426C2FCD20D7}", "name": "Mid24 - No1 Vox Comp-007.wav", "details": {"track_name": "Guitar AM Ld"}}, {"type": "item", "guid": "{F7E1032D-85A8-6E41-B90B-7C7A1B67A8EA}", "name": " - stem", "details": {"track_name": "<PERSON><PERSON>"}}, {"type": "item", "guid": "{1971DA12-1621-4C4C-87DA-D8F846C72BC3}", "name": "Mid24 - No1 Vox Comp-008.wav", "details": {"track_name": "Vocal Lead"}}], "sample_issue": {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}}, {"rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "title": "2 items with item abrupt end", "description": "Affects tracks: <PERSON><PERSON>, <PERSON>", "count": 2, "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}, {"type": "item", "guid": "{F7E1032D-85A8-6E41-B90B-7C7A1B67A8EA}", "name": " - stem", "details": {"track_name": "<PERSON><PERSON>"}}], "sample_issue": {"id": "ITEM_ABRUPT_END-36", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}}], "ungrouped_issues": [{"id": "PLUGIN_BYPASSED_BLACKLISTED_MASTER-8", "rule_id": "PLUGIN_BYPASSED_BLACKLISTED_MASTER", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Bypassed plugin 'VST3: Pro-L 2 (FabFilter)' on the Master bus would be blacklisted if active.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "If 'VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)' is reactivated on the master bus, review its suitability carefully. Consider removing if no longer needed."}, {"id": "PROJECT_HAS_MISSING_MEDIA-19", "rule_id": "PROJECT_HAS_MISSING_MEDIA", "category": "Media Files", "severity": "critical", "message": "Project contains 10 unique missing media file(s)", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Locate and reconnect all missing media files. Missing media will result in silent gaps in the audio."}, {"id": "HYGIENE_MISSING_REGIONS-20", "rule_id": "HYGIENE_MISSING_REGIONS", "category": "Session Hygiene", "severity": "info", "message": "Project is missing regions, which can be useful for selections and exports.", "is_mastering_critical": false, "affected_elements": [{"type": "project"}], "recommendation": "Consider adding regions for song sections or export areas."}, {"id": "HYGIENE_MISSING_NOTES-21", "rule_id": "HYGIENE_MISSING_NOTES", "category": "Session Hygiene", "severity": "info", "message": "Project notes are empty.", "is_mastering_critical": false, "affected_elements": [{"type": "project"}], "recommendation": "Consider adding project notes for mix versions, collaborators, or other relevant info."}, {"id": "PROJECT_INCOMPLETE_INFO-45", "rule_id": "PROJECT_INCOMPLETE_INFO", "category": "Project Settings", "severity": "info", "message": "Project metadata (e.g., title) is incomplete.", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Consider filling in project title in REAPER's project settings for better organization."}, {"id": "PROJECT_ABRUPT_START_AT_ZERO-46", "rule_id": "PROJECT_ABRUPT_START_AT_ZERO", "category": "Project Structure", "severity": "warning", "message": "The first item in the project starts at 0:00 with an abrupt (short/no) fade-in.", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Ensure there's adequate pre-roll or a gentle fade-in at the project start to avoid clicks or immediate transients."}, {"id": "TRACK_BYPASSED_FX-47", "rule_id": "TRACK_BYPASSED_FX", "category": "Track Settings", "severity": "info", "message": "Track 'Master' has bypassed FX in its chain: VST3: Pro-L 2 (FabFilter).", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "MASTER_TRACK", "name": "Master", "details": {"bypassed_plugins": ["VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)"]}}], "recommendation": "Review bypassed plugins to ensure they are intentionally inactive."}, {"id": "TRACK_MUTED-48", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track '' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{A40EFDE7-0F00-554C-B635-27AF7124D3BC}", "name": ""}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}], "detailed_analysis": {"plugin_analysis": [{"track_guid": "MASTER_TRACK", "track_name": "Master", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "is_bypassed": false, "category": "Saturation", "issues": ["Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "AU: elysia alpha master (Plugin Alliance)", "is_bypassed": false, "category": "Compressor", "issues": ["Plugin 'AU: elysia alpha master (Plugin Alliance)' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "VST3: UADx SSL G Bus Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Compressor", "issues": ["Plugin 'VST3: UADx SSL G Bus Compressor (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "VST3: Black Box Analog Design HG-2 (Plugin Alliance)", "is_bypassed": false, "category": "Saturation", "issues": ["Plugin 'VST3: Black Box Analog Design HG-2 (Plugin Alliance)' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "AU: Ozone 9 Imager (iZotope)", "is_bypassed": false, "category": "Stereo Imaging", "issues": ["Plugin 'AU: Ozone 9 Imager (iZotope)' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "VST3: Chandler Limited Curve Bender (Softube)", "is_bypassed": false, "category": "EQ", "issues": ["Plugin 'VST3: Chandler Limited Curve Bender (Softube)' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "VST3: Pro-Q 4 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "is_bypassed": false, "category": "EQ", "issues": ["Plugin 'VST3: Pro-Q 4 (<PERSON><PERSON><PERSON><PERSON><PERSON>)' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "is_bypassed": true, "category": "Limiter", "issues": ["Bypassed plugin 'VST3: Pro-L 2 (FabFilter)' on the Master bus would be blacklisted if active."]}]}], "master_bus_analysis": {"guid": "MASTER_TRACK", "name": "Master", "issues_found": []}, "master_output_analysis": {"master_track_guid": "MASTER_TRACK", "master_track_name": "Master", "issues_found": []}, "offline_media_analysis": {"offline_files": [], "missing_files": [{"file_path": "Media/EZbass Track 1 Stereo Mix (4).wav", "track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "take_name": "Take 1"}, {"file_path": "Media/EZbass Track 1 Stereo Mix (4).wav", "track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "take_name": "Take 1"}, {"file_path": "Media/EZbass Track 1 Stereo Mix (4).wav", "track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "take_name": "Take 1"}, {"file_path": "Media/EZbass Track 1 Stereo Mix (4).wav", "track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-002.wav", "track_name": "Guitar AG Vs", "item_name": "Mid24 - No1 Vox Comp-002.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-002.wav", "track_name": "Guitar AG Vs", "item_name": "Mid24 - No1 Vox Comp-002.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-002.wav", "track_name": "Guitar AG Vs", "item_name": "Mid24 - No1 Vox Comp-002.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-003.wav", "track_name": "Guitar AG Brg", "item_name": "Mid24 - No1 Vox Comp-003.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-004.wav", "track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-004.wav", "track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-004.wav", "track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-004.wav", "track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-004.wav", "track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-004.wav", "track_name": "Guitar AG stabs", "item_name": "Mid24 - No1 Vox Comp-004.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-005.wav", "track_name": "Guitar AM RL", "item_name": "Mid24 - No1 Vox Comp-005.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-005.wav", "track_name": "Guitar AM RL", "item_name": "Mid24 - No1 Vox Comp-005.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-006.wav", "track_name": "Guitar AM RR", "item_name": "Mid24 - No1 Vox Comp-006.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-007.wav", "track_name": "Guitar AM Ld", "item_name": "Mid24 - No1 Vox Comp-007.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-007.wav", "track_name": "Guitar AM Ld", "item_name": "Mid24 - No1 Vox Comp-007.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-007.wav", "track_name": "Guitar AM Ld", "item_name": "Mid24 - No1 Vox Comp-007.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-007.wav", "track_name": "Guitar AM Ld", "item_name": "Mid24 - No1 Vox Comp-007.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 No1 Mix_stems.wav", "track_name": "<PERSON><PERSON>", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 No1 Mix_stems_Mid24 - 1_stems_vox7-007.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - 1_stems_vox7-007 - stem", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Lead", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal BV", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal BV", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal BV", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal BV", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Bridge", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Bridge", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Bridge", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}, {"file_path": "Media/Mid24 - No1 Vox Comp-008.wav", "track_name": "Vocal Bridge", "item_name": "Mid24 - No1 Vox Comp-008.wav", "take_name": "Take 1"}], "total_media_files": 44, "total_offline": 0, "total_missing": 10}, "session_hygiene_analysis": {"project_level": ["Project is missing regions, which can be useful for selections and exports.", "Project notes are empty."], "track_level": [{"track_guid": "{88652663-91E9-E54F-B601-480FE745A4BE}", "track_name": "Drums", "issues": ["Track 'Drums' is not part of any folder structure."]}, {"track_guid": "{B4DF1191-5A86-3245-B206-3E934B39726F}", "track_name": "Bass", "issues": ["Track 'Bass' is not part of any folder structure."]}, {"track_guid": "{0286D29D-D960-A34F-9AF1-511013391EB7}", "track_name": "FX Pad", "issues": ["Track 'FX Pad' is not part of any folder structure."]}, {"track_guid": "{865B73A0-BC99-A44F-A591-B1E8C986600E}", "track_name": "FX Riser", "issues": ["Track 'FX Riser' is not part of any folder structure.", "Track name 'FX Riser' is duplicated in the project."]}, {"track_guid": "{1BE0C274-5278-D84E-BCEA-99B3A17C2DF2}", "track_name": "FX Riser", "issues": ["Track 'FX Riser' is not part of any folder structure.", "Track name 'FX Riser' is duplicated in the project."]}, {"track_guid": "{1EA392B5-1C34-6449-9260-CA1AF0541282}", "track_name": "Vocal Lead", "issues": ["Track 'Vocal Lead' is not part of any folder structure."]}, {"track_guid": "{4E7FE7FC-0527-8B4E-A37C-964B171944ED}", "track_name": "Vocal BV", "issues": ["Track 'Vocal BV' is not part of any folder structure."]}, {"track_guid": "{9B1809BD-9F0E-9A45-8EEF-EC3921F5945A}", "track_name": "Vocal Bridge", "issues": ["Track 'Vocal Bridge' is not part of any folder structure."]}, {"track_guid": "{EDE251B7-B545-6345-B64B-F371056FDA8D}", "track_name": "Room Verb", "issues": ["Track 'Room Verb' is not part of any folder structure."]}, {"track_guid": "{4D174DEB-CF59-814D-8E15-B2D591EF936B}", "track_name": "Git Ld Delay", "issues": ["Track 'Git Ld Delay' is not part of any folder structure."]}, {"track_guid": "{F809F7FA-4433-5646-A932-7A132971935C}", "track_name": "Vx Lng Delay", "issues": ["Track 'Vx Lng Delay' is not part of any folder structure."]}]}, "file_reference_analysis": {"summary_messages": ["✅ No problematic file references detected based on path patterns and metadata."], "problematic_files": []}, "item_property_analysis": [{"item_guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "track_guid": "{B4DF1191-5A86-3245-B206-3E934B39726F}", "track_name": "Bass", "issues": ["Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "track_guid": "{B4DF1191-5A86-3245-B206-3E934B39726F}", "track_name": "Bass", "issues": ["Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{306ED79D-A8C4-A246-B56E-6ACC26FEF836}", "item_name": "Mid24 - No1 Vox Comp-002.wav", "track_guid": "{542623E9-23DF-8948-9401-7AA5C627B431}", "track_name": "Guitar AG Vs", "issues": ["Item 'Mid24 - No1 Vox Comp-002.wav' on track 'Guitar AG Vs' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{45C2870C-673F-1E4E-B614-43F0247B99EF}", "item_name": "Mid24 - No1 Vox Comp-003.wav", "track_guid": "{3EFA4245-5C24-5645-B6F6-F7C686F39273}", "track_name": "Guitar AG Brg", "issues": ["Item 'Mid24 - No1 Vox Comp-003.wav' on track 'Guitar AG Brg' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{D3FE22ED-E7A6-D94C-9CEB-94B8BE3D58BA}", "item_name": "Mid24 - No1 Vox Comp-005.wav", "track_guid": "{6C7AC72C-5D9E-A346-A437-176C3EA9558E}", "track_name": "Guitar AM RL", "issues": ["Item 'Mid24 - No1 Vox Comp-005.wav' on track 'Guitar AM RL' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{EB0F3425-8D9F-8A41-8979-B35704099B6E}", "item_name": "Mid24 - No1 Vox Comp-006.wav", "track_guid": "{1303983A-9666-6C46-BBD0-677688461E5A}", "track_name": "Guitar AM RR", "issues": ["Item 'Mid24 - No1 Vox Comp-006.wav' on track 'Guitar AM RR' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{56A2C685-AD0E-534D-97C0-426C2FCD20D7}", "item_name": "Mid24 - No1 Vox Comp-007.wav", "track_guid": "{ACD6E9A1-3D55-854B-82EB-7A2FB2564A50}", "track_name": "Guitar AM Ld", "issues": ["Item 'Mid24 - No1 Vox Comp-007.wav' on track 'Guitar AM Ld' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{F7E1032D-85A8-6E41-B90B-7C7A1B67A8EA}", "item_name": " - stem", "track_guid": "{8E3104BF-9F9F-9D49-B1FE-FDD8A529FBA8}", "track_name": "<PERSON><PERSON>", "issues": ["Item ' - stem' on track 'Chug' has an abrupt start (short/no fade-in).", "Item ' - stem' on track 'Chug' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{1971DA12-1621-4C4C-87DA-D8F846C72BC3}", "item_name": "Mid24 - No1 Vox Comp-008.wav", "track_guid": "{1EA392B5-1C34-6449-9260-CA1AF0541282}", "track_name": "Vocal Lead", "issues": ["Item 'Mid24 - No1 Vox Comp-008.wav' on track 'Vocal Lead' has an abrupt start (short/no fade-in)."]}], "project_settings_analysis": {"bit_depth": {"value": null, "is_suboptimal": false}, "sample_rate": {"value": 44100, "is_nonstandard": false}, "has_incomplete_info": true, "first_item_abrupt_start_at_zero": true, "stereo_balance_issue": null}, "track_specific_issues": [{"guid": "MASTER_TRACK", "name": "Master", "issues": ["Track 'Master' has bypassed FX in its chain: VST3: Pro-L 2 (FabFilter)."]}, {"guid": "{A40EFDE7-0F00-554C-B635-27AF7124D3BC}", "name": "", "issues": ["Track '' is muted."]}]}, "issues": [{"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx Ampex ATR-102 Master Tape (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-9", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/EZbass Track 1 Stereo Mix (4).wav' (used on <PERSON>)", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2622EC3B-0014-1541-B79D-FB3E0CA3D6E0}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{********-0AC0-C34C-8650-14D5C3528956}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}-0}", "name": "Take 1", "details": {"track_name": "Bass", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "file_path": "Media/EZbass Track 1 Stereo Mix (4).wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-35", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-36", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-36", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "PLUGIN_BYPASSED_BLACKLISTED_MASTER-8", "rule_id": "PLUGIN_BYPASSED_BLACKLISTED_MASTER", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Bypassed plugin 'VST3: Pro-L 2 (FabFilter)' on the Master bus would be blacklisted if active.", "is_mastering_critical": true, "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "If 'VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)' is reactivated on the master bus, review its suitability carefully. Consider removing if no longer needed."}, {"id": "PROJECT_HAS_MISSING_MEDIA-19", "rule_id": "PROJECT_HAS_MISSING_MEDIA", "category": "Media Files", "severity": "critical", "message": "Project contains 10 unique missing media file(s)", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Locate and reconnect all missing media files. Missing media will result in silent gaps in the audio."}, {"id": "PROJECT_INCOMPLETE_INFO-45", "rule_id": "PROJECT_INCOMPLETE_INFO", "category": "Project Settings", "severity": "info", "message": "Project metadata (e.g., title) is incomplete.", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Consider filling in project title in REAPER's project settings for better organization."}, {"id": "PROJECT_ABRUPT_START_AT_ZERO-46", "rule_id": "PROJECT_ABRUPT_START_AT_ZERO", "category": "Project Structure", "severity": "warning", "message": "The first item in the project starts at 0:00 with an abrupt (short/no) fade-in.", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Ensure there's adequate pre-roll or a gentle fade-in at the project start to avoid clicks or immediate transients."}, {"id": "TRACK_BYPASSED_FX-47", "rule_id": "TRACK_BYPASSED_FX", "category": "Track Settings", "severity": "info", "message": "Track 'Master' has bypassed FX in its chain: VST3: Pro-L 2 (FabFilter).", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "MASTER_TRACK", "name": "Master", "details": {"bypassed_plugins": ["VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)"]}}], "recommendation": "Review bypassed plugins to ensure they are intentionally inactive."}, {"id": "TRACK_MUTED-48", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track '' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{A40EFDE7-0F00-554C-B635-27AF7124D3BC}", "name": ""}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}]}