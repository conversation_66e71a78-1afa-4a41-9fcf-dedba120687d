{"overall_health_score": 0, "genre": "general", "strict_mode": false, "summary_metrics": {"critical_issues_count": 145, "warning_issues_count": 19, "info_issues_count": 30, "passed_checks_count": 2}, "issue_groups": [{"rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "title": "144 issues: missing media file", "description": "Affects tracks: SoF Guide,  - stem - stem - stem and 6 more", "count": 144, "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{A7D402EB-BC38-8C45-88CA-C263840882B6}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{97D56F43-1B7A-2A4F-9C05-5D9FD3203ECB}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{D155FA1D-F666-C94D-97D9-A09245C34245}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{9DDEE00A-6259-034A-8E3E-85C67066C329}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{8DBCBE14-1BBB-684F-A401-58BC62EF751E}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{034B68F1-7000-884D-8F0D-1A00D4F7A69A}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem", "item_name": " - stem - stem", "file_path": "Media/Claire <PERSON>_stems_ - stem.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON><PERSON><PERSON>-{5B121D56-B831-D34E-85FE-E227C77B5FE7}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{C97D8F1C-8BCF-E34F-8AD2-BDD0B4A7EA47}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON>UI<PERSON>-{49D08F31-8B7C-E941-BEBD-9A503C3788F8}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2E1C8216-E97B-1143-9538-9B981F0DC5F8}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{79C356C2-24F2-6E44-8180-B4A42B5CF6BF}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{30579F23-FA08-2649-A9F1-761588D71DF8}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{92B6C849-B762-CF48-8989-63776B6A286D}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{FE000D5B-782B-094D-B1FD-BED86FB8BC77}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{4D40D5BD-996C-C344-9318-23E94986148E}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E89349FE-7ED0-3941-9BC5-1EB0B675E4B9}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F8724DEB-FDD2-7943-9F22-7D7EF6CC6DD7}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{A7683185-BC6E-6344-A501-8A824A4ADAAE}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2C1EEDBB-ED16-EC4C-8F7C-4A0B9BDF574A}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/U&Me_stems.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{5E0B680C-5B79-1E45-9360-D58EB80BAF2D}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1027.wav", "file_path": "Media/02-231216_1027.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{CEB41A36-290B-3842-8A6E-8F2AD7E43164}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1029.wav", "file_path": "Media/02-231216_1029.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{7D2575A9-675A-3A4E-A420-F74B00B1805F}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1030.wav", "file_path": "Media/02-231216_1030.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{EBBBEF4F-6B5F-DA48-B72A-1AEEDFCDCF0B}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1024.wav", "file_path": "Media/02-231216_1024.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{0FB77E51-F10B-B042-9C29-64E29CD36143}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1029.wav", "file_path": "Media/02-231216_1029.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{117467A7-3AAD-D64B-9523-74900AB62208}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1038.wav", "file_path": "Media/02-231216_1038.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-G<PERSON><PERSON>-{BC5387C2-6161-4545-B458-7C12B3A35EE2}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1037.wav", "file_path": "Media/02-231216_1037.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{C5D888FB-0615-B14C-A884-BF0B60795431}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1036.wav", "file_path": "Media/02-231216_1036.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-G<PERSON><PERSON>-{5B4570E1-DED4-8F48-B003-A0D90D0A2487}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1032.wav", "file_path": "Media/02-231216_1032.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BFF8C89C-F194-0749-A836-DF483CED214F}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1033.wav", "file_path": "Media/02-231216_1033.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{1CA0A462-BB82-F74D-82FB-0CD56B260FF9}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1038.wav", "file_path": "Media/02-231216_1038.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{A7DC4FB7-C500-D444-86E6-BF8A25B88655}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1048-02.wav", "file_path": "Media/02-231216_1048-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{5EE7A7D9-492E-7A46-9AA8-BAD608FF4677}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1046.wav", "file_path": "Media/02-231216_1046.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{409C54B8-82FD-5745-9CE8-E0A9E607EB98}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1048-01.wav", "file_path": "Media/02-231216_1048-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{EC944D7C-6297-7540-938A-C3C6CA5528DE}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1045.wav", "file_path": "Media/02-231216_1045.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{12943EDF-4965-6F45-BCA5-1FA85D34CC89}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1048-02.wav", "file_path": "Media/02-231216_1048-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E157623C-9243-4A40-BB5F-81E2F5708F48}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1051.wav", "file_path": "Media/02-231216_1051.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F1F64C5B-3DFF-3049-A6C8-92D25615F8C5}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1050-01.wav", "file_path": "Media/02-231216_1050-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON>UI<PERSON>-{6E038F47-63EC-2441-9D31-3BA777863BEE}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1050.wav", "file_path": "Media/02-231216_1050.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{694F4DAA-A1F2-E74F-B5E2-AF0F7C3601EB}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1050.wav", "file_path": "Media/02-231216_1050.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F305C08A-6A3C-CF45-9183-EB7C88DAED09}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1048-02.wav", "file_path": "Media/02-231216_1048-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{08745A48-8576-9B4C-8725-969A5ED0AD20}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1058.wav", "file_path": "Media/02-231216_1058.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON><PERSON><PERSON>-{275D43E1-C224-404E-A901-6961A3C11F1F}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1057.wav", "file_path": "Media/02-231216_1057.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{780AAF2E-7D9E-8744-9400-475DB5F5EC0A}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1058-01.wav", "file_path": "Media/02-231216_1058-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{A2BAAEFD-5B5A-B04D-9C84-9258898D1692}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1056-02.wav", "file_path": "Media/02-231216_1056-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{CF494434-DC1F-3546-B4B7-ECDA9DB4E781}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1056-01.wav", "file_path": "Media/02-231216_1056-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{16F40C7B-17AC-C141-92D8-A26263DFD146}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1059.wav", "file_path": "Media/02-231216_1059.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0B3DC1E0-074E-A943-AB54-810E9643ACAF}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1059.wav", "file_path": "Media/02-231216_1059.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{FAA9A96D-5291-EB40-A3F5-606D970C6EEA}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1133.wav", "file_path": "Media/02-231216_1133.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{3C77EB1A-11BD-3944-8B08-73340D043593}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1133-02.wav", "file_path": "Media/02-231216_1133-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{B2AB7B70-A5F1-DF45-8031-E4554B02619A}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1135.wav", "file_path": "Media/02-231216_1135.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{C4EB97A1-0BD3-CF42-BA9F-3C317345F388}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1135.wav", "file_path": "Media/02-231216_1135.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{C0A64242-1482-FE44-99D3-8859B43F87D3}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1133-02.wav", "file_path": "Media/02-231216_1133-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{0CAD588E-0821-374E-A33F-E8185DBACB40}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "02-231216_1135.wav", "file_path": "Media/02-231216_1135.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{4D652367-B038-1E49-966C-7789D3254821}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{760D100A-571F-3746-BB58-2E89BF05E295}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{4C05D606-EFDE-5046-901F-21C6A9B2D3CB}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{DA70BF43-8601-F849-B91E-F49EBF52CE9D}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{014533F1-E109-9840-8B79-A8E511E63DCC}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1040.wav", "file_path": "Media/03-231216_1040.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON>UI<PERSON>-{8CAE6A8E-CC6C-7043-9D9C-0B699CA47CCA}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1042-01.wav", "file_path": "Media/03-231216_1042-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2DBF6764-2CAC-B749-9863-B8C9B279CFBB}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1042.wav", "file_path": "Media/03-231216_1042.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{1035F8BF-216C-8049-A22D-29D5401370C6}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1041.wav", "file_path": "Media/03-231216_1041.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{AC12E603-A174-6243-820E-BF05FCFC992F}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1040-01.wav", "file_path": "Media/03-231216_1040-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{06C144CD-6BFE-3C41-B0E9-D6C9448526AE}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1043.wav", "file_path": "Media/03-231216_1043.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON><PERSON><PERSON>-{5545EB8D-0D8C-3A44-BBDD-F97B92BC4051}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1039.wav", "file_path": "Media/03-231216_1039.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BC4CF57F-4DB8-6548-80A7-BA2A7E50346F}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1042.wav", "file_path": "Media/03-231216_1042.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F6F38FFF-899A-9E4C-A63F-C40FC80F7624}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1052-01.wav", "file_path": "Media/03-231216_1052-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{89FAE235-245F-2F42-9FCF-81D2E78030AA}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1053.wav", "file_path": "Media/03-231216_1053.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{9E8B634D-1DF2-C240-B71E-36BC85EE3EFF}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1053-01.wav", "file_path": "Media/03-231216_1053-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{D8C6CA7F-8C6B-1F43-9E61-A9E2240B8BD1}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1052.wav", "file_path": "Media/03-231216_1052.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{517234FA-1144-5941-B4A6-DF9BA5237C89}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1053.wav", "file_path": "Media/03-231216_1053.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON><PERSON><PERSON>-{44B29471-7A37-A642-A543-C70B84DD3CD1}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1112.wav", "file_path": "Media/03-231216_1112.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{977D8B58-04DA-6F4A-A425-5685C90AD31C}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1112-01.wav", "file_path": "Media/03-231216_1112-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E7593859-D51D-CE40-BB71-D208A4435A69}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1111-01.wav", "file_path": "Media/03-231216_1111-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E0C02972-2FF8-674B-913C-C4407664FD81}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1104.wav", "file_path": "Media/03-231216_1104.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{CA9019C8-52F9-654D-96E2-FFC62E5711D0}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1111.wav", "file_path": "Media/03-231216_1111.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{B6B13B41-CE16-E04B-A1E1-7C00EDFAED94}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1101-01.wav", "file_path": "Media/03-231216_1101-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{11BA9B4A-96B0-0B41-94C2-298E96A22F69}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1102.wav", "file_path": "Media/03-231216_1102.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{7F529546-959C-184C-8D01-2A8C0ED24A62}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1101.wav", "file_path": "Media/03-231216_1101.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{1CCA528C-CDA6-B742-8DE0-ED7571C0FBEF}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1103-01.wav", "file_path": "Media/03-231216_1103-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F14DBE1B-5E1C-FA4D-AED3-FE918FADF40C}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1100-02.wav", "file_path": "Media/03-231216_1100-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{EDD82209-712E-554C-8C2D-2F93DB394F47}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1100.wav", "file_path": "Media/03-231216_1100.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{51CF36EC-F3BE-B64F-B300-C901E536E63F}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1112-01.wav", "file_path": "Media/03-231216_1112-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{D0381303-0CF1-AC40-85CB-F069801DE02D}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1112.wav", "file_path": "Media/03-231216_1112.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{168FCED5-D536-DB40-B48D-B36FCCF8AA82}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1142.wav", "file_path": "Media/03-231216_1142.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{AB8F8E81-A303-E94E-AA4F-22DE796908ED}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1145.wav", "file_path": "Media/03-231216_1145.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON><PERSON><PERSON>-{4F7BE5E1-B5F8-DB47-863D-B886579C6DC0}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1144.wav", "file_path": "Media/03-231216_1144.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F12D0348-BC41-DC4A-B467-F6316518E095}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1141.wav", "file_path": "Media/03-231216_1141.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-<PERSON><PERSON><PERSON>-{95EE421D-87E6-B844-9E68-DC9BCA8EA97D}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1140.wav", "file_path": "Media/03-231216_1140.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{4FC55987-F75F-7743-80B9-BE6B383B6067}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "03-231216_1142.wav", "file_path": "Media/03-231216_1142.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{C81D7A3A-1910-4C4C-B42C-7D1219B1A9E3}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "file_path": "Media/<PERSON>_stems_ - stem - stem-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{6D8E42B3-1469-404A-A07B-FAB7877439DB}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "file_path": "Media/<PERSON>_stems_ - stem - stem-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{FA17DC73-2AD6-B844-B70A-AC479B214A50}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "file_path": "Media/<PERSON>_stems_ - stem - stem-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{36BAFB98-E344-834E-8606-6CF9CF367A3A}-0}", "name": "Take 1", "details": {"track_name": " - stem - stem", "item_name": " - stem - stem", "file_path": "Media/<PERSON>_stems_ - stem-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{7CB00E84-C7CD-D740-BC5D-9E28FE4894DF}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-002.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{738AD330-A407-5844-9286-E3A2AA6217A9}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-003.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{42118230-EEBE-6243-828D-0F516A69CF3A}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1114-01.wav", "file_path": "Media/04-231216_1114-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E16F4505-A101-2F4A-9A6E-FBA485E7794F}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1116.wav", "file_path": "Media/04-231216_1116.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F1607D62-BBD8-FC4B-860C-B088085CB502}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1115.wav", "file_path": "Media/04-231216_1115.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{CBB5F222-A284-3A43-B9EF-4C24FCAFD433}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1116.wav", "file_path": "Media/04-231216_1116.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2CC04F6B-4465-BA4B-AE5F-A9881C9511F2}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1116-01.wav", "file_path": "Media/04-231216_1116-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{39B92EE7-5819-C24D-8862-80AF5F9E4E53}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1117.wav", "file_path": "Media/04-231216_1117.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{16BC6265-5B08-3547-B529-2C042DF94E39}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1119-01.wav", "file_path": "Media/04-231216_1119-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{07254798-3840-344C-B6DA-0BE0BA7F3EED}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1119-02.wav", "file_path": "Media/04-231216_1119-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{884591F2-0F81-7340-941B-AF62742B6FEB}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1120.wav", "file_path": "Media/04-231216_1120.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{5A140AE8-4597-794F-9917-E61AA9827E1C}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1119.wav", "file_path": "Media/04-231216_1119.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{A93E7B4B-4DA7-924C-BA90-67F6DD03FF88}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1119-02.wav", "file_path": "Media/04-231216_1119-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{0C6A6F5C-F142-CE4D-BDC8-5AA3C846E3DD}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1119.wav", "file_path": "Media/04-231216_1119.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{D92DEA3A-41AA-DC45-B555-4C3B9855949C}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1119-02.wav", "file_path": "Media/04-231216_1119-02.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{134D0925-4998-B649-B4B5-F74AF467E35E}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1129.wav", "file_path": "Media/04-231216_1129.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{F85AC54C-D036-1049-B5E3-DE29D315A684}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1129-01.wav", "file_path": "Media/04-231216_1129-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{14E77E0F-E94B-F647-902C-ED776BCAA166}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1128.wav", "file_path": "Media/04-231216_1128.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E5C47C89-B312-144B-BC14-86FD56618598}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1131.wav", "file_path": "Media/04-231216_1131.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{FD1745E8-A25E-6149-A994-50696086CF6B}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1128-01.wav", "file_path": "Media/04-231216_1128-01.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{C6C11BDC-BE2B-7744-91FE-9F4A73FA5FB9}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "04-231216_1130.wav", "file_path": "Media/04-231216_1130.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{9289D69B-84FD-574F-AD61-A6D40AADED3E}-0}", "name": "Take 1", "details": {"track_name": " - stem", "item_name": " - stem", "file_path": "Media/Claire <PERSON>_stems-003.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{2C3B7E25-3E98-5C44-BFA5-8D0242C0B958}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "05-231216_1125.wav", "file_path": "Media/05-231216_1125.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{88EDB38A-7C29-AC4B-8F30-EC0DDD800FD7}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "05-231216_1126.wav", "file_path": "Media/05-231216_1126.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{58914E6E-CE9C-6140-9BF3-E0B8273FAF27}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "05-231216_1124.wav", "file_path": "Media/05-231216_1124.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{2810BE94-9770-404E-B907-B9BC4EC23448}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "05-231216_1122.wav", "file_path": "Media/05-231216_1122.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{4C4054C9-2ADF-514C-8ACC-71853A982234}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "05-231216_1122.wav", "file_path": "Media/05-231216_1122.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{3E373480-9C22-6F4A-814E-2B9AA55C6E6D}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "05-231216_1124.wav", "file_path": "Media/05-231216_1124.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-G<PERSON><PERSON>-{9090A032-5A90-3543-924F-F70E419335BE}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "05-231216_1122.wav", "file_path": "Media/05-231216_1122.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUI<PERSON>-{2F796987-C06B-7D46-BAC8-D82E3C4B61A9}-0}", "name": "Take 1", "details": {"track_name": "", "item_name": "05-231216_1124.wav", "file_path": "Media/05-231216_1124.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{25665736-C9F2-0349-A07D-AE7C10B0F7F4}-0}", "name": "Take 1", "details": {"track_name": "AndyM2", "item_name": "AndyM2.WAV", "file_path": "Media/AndyM2.WAV"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{F9E1B257-6E59-D144-B253-0635C5230504}-0}", "name": "Take 1", "details": {"track_name": "AndyM2", "item_name": "AndyM2.WAV", "file_path": "Media/AndyM2.WAV"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{12A493F7-D35C-6645-AEC5-189CAB154507}-0}", "name": "Take 1", "details": {"track_name": "AndyM_lead2", "item_name": "AndyM_lead1.WAV", "file_path": "Media/AndyM_lead1.WAV"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E69097D3-CDC6-5848-AC07-49E91B2AA350}-0}", "name": "Take 1", "details": {"track_name": "AndyM_lead2", "item_name": "AndyM_lead1.WAV", "file_path": "Media/AndyM_lead1.WAV"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{53EC289C-4EBC-C045-B0F5-6BA60A3A825D}-0}", "name": "Take 1", "details": {"track_name": "AndyM_lead2", "item_name": "AndyM_lead1.WAV", "file_path": "Media/AndyM_lead1.WAV"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{1358280C-1A87-4849-8C20-4708A6D2AAD7}-0}", "name": "Take 1", "details": {"track_name": "AndyM_lead2", "item_name": "AndyM_lead1.WAV", "file_path": "Media/AndyM_lead1.WAV"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{441E2813-D88F-C04D-A2EE-7D80A1B5D642}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-001", "item_name": "U&ME AndyG Rhythm-001.wav", "file_path": "Media/U&ME AndyG Rhythm-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{E5B53FD8-165E-DF44-AFAC-8ED7D7FB3D7B}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-001", "item_name": "U&ME AndyG Rhythm-001.wav", "file_path": "Media/U&ME AndyG Rhythm-001.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{40C014AC-F298-AD4D-92BC-96C8B9D3EE13}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-002", "item_name": "U&ME AndyG Rhythm-002.wav", "file_path": "Media/U&ME AndyG Rhythm-002.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{5F1AEEE2-3FBB-EB4A-881C-0216316E0D9C}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-002", "item_name": "U&ME AndyG Rhythm-002.wav", "file_path": "Media/U&ME AndyG Rhythm-002.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{B8162FBD-2FA1-1B4A-B028-5D39E9AA864D}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-002", "item_name": "U&ME AndyG Rhythm-002.wav", "file_path": "Media/U&ME AndyG Rhythm-002.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{BDA13D27-B523-3D48-B365-AD84F30A42B8}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-002", "item_name": "U&ME AndyG Rhythm-002.wav", "file_path": "Media/U&ME AndyG Rhythm-002.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{881E10D2-6B12-8B4E-9F6F-25D60DBDB180}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-003", "item_name": "U&ME AndyG Rhythm-003.wav", "file_path": "Media/U&ME AndyG Rhythm-003.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{B404B058-B21B-5A42-A5C8-05477220D31D}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-005", "item_name": "U&ME AndyG Rhythm-005.wav", "file_path": "Media/U&ME AndyG Rhythm-005.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{00FD0616-66DE-A241-9634-F76372879FD0}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-005", "item_name": "U&ME AndyG Rhythm-005.wav", "file_path": "Media/U&ME AndyG Rhythm-005.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{9A490AA4-C7A1-FD4D-9398-349CEDB07163}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-005", "item_name": "U&ME AndyG Rhythm-005.wav", "file_path": "Media/U&ME AndyG Rhythm-005.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{B6DDB204-13AD-5E42-91BE-899A757FA27C}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-006", "item_name": "U&ME AndyG Rhythm-006.wav", "file_path": "Media/U&ME AndyG Rhythm-006.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-GUID-{AA660D69-A27C-EF43-BAB4-57F7A58E10DA}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-006", "item_name": "U&ME AndyG Rhythm-006.wav", "file_path": "Media/U&ME AndyG Rhythm-006.wav"}}, {"type": "take", "guid": "{DEFAULT-TAKE-G<PERSON><PERSON>-{866417B2-02D5-DC40-ADF4-A224AD507D9F}-0}", "name": "Take 1", "details": {"track_name": "U&ME AndyG Rhythm-006", "item_name": "U&ME AndyG Rhythm-006.wav", "file_path": "Media/U&ME AndyG Rhythm-006.wav"}}], "sample_issue": {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}}, {"rule_id": "HYGIENE_DUPLICATE_TRACK_NAME", "category": "Session Hygiene", "severity": "warning", "title": "9 tracks with hygiene duplicate track name", "description": "Affects tracks:  - stem - stem,  - stem - stem - stem,  - stem", "count": 9, "is_mastering_critical": false, "affected_elements": [{"type": "track", "guid": "{CE762FAB-33C5-9842-8B41-67867E5DF587}", "name": " - stem - stem - stem"}, {"type": "track", "guid": "{F763906E-9544-AE4D-9CA7-0D35CCF3078D}", "name": " - stem - stem"}, {"type": "track", "guid": "{BF0C1A62-39A6-B447-AC95-CC7573CC7B92}", "name": " - stem"}, {"type": "track", "guid": "{7B0BD668-DAAE-2240-9EE7-C9F54986151E}", "name": " - stem"}, {"type": "track", "guid": "{00B65410-DDEF-E541-981A-C77D12A2F515}", "name": " - stem"}, {"type": "track", "guid": "{3A259BC7-FC8F-5D48-8312-BD8A75B0446D}", "name": " - stem - stem - stem"}, {"type": "track", "guid": "{EAD44D13-84C3-584E-AC86-E345180F997B}", "name": " - stem - stem"}, {"type": "track", "guid": "{1A7BDA4B-B3ED-0445-9C67-07C817E78892}", "name": " - stem"}, {"type": "track", "guid": "{3C66D644-F711-9043-A0BE-6390DC7AC961}", "name": " - stem"}], "sample_issue": {"id": "HYGIENE_DUPLICATE_TRACK_NAME-149", "rule_id": "HYGIENE_DUPLICATE_TRACK_NAME", "category": "Session Hygiene", "severity": "warning", "message": "Track name ' - stem - stem - stem' is duplicated in the project.", "is_mastering_critical": false, "affected_elements": [{"type": "track", "guid": "{CE762FAB-33C5-9842-8B41-67867E5DF587}", "name": " - stem - stem - stem"}], "recommendation": "Rename track ' - stem - stem - stem' to ensure all track names are unique for clarity."}}, {"rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "title": "12 items with item abrupt start", "description": "Affects tracks:  - stem - stem, SoF Guide,  - stem - stem - stem,  - stem and 2 more", "count": 12, "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}, {"type": "item", "guid": "{A7D402EB-BC38-8C45-88CA-C263840882B6}", "name": " - stem - stem - stem", "details": {"track_name": " - stem - stem - stem"}}, {"type": "item", "guid": "{034B68F1-7000-884D-8F0D-1A00D4F7A69A}", "name": " - stem - stem", "details": {"track_name": " - stem - stem"}}, {"type": "item", "guid": "{2C1EEDBB-ED16-EC4C-8F7C-4A0B9BDF574A}", "name": " - stem", "details": {"track_name": " - stem"}}, {"type": "item", "guid": "{4D652367-B038-1E49-966C-7789D3254821}", "name": " - stem", "details": {"track_name": " - stem"}}, {"type": "item", "guid": "{C81D7A3A-1910-4C4C-B42C-7D1219B1A9E3}", "name": " - stem - stem - stem", "details": {"track_name": " - stem - stem - stem"}}, {"type": "item", "guid": "{36BAFB98-E344-834E-8606-6CF9CF367A3A}", "name": " - stem - stem", "details": {"track_name": " - stem - stem"}}, {"type": "item", "guid": "{7CB00E84-C7CD-D740-BC5D-9E28FE4894DF}", "name": " - stem", "details": {"track_name": " - stem"}}, {"type": "item", "guid": "{9289D69B-84FD-574F-AD61-A6D40AADED3E}", "name": " - stem", "details": {"track_name": " - stem"}}, {"type": "item", "guid": "{12A493F7-D35C-6645-AEC5-189CAB154507}", "name": "AndyM_lead1.WAV", "details": {"track_name": "AndyM_lead2"}}, {"type": "item", "guid": "{441E2813-D88F-C04D-A2EE-7D80A1B5D642}", "name": "U&ME AndyG Rhythm-001.wav", "details": {"track_name": "U&ME AndyG Rhythm-001"}}, {"type": "item", "guid": "{881E10D2-6B12-8B4E-9F6F-25D60DBDB180}", "name": "U&ME AndyG Rhythm-003.wav", "details": {"track_name": "U&ME AndyG Rhythm-003"}}], "sample_issue": {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}}, {"rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "title": "14 items with item abrupt end", "description": "Affects tracks:  - stem - stem, SoF Guide,  - stem - stem - stem,  - stem and 3 more", "count": 14, "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}, {"type": "item", "guid": "{8DBCBE14-1BBB-684F-A401-58BC62EF751E}", "name": " - stem - stem - stem", "details": {"track_name": " - stem - stem - stem"}}, {"type": "item", "guid": "{034B68F1-7000-884D-8F0D-1A00D4F7A69A}", "name": " - stem - stem", "details": {"track_name": " - stem - stem"}}, {"type": "item", "guid": "{A7683185-BC6E-6344-A501-8A824A4ADAAE}", "name": " - stem", "details": {"track_name": " - stem"}}, {"type": "item", "guid": "{2C1EEDBB-ED16-EC4C-8F7C-4A0B9BDF574A}", "name": " - stem", "details": {"track_name": " - stem"}}, {"type": "item", "guid": "{E157623C-9243-4A40-BB5F-81E2F5708F48}", "name": "02-231216_1051.wav", "details": {"track_name": ""}}, {"type": "item", "guid": "{11BA9B4A-96B0-0B41-94C2-298E96A22F69}", "name": "03-231216_1102.wav", "details": {"track_name": ""}}, {"type": "item", "guid": "{FA17DC73-2AD6-B844-B70A-AC479B214A50}", "name": " - stem - stem - stem", "details": {"track_name": " - stem - stem - stem"}}, {"type": "item", "guid": "{36BAFB98-E344-834E-8606-6CF9CF367A3A}", "name": " - stem - stem", "details": {"track_name": " - stem - stem"}}, {"type": "item", "guid": "{738AD330-A407-5844-9286-E3A2AA6217A9}", "name": " - stem", "details": {"track_name": " - stem"}}, {"type": "item", "guid": "{E5B53FD8-165E-DF44-AFAC-8ED7D7FB3D7B}", "name": "U&ME AndyG Rhythm-001.wav", "details": {"track_name": "U&ME AndyG Rhythm-001"}}, {"type": "item", "guid": "{BDA13D27-B523-3D48-B365-AD84F30A42B8}", "name": "U&ME AndyG Rhythm-002.wav", "details": {"track_name": "U&ME AndyG Rhythm-002"}}, {"type": "item", "guid": "{9A490AA4-C7A1-FD4D-9398-349CEDB07163}", "name": "U&ME AndyG Rhythm-005.wav", "details": {"track_name": "U&ME AndyG Rhythm-005"}}, {"type": "item", "guid": "{866417B2-02D5-DC40-ADF4-A224AD507D9F}", "name": "U&ME AndyG Rhythm-006.wav", "details": {"track_name": "U&ME AndyG Rhythm-006"}}], "sample_issue": {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}}, {"rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "title": "9 tracks with track muted", "description": "Affects tracks:  - stem - stem, SoF Guide,  - stem", "count": 9, "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}, {"type": "track", "guid": "{F763906E-9544-AE4D-9CA7-0D35CCF3078D}", "name": " - stem - stem"}, {"type": "track", "guid": "{BF0C1A62-39A6-B447-AC95-CC7573CC7B92}", "name": " - stem"}, {"type": "track", "guid": "{27EED9ED-98B9-4B47-81AF-AC2D6CF9082B}", "name": ""}, {"type": "track", "guid": "{76D6A850-BABE-FC49-97D4-A34ABE7B954D}", "name": ""}, {"type": "track", "guid": "{EAD44D13-84C3-584E-AC86-E345180F997B}", "name": " - stem - stem"}, {"type": "track", "guid": "{1A7BDA4B-B3ED-0445-9C67-07C817E78892}", "name": " - stem"}, {"type": "track", "guid": "{2A703094-5F3A-1242-837C-8B9485143B22}", "name": ""}, {"type": "track", "guid": "{5EE45C4B-6F5B-A748-9C54-0B218C3FE5E6}", "name": ""}], "sample_issue": {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}}], "ungrouped_issues": [{"id": "PROJECT_HAS_MISSING_MEDIA-145", "rule_id": "PROJECT_HAS_MISSING_MEDIA", "category": "Media Files", "severity": "critical", "message": "Project contains 144 missing media file(s)", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Locate and reconnect all missing media files. Missing media will result in silent gaps in the audio."}, {"id": "HYGIENE_MISSING_MARKERS-146", "rule_id": "HYGIENE_MISSING_MARKERS", "category": "Session Hygiene", "severity": "info", "message": "Project is missing markers, which can help with navigation and song structure.", "is_mastering_critical": false, "affected_elements": [{"type": "project"}], "recommendation": "Consider adding markers for key song sections."}, {"id": "HYGIENE_MISSING_REGIONS-147", "rule_id": "HYGIENE_MISSING_REGIONS", "category": "Session Hygiene", "severity": "info", "message": "Project is missing regions, which can be useful for selections and exports.", "is_mastering_critical": false, "affected_elements": [{"type": "project"}], "recommendation": "Consider adding regions for song sections or export areas."}, {"id": "HYGIENE_MISSING_NOTES-148", "rule_id": "HYGIENE_MISSING_NOTES", "category": "Session Hygiene", "severity": "info", "message": "Project notes are empty.", "is_mastering_critical": false, "affected_elements": [{"type": "project"}], "recommendation": "Consider adding project notes for mix versions, collaborators, or other relevant info."}, {"id": "PROJECT_INCOMPLETE_INFO-184", "rule_id": "PROJECT_INCOMPLETE_INFO", "category": "Project Settings", "severity": "info", "message": "Project metadata (e.g., title) is incomplete.", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Consider filling in project title in REAPER's project settings for better organization."}, {"id": "PROJECT_ABRUPT_START_AT_ZERO-185", "rule_id": "PROJECT_ABRUPT_START_AT_ZERO", "category": "Project Structure", "severity": "warning", "message": "The first item in the project starts at 0:00 with an abrupt (short/no) fade-in.", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Ensure there's adequate pre-roll or a gentle fade-in at the project start to avoid clicks or immediate transients."}], "detailed_analysis": {"master_bus_analysis": {"guid": "MASTER_TRACK", "name": "Master", "issues_found": []}, "master_output_analysis": {"master_track_guid": "MASTER_TRACK", "master_track_name": "Master", "issues_found": []}, "offline_media_analysis": {"offline_files": [], "missing_files": [{"file_path": "Media/SoF Guide.wav", "track_name": "SoF Guide", "item_name": "SoF Guide.wav", "take_name": "Take 1"}, {"file_path": "Media/SoF Guide.wav", "track_name": "SoF Guide", "item_name": "SoF Guide.wav", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav", "track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav", "track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav", "track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav", "track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems_ - stem - stem.wav", "track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems_ - stem.wav", "track_name": " - stem - stem", "item_name": " - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-001.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-001.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-001.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-001.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/U&Me_stems.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1027.wav", "track_name": "", "item_name": "02-231216_1027.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1029.wav", "track_name": "", "item_name": "02-231216_1029.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1030.wav", "track_name": "", "item_name": "02-231216_1030.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1024.wav", "track_name": "", "item_name": "02-231216_1024.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1029.wav", "track_name": "", "item_name": "02-231216_1029.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1038.wav", "track_name": "", "item_name": "02-231216_1038.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1037.wav", "track_name": "", "item_name": "02-231216_1037.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1036.wav", "track_name": "", "item_name": "02-231216_1036.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1032.wav", "track_name": "", "item_name": "02-231216_1032.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1033.wav", "track_name": "", "item_name": "02-231216_1033.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1038.wav", "track_name": "", "item_name": "02-231216_1038.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1048-02.wav", "track_name": "", "item_name": "02-231216_1048-02.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1046.wav", "track_name": "", "item_name": "02-231216_1046.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1048-01.wav", "track_name": "", "item_name": "02-231216_1048-01.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1045.wav", "track_name": "", "item_name": "02-231216_1045.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1048-02.wav", "track_name": "", "item_name": "02-231216_1048-02.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1051.wav", "track_name": "", "item_name": "02-231216_1051.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1050-01.wav", "track_name": "", "item_name": "02-231216_1050-01.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1050.wav", "track_name": "", "item_name": "02-231216_1050.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1050.wav", "track_name": "", "item_name": "02-231216_1050.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1048-02.wav", "track_name": "", "item_name": "02-231216_1048-02.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1058.wav", "track_name": "", "item_name": "02-231216_1058.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1057.wav", "track_name": "", "item_name": "02-231216_1057.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1058-01.wav", "track_name": "", "item_name": "02-231216_1058-01.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1056-02.wav", "track_name": "", "item_name": "02-231216_1056-02.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1056-01.wav", "track_name": "", "item_name": "02-231216_1056-01.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1059.wav", "track_name": "", "item_name": "02-231216_1059.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1059.wav", "track_name": "", "item_name": "02-231216_1059.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1133.wav", "track_name": "", "item_name": "02-231216_1133.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1133-02.wav", "track_name": "", "item_name": "02-231216_1133-02.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1135.wav", "track_name": "", "item_name": "02-231216_1135.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1135.wav", "track_name": "", "item_name": "02-231216_1135.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1133-02.wav", "track_name": "", "item_name": "02-231216_1133-02.wav", "take_name": "Take 1"}, {"file_path": "Media/02-231216_1135.wav", "track_name": "", "item_name": "02-231216_1135.wav", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-001.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-001.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-001.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-001.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1040.wav", "track_name": "", "item_name": "03-231216_1040.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1042-01.wav", "track_name": "", "item_name": "03-231216_1042-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1042.wav", "track_name": "", "item_name": "03-231216_1042.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1041.wav", "track_name": "", "item_name": "03-231216_1041.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1040-01.wav", "track_name": "", "item_name": "03-231216_1040-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1043.wav", "track_name": "", "item_name": "03-231216_1043.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1039.wav", "track_name": "", "item_name": "03-231216_1039.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1042.wav", "track_name": "", "item_name": "03-231216_1042.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1052-01.wav", "track_name": "", "item_name": "03-231216_1052-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1053.wav", "track_name": "", "item_name": "03-231216_1053.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1053-01.wav", "track_name": "", "item_name": "03-231216_1053-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1052.wav", "track_name": "", "item_name": "03-231216_1052.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1053.wav", "track_name": "", "item_name": "03-231216_1053.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1112.wav", "track_name": "", "item_name": "03-231216_1112.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1112-01.wav", "track_name": "", "item_name": "03-231216_1112-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1111-01.wav", "track_name": "", "item_name": "03-231216_1111-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1104.wav", "track_name": "", "item_name": "03-231216_1104.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1111.wav", "track_name": "", "item_name": "03-231216_1111.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1101-01.wav", "track_name": "", "item_name": "03-231216_1101-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1102.wav", "track_name": "", "item_name": "03-231216_1102.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1101.wav", "track_name": "", "item_name": "03-231216_1101.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1103-01.wav", "track_name": "", "item_name": "03-231216_1103-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1100-02.wav", "track_name": "", "item_name": "03-231216_1100-02.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1100.wav", "track_name": "", "item_name": "03-231216_1100.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1112-01.wav", "track_name": "", "item_name": "03-231216_1112-01.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1112.wav", "track_name": "", "item_name": "03-231216_1112.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1142.wav", "track_name": "", "item_name": "03-231216_1142.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1145.wav", "track_name": "", "item_name": "03-231216_1145.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1144.wav", "track_name": "", "item_name": "03-231216_1144.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1141.wav", "track_name": "", "item_name": "03-231216_1141.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1140.wav", "track_name": "", "item_name": "03-231216_1140.wav", "take_name": "Take 1"}, {"file_path": "Media/03-231216_1142.wav", "track_name": "", "item_name": "03-231216_1142.wav", "take_name": "Take 1"}, {"file_path": "Media/<PERSON>_stems_ - stem - stem-001.wav", "track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/<PERSON>_stems_ - stem - stem-001.wav", "track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/<PERSON>_stems_ - stem - stem-001.wav", "track_name": " - stem - stem - stem", "item_name": " - stem - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/<PERSON>_stems_ - stem-001.wav", "track_name": " - stem - stem", "item_name": " - stem - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-002.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-003.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1114-01.wav", "track_name": "", "item_name": "04-231216_1114-01.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1116.wav", "track_name": "", "item_name": "04-231216_1116.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1115.wav", "track_name": "", "item_name": "04-231216_1115.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1116.wav", "track_name": "", "item_name": "04-231216_1116.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1116-01.wav", "track_name": "", "item_name": "04-231216_1116-01.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1117.wav", "track_name": "", "item_name": "04-231216_1117.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1119-01.wav", "track_name": "", "item_name": "04-231216_1119-01.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1119-02.wav", "track_name": "", "item_name": "04-231216_1119-02.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1120.wav", "track_name": "", "item_name": "04-231216_1120.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1119.wav", "track_name": "", "item_name": "04-231216_1119.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1119-02.wav", "track_name": "", "item_name": "04-231216_1119-02.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1119.wav", "track_name": "", "item_name": "04-231216_1119.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1119-02.wav", "track_name": "", "item_name": "04-231216_1119-02.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1129.wav", "track_name": "", "item_name": "04-231216_1129.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1129-01.wav", "track_name": "", "item_name": "04-231216_1129-01.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1128.wav", "track_name": "", "item_name": "04-231216_1128.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1131.wav", "track_name": "", "item_name": "04-231216_1131.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1128-01.wav", "track_name": "", "item_name": "04-231216_1128-01.wav", "take_name": "Take 1"}, {"file_path": "Media/04-231216_1130.wav", "track_name": "", "item_name": "04-231216_1130.wav", "take_name": "Take 1"}, {"file_path": "Media/Claire <PERSON>_stems-003.wav", "track_name": " - stem", "item_name": " - stem", "take_name": "Take 1"}, {"file_path": "Media/05-231216_1125.wav", "track_name": "", "item_name": "05-231216_1125.wav", "take_name": "Take 1"}, {"file_path": "Media/05-231216_1126.wav", "track_name": "", "item_name": "05-231216_1126.wav", "take_name": "Take 1"}, {"file_path": "Media/05-231216_1124.wav", "track_name": "", "item_name": "05-231216_1124.wav", "take_name": "Take 1"}, {"file_path": "Media/05-231216_1122.wav", "track_name": "", "item_name": "05-231216_1122.wav", "take_name": "Take 1"}, {"file_path": "Media/05-231216_1122.wav", "track_name": "", "item_name": "05-231216_1122.wav", "take_name": "Take 1"}, {"file_path": "Media/05-231216_1124.wav", "track_name": "", "item_name": "05-231216_1124.wav", "take_name": "Take 1"}, {"file_path": "Media/05-231216_1122.wav", "track_name": "", "item_name": "05-231216_1122.wav", "take_name": "Take 1"}, {"file_path": "Media/05-231216_1124.wav", "track_name": "", "item_name": "05-231216_1124.wav", "take_name": "Take 1"}, {"file_path": "Media/AndyM2.WAV", "track_name": "AndyM2", "item_name": "AndyM2.WAV", "take_name": "Take 1"}, {"file_path": "Media/AndyM2.WAV", "track_name": "AndyM2", "item_name": "AndyM2.WAV", "take_name": "Take 1"}, {"file_path": "Media/AndyM_lead1.WAV", "track_name": "AndyM_lead2", "item_name": "AndyM_lead1.WAV", "take_name": "Take 1"}, {"file_path": "Media/AndyM_lead1.WAV", "track_name": "AndyM_lead2", "item_name": "AndyM_lead1.WAV", "take_name": "Take 1"}, {"file_path": "Media/AndyM_lead1.WAV", "track_name": "AndyM_lead2", "item_name": "AndyM_lead1.WAV", "take_name": "Take 1"}, {"file_path": "Media/AndyM_lead1.WAV", "track_name": "AndyM_lead2", "item_name": "AndyM_lead1.WAV", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-001.wav", "track_name": "U&ME AndyG Rhythm-001", "item_name": "U&ME AndyG Rhythm-001.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-001.wav", "track_name": "U&ME AndyG Rhythm-001", "item_name": "U&ME AndyG Rhythm-001.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-002.wav", "track_name": "U&ME AndyG Rhythm-002", "item_name": "U&ME AndyG Rhythm-002.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-002.wav", "track_name": "U&ME AndyG Rhythm-002", "item_name": "U&ME AndyG Rhythm-002.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-002.wav", "track_name": "U&ME AndyG Rhythm-002", "item_name": "U&ME AndyG Rhythm-002.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-002.wav", "track_name": "U&ME AndyG Rhythm-002", "item_name": "U&ME AndyG Rhythm-002.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-003.wav", "track_name": "U&ME AndyG Rhythm-003", "item_name": "U&ME AndyG Rhythm-003.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-005.wav", "track_name": "U&ME AndyG Rhythm-005", "item_name": "U&ME AndyG Rhythm-005.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-005.wav", "track_name": "U&ME AndyG Rhythm-005", "item_name": "U&ME AndyG Rhythm-005.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-005.wav", "track_name": "U&ME AndyG Rhythm-005", "item_name": "U&ME AndyG Rhythm-005.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-006.wav", "track_name": "U&ME AndyG Rhythm-006", "item_name": "U&ME AndyG Rhythm-006.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-006.wav", "track_name": "U&ME AndyG Rhythm-006", "item_name": "U&ME AndyG Rhythm-006.wav", "take_name": "Take 1"}, {"file_path": "Media/U&ME AndyG Rhythm-006.wav", "track_name": "U&ME AndyG Rhythm-006", "item_name": "U&ME AndyG Rhythm-006.wav", "take_name": "Take 1"}], "total_media_files": 144, "total_offline": 0, "total_missing": 144}, "session_hygiene_analysis": {"project_level": ["Project is missing markers, which can help with navigation and song structure.", "Project is missing regions, which can be useful for selections and exports.", "Project notes are empty."], "track_level": [{"track_guid": "{CE762FAB-33C5-9842-8B41-67867E5DF587}", "track_name": " - stem - stem - stem", "issues": ["Track name ' - stem - stem - stem' is duplicated in the project."]}, {"track_guid": "{F763906E-9544-AE4D-9CA7-0D35CCF3078D}", "track_name": " - stem - stem", "issues": ["Track name ' - stem - stem' is duplicated in the project."]}, {"track_guid": "{BF0C1A62-39A6-B447-AC95-CC7573CC7B92}", "track_name": " - stem", "issues": ["Track name ' - stem' is duplicated in the project."]}, {"track_guid": "{7B0BD668-DAAE-2240-9EE7-C9F54986151E}", "track_name": " - stem", "issues": ["Track name ' - stem' is duplicated in the project."]}, {"track_guid": "{00B65410-DDEF-E541-981A-C77D12A2F515}", "track_name": " - stem", "issues": ["Track name ' - stem' is duplicated in the project."]}, {"track_guid": "{3A259BC7-FC8F-5D48-8312-BD8A75B0446D}", "track_name": " - stem - stem - stem", "issues": ["Track name ' - stem - stem - stem' is duplicated in the project."]}, {"track_guid": "{EAD44D13-84C3-584E-AC86-E345180F997B}", "track_name": " - stem - stem", "issues": ["Track name ' - stem - stem' is duplicated in the project."]}, {"track_guid": "{1A7BDA4B-B3ED-0445-9C67-07C817E78892}", "track_name": " - stem", "issues": ["Track name ' - stem' is duplicated in the project."]}, {"track_guid": "{3C66D644-F711-9043-A0BE-6390DC7AC961}", "track_name": " - stem", "issues": ["Track name ' - stem' is duplicated in the project."]}]}, "file_reference_analysis": {"summary_messages": ["✅ No problematic file references detected based on path patterns and metadata."], "problematic_files": []}, "item_property_analysis": [{"item_guid": "{32727495-502C-A948-903F-5E8D24349752}", "item_name": "SoF Guide.wav", "track_guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "track_name": "SoF Guide", "issues": ["Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "item_name": "SoF Guide.wav", "track_guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "track_name": "SoF Guide", "issues": ["Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{A7D402EB-BC38-8C45-88CA-C263840882B6}", "item_name": " - stem - stem - stem", "track_guid": "{CE762FAB-33C5-9842-8B41-67867E5DF587}", "track_name": " - stem - stem - stem", "issues": ["Item ' - stem - stem - stem' on track ' - stem - stem - stem' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{8DBCBE14-1BBB-684F-A401-58BC62EF751E}", "item_name": " - stem - stem - stem", "track_guid": "{CE762FAB-33C5-9842-8B41-67867E5DF587}", "track_name": " - stem - stem - stem", "issues": ["Item ' - stem - stem - stem' on track ' - stem - stem - stem' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{034B68F1-7000-884D-8F0D-1A00D4F7A69A}", "item_name": " - stem - stem", "track_guid": "{F763906E-9544-AE4D-9CA7-0D35CCF3078D}", "track_name": " - stem - stem", "issues": ["Item ' - stem - stem' on track ' - stem - stem' has an abrupt start (short/no fade-in).", "Item ' - stem - stem' on track ' - stem - stem' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{A7683185-BC6E-6344-A501-8A824A4ADAAE}", "item_name": " - stem", "track_guid": "{BF0C1A62-39A6-B447-AC95-CC7573CC7B92}", "track_name": " - stem", "issues": ["Item ' - stem' on track ' - stem' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{2C1EEDBB-ED16-EC4C-8F7C-4A0B9BDF574A}", "item_name": " - stem", "track_guid": "{7B0BD668-DAAE-2240-9EE7-C9F54986151E}", "track_name": " - stem", "issues": ["Item ' - stem' on track ' - stem' has an abrupt start (short/no fade-in).", "Item ' - stem' on track ' - stem' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{E157623C-9243-4A40-BB5F-81E2F5708F48}", "item_name": "02-231216_1051.wav", "track_guid": "{27EED9ED-98B9-4B47-81AF-AC2D6CF9082B}", "track_name": "", "issues": ["Item '02-231216_1051.wav' on track '' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{4D652367-B038-1E49-966C-7789D3254821}", "item_name": " - stem", "track_guid": "{00B65410-DDEF-E541-981A-C77D12A2F515}", "track_name": " - stem", "issues": ["Item ' - stem' on track ' - stem' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{11BA9B4A-96B0-0B41-94C2-298E96A22F69}", "item_name": "03-231216_1102.wav", "track_guid": "{76D6A850-BABE-FC49-97D4-A34ABE7B954D}", "track_name": "", "issues": ["Item '03-231216_1102.wav' on track '' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{C81D7A3A-1910-4C4C-B42C-7D1219B1A9E3}", "item_name": " - stem - stem - stem", "track_guid": "{3A259BC7-FC8F-5D48-8312-BD8A75B0446D}", "track_name": " - stem - stem - stem", "issues": ["Item ' - stem - stem - stem' on track ' - stem - stem - stem' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{FA17DC73-2AD6-B844-B70A-AC479B214A50}", "item_name": " - stem - stem - stem", "track_guid": "{3A259BC7-FC8F-5D48-8312-BD8A75B0446D}", "track_name": " - stem - stem - stem", "issues": ["Item ' - stem - stem - stem' on track ' - stem - stem - stem' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{36BAFB98-E344-834E-8606-6CF9CF367A3A}", "item_name": " - stem - stem", "track_guid": "{EAD44D13-84C3-584E-AC86-E345180F997B}", "track_name": " - stem - stem", "issues": ["Item ' - stem - stem' on track ' - stem - stem' has an abrupt start (short/no fade-in).", "Item ' - stem - stem' on track ' - stem - stem' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{7CB00E84-C7CD-D740-BC5D-9E28FE4894DF}", "item_name": " - stem", "track_guid": "{1A7BDA4B-B3ED-0445-9C67-07C817E78892}", "track_name": " - stem", "issues": ["Item ' - stem' on track ' - stem' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{738AD330-A407-5844-9286-E3A2AA6217A9}", "item_name": " - stem", "track_guid": "{1A7BDA4B-B3ED-0445-9C67-07C817E78892}", "track_name": " - stem", "issues": ["Item ' - stem' on track ' - stem' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{9289D69B-84FD-574F-AD61-A6D40AADED3E}", "item_name": " - stem", "track_guid": "{3C66D644-F711-9043-A0BE-6390DC7AC961}", "track_name": " - stem", "issues": ["Item ' - stem' on track ' - stem' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{12A493F7-D35C-6645-AEC5-189CAB154507}", "item_name": "AndyM_lead1.WAV", "track_guid": "{E083B37E-F441-2D44-8101-3490AB21A419}", "track_name": "AndyM_lead2", "issues": ["Item 'AndyM_lead1.WAV' on track 'AndyM_lead2' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{441E2813-D88F-C04D-A2EE-7D80A1B5D642}", "item_name": "U&ME AndyG Rhythm-001.wav", "track_guid": "{69745E54-8B31-2744-9EF1-E580DFFA7704}", "track_name": "U&ME AndyG Rhythm-001", "issues": ["Item 'U&ME AndyG Rhythm-001.wav' on track 'U&ME AndyG Rhythm-001' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{E5B53FD8-165E-DF44-AFAC-8ED7D7FB3D7B}", "item_name": "U&ME AndyG Rhythm-001.wav", "track_guid": "{69745E54-8B31-2744-9EF1-E580DFFA7704}", "track_name": "U&ME AndyG Rhythm-001", "issues": ["Item 'U&ME AndyG Rhythm-001.wav' on track 'U&ME AndyG Rhythm-001' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{BDA13D27-B523-3D48-B365-AD84F30A42B8}", "item_name": "U&ME AndyG Rhythm-002.wav", "track_guid": "{1EAA850B-0B91-144F-BFA4-DCD34C9C737C}", "track_name": "U&ME AndyG Rhythm-002", "issues": ["Item 'U&ME AndyG Rhythm-002.wav' on track 'U&ME AndyG Rhythm-002' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{881E10D2-6B12-8B4E-9F6F-25D60DBDB180}", "item_name": "U&ME AndyG Rhythm-003.wav", "track_guid": "{2596D320-CD4B-CB4B-B260-928F1E17DB1F}", "track_name": "U&ME AndyG Rhythm-003", "issues": ["Item 'U&ME AndyG Rhythm-003.wav' on track 'U&ME AndyG Rhythm-003' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{9A490AA4-C7A1-FD4D-9398-349CEDB07163}", "item_name": "U&ME AndyG Rhythm-005.wav", "track_guid": "{4463CF5A-2627-464D-9856-2360CFB4980F}", "track_name": "U&ME AndyG Rhythm-005", "issues": ["Item 'U&ME AndyG Rhythm-005.wav' on track 'U&ME AndyG Rhythm-005' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{866417B2-02D5-DC40-ADF4-A224AD507D9F}", "item_name": "U&ME AndyG Rhythm-006.wav", "track_guid": "{1CA630D9-E181-F643-8303-55FE350911C0}", "track_name": "U&ME AndyG Rhythm-006", "issues": ["Item 'U&ME AndyG Rhythm-006.wav' on track 'U&ME AndyG Rhythm-006' has an abrupt end (short/no fade-out)."]}], "project_settings_analysis": {"bit_depth": {"value": null, "is_suboptimal": false}, "sample_rate": {"value": 44100, "is_nonstandard": false}, "has_incomplete_info": true, "first_item_abrupt_start_at_zero": true, "stereo_balance_issue": null}, "track_specific_issues": [{"guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide", "issues": ["Track 'SoF Guide' is muted."]}, {"guid": "{F763906E-9544-AE4D-9CA7-0D35CCF3078D}", "name": " - stem - stem", "issues": ["Track ' - stem - stem' is muted."]}, {"guid": "{BF0C1A62-39A6-B447-AC95-CC7573CC7B92}", "name": " - stem", "issues": ["Track ' - stem' is muted."]}, {"guid": "{27EED9ED-98B9-4B47-81AF-AC2D6CF9082B}", "name": "", "issues": ["Track '' is muted."]}, {"guid": "{76D6A850-BABE-FC49-97D4-A34ABE7B954D}", "name": "", "issues": ["Track '' is muted."]}, {"guid": "{EAD44D13-84C3-584E-AC86-E345180F997B}", "name": " - stem - stem", "issues": ["Track ' - stem - stem' is muted."]}, {"guid": "{1A7BDA4B-B3ED-0445-9C67-07C817E78892}", "name": " - stem", "issues": ["Track ' - stem' is muted."]}, {"guid": "{2A703094-5F3A-1242-837C-8B9485143B22}", "name": "", "issues": ["Track '' is muted."]}, {"guid": "{5EE45C4B-6F5B-A748-9C54-0B218C3FE5E6}", "name": "", "issues": ["Track '' is muted."]}]}, "issues": [{"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "MISSING_MEDIA_FILE-1", "rule_id": "MISSING_MEDIA_FILE", "category": "Media Files", "severity": "critical", "message": "Missing media file: 'Media/SoF Guide.wav' in take 'Take 1' on track 'SoF Guide'", "is_mastering_critical": true, "affected_elements": [{"type": "take", "guid": "{DEFAULT-TAKE-GUID-{32727495-502C-A948-903F-5E8D24349752}-0}", "name": "Take 1", "details": {"track_name": "SoF Guide", "item_name": "SoF Guide.wav", "file_path": "Media/SoF Guide.wav"}}], "recommendation": "Locate and reconnect missing media files. Consider consolidating project media into the project directory."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-158", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt start (short/no fade-in).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{32727495-502C-A948-903F-5E8D24349752}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-159", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'SoF Guide.wav' on track 'SoF Guide' has an abrupt end (short/no fade-out).", "is_mastering_critical": true, "affected_elements": [{"type": "item", "guid": "{E273D894-4E61-2E48-A5D8-8DB3C3FB789A}", "name": "SoF Guide.wav", "details": {"track_name": "SoF Guide"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-186", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'SoF Guide' is muted.", "is_mastering_critical": true, "affected_elements": [{"type": "track", "guid": "{F1669967-3790-CA4B-BB13-F817D56B3F17}", "name": "SoF Guide"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "PROJECT_HAS_MISSING_MEDIA-145", "rule_id": "PROJECT_HAS_MISSING_MEDIA", "category": "Media Files", "severity": "critical", "message": "Project contains 144 missing media file(s)", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Locate and reconnect all missing media files. Missing media will result in silent gaps in the audio."}, {"id": "PROJECT_INCOMPLETE_INFO-184", "rule_id": "PROJECT_INCOMPLETE_INFO", "category": "Project Settings", "severity": "info", "message": "Project metadata (e.g., title) is incomplete.", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Consider filling in project title in REAPER's project settings for better organization."}, {"id": "PROJECT_ABRUPT_START_AT_ZERO-185", "rule_id": "PROJECT_ABRUPT_START_AT_ZERO", "category": "Project Structure", "severity": "warning", "message": "The first item in the project starts at 0:00 with an abrupt (short/no) fade-in.", "is_mastering_critical": true, "affected_elements": [{"type": "project"}], "recommendation": "Ensure there's adequate pre-roll or a gentle fade-in at the project start to avoid clicks or immediate transients."}]}