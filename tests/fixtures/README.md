# Test Fixtures

This directory contains test fixture files used for testing the RPP Analyser.

## Available Test Fixtures

### parameter_test

A REAPER project designed to test various parameter extraction and handling
capabilities:

-   **File**: `parameter_test/parameter_test.RPP`
-   **Purpose**: Test the extraction and processing of various plugin parameters
    and settings
-   **Features**:
    -   Contains a variety of plugin types (VST, VST3, VST3i, etc.)
    -   Includes plugins with different parameter configurations
    -   Demonstrates both instrument and effect plugins

## Adding New Test Fixtures

When adding new test fixtures to this directory:

1. Create a self-contained subdirectory for each test case
2. Include all necessary files (project files, media, etc.)
3. Update this README.md with information about the new fixture
4. Consider adding a brief description of what particular aspect of the system
   the fixture is designed to test

## Usage in Tests

You can load these fixtures in your test code by using a path relative to the
project root, for example:

```python
# Python example
from pathlib import Path

def test_parameter_extraction():
    test_file_path = Path("tests/fixtures/parameter_test/parameter_test.RPP")
    # Test code using the fixture...
```

For testing the frontend without the backend, you might want to generate JSON
representations of these projects.
