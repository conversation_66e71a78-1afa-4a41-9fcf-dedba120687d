{"overall_health_score": 0, "genre": "general", "strict_mode": false, "summary_metrics": {"critical_issues_count": 0, "warning_issues_count": 44, "info_issues_count": 13, "passed_checks_count": 0}, "issues": [{"id": "PLUGIN_BLACKLISTED_MASTER-1", "rule_id": "PLUGIN_BLACKLISTED_MASTER", "category": "Plugin Analysis - Master Bus", "severity": "warning", "message": "Plugin 'AU: Ozone 9 Imager (iZotope)' on the Master bus is not recommended for final mastering.", "affected_elements": [{"type": "plugin", "guid": null, "name": "AU: Ozone 9 Imager (iZotope)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Critically review 'AU: Ozone 9 Imager (iZotope)'. Consider bypassing or removing it before sending to mastering, or ensure it's used with extreme care."}, {"id": "PLUGIN_BYPASSED_BLACKLISTED_MASTER-2", "rule_id": "PLUGIN_BYPASSED_BLACKLISTED_MASTER", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Bypassed plugin 'VST3: Pro-L 2 (FabFilter)' on the Master bus would be blacklisted if active.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "If 'VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)' is reactivated on the master bus, review its suitability carefully. Consider removing if no longer needed."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-3", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Guitar AM Lead L' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Guitar AM Lead L", "track_guid": "{0B4A92D0-EE8C-194E-9F7D-10A5CE79525F}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-4", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Guitar AM Lead R' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Guitar AM Lead R", "track_guid": "{BD77FF2D-F7EB-4E40-813A-8BC4AE70ACFF}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-5", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Guitar AndyG Rhythm-001' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Guitar AndyG Rhythm-001", "track_guid": "{1A2D39A4-E390-C245-8B7A-A506C3D0794B}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-6", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Git AndyG Rhythm-002' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Git AndyG Rhythm-002", "track_guid": "{3793A8C4-8572-2C49-9470-3CD327463DB8}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-7", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Run and Hide-002 AndyG filthy harry - stem' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Run and Hide-002 AndyG filthy harry - stem", "track_guid": "{480476E1-67D4-6946-8715-BA94BE09F1FA}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-8", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Guitar Chug' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Guitar Chug", "track_guid": "{5FEB6651-56A3-614E-A92E-9780667A3384}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-9", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Vocals CF Lead' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Vocals CF Lead", "track_guid": "{3EBE6CDC-A60F-8048-9984-548C6D6207EF}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-10", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Vocals CF BV' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Vocals CF BV", "track_guid": "{A8AEF14E-0AF0-F34E-B65C-1759342B1A3C}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-11", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'CF Vs BVS Vocals' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "CF Vs BVS Vocals", "track_guid": "{A2443703-F9BE-6B4C-9A3C-AFCD5F96A005}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "PLUGIN_BLACKLISTED_GENERAL-12", "rule_id": "PLUGIN_BLACKLISTED_GENERAL", "category": "Plugin Analysis", "severity": "warning", "message": "Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Vocals CF Double' is generally not recommended for mastering or final mixdowns.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "details": {"track_name": "Vocals CF Double", "track_guid": "{D57D8F01-66E4-9146-834F-9AA435AB504A}"}}], "recommendation": "Consider bypassing or removing 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' or replacing it with an alternative suitable for mastering."}, {"id": "HYGIENE_MISSING_REGIONS-13", "rule_id": "HYGIENE_MISSING_REGIONS", "category": "Session Hygiene", "severity": "info", "message": "Project is missing regions, which can be useful for selections and exports.", "affected_elements": [{"type": "project"}], "recommendation": "Consider adding regions for song sections or export areas."}, {"id": "HYGIENE_MISSING_NOTES-14", "rule_id": "HYGIENE_MISSING_NOTES", "category": "Session Hygiene", "severity": "info", "message": "Project notes are empty.", "affected_elements": [{"type": "project"}], "recommendation": "Consider adding project notes for mix versions, collaborators, or other relevant info."}, {"id": "HYGIENE_NOT_IN_FOLDER-15", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Bass' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{02823BF1-41A7-D14C-A3A8-FE93C6C1E2A2}", "name": "Bass"}], "recommendation": "Consider organizing track 'Bass' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-16", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Drums' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{1F2980C9-B24F-744B-8B4D-9D4C9764F06C}", "name": "Drums"}], "recommendation": "Consider organizing track 'Drums' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-17", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'FX - Riser' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{0B850461-A035-B044-997B-EC24D7274DA3}", "name": "FX - Riser"}], "recommendation": "Consider organizing track 'FX - Riser' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_DUPLICATE_TRACK_NAME-18", "rule_id": "HYGIENE_DUPLICATE_TRACK_NAME", "category": "Session Hygiene", "severity": "warning", "message": "Track name 'FX - Riser' is duplicated in the project.", "affected_elements": [{"type": "track", "guid": "{0B850461-A035-B044-997B-EC24D7274DA3}", "name": "FX - Riser"}], "recommendation": "Rename track 'FX - Riser' to ensure all track names are unique for clarity."}, {"id": "HYGIENE_NOT_IN_FOLDER-19", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'FX - Riser' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{34C9C510-A077-F34D-8C70-A3E969B23AEC}", "name": "FX - Riser"}], "recommendation": "Consider organizing track 'FX - Riser' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_DUPLICATE_TRACK_NAME-20", "rule_id": "HYGIENE_DUPLICATE_TRACK_NAME", "category": "Session Hygiene", "severity": "warning", "message": "Track name 'FX - Riser' is duplicated in the project.", "affected_elements": [{"type": "track", "guid": "{34C9C510-A077-F34D-8C70-A3E969B23AEC}", "name": "FX - Riser"}], "recommendation": "Rename track 'FX - Riser' to ensure all track names are unique for clarity."}, {"id": "HYGIENE_NOT_IN_FOLDER-21", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Room Verb' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{41D5B075-9459-AC43-BA8A-7746BB8D7D59}", "name": "Room Verb"}], "recommendation": "Consider organizing track 'Room Verb' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-22", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Guitar Delay' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{8EA3F33B-9A64-0043-B7C1-529261456CAF}", "name": "Guitar Delay"}], "recommendation": "Consider organizing track 'Guitar Delay' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-23", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Slap Delay' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{99037C75-5016-884B-B1DA-256073D112CD}", "name": "<PERSON><PERSON>"}], "recommendation": "Consider organizing track 'Slap Delay' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-24", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Vox Long Delay' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{C4858C29-4147-A34F-96C1-D9E69A0868EA}", "name": "Vox Long Delay"}], "recommendation": "Consider organizing track 'Vox Long Delay' into a folder if appropriate for the project structure."}, {"id": "ITEM_ABRUPT_START-25", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'EZbass Track 1 Stereo Mix (2).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{D6549952-E128-DE4F-8DE3-13D2AE8093E9}", "name": "EZbass Track 1 Stereo Mix (2).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-26", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H AndyM_Lead2.wav' on track 'Guitar AM Lead L' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{5CB66E81-3083-4649-A3D5-8C431386F07F}", "name": "R&H AndyM_Lead2.wav", "details": {"track_name": "Guitar AM Lead L"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-27", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H AndyM_Lead2-imported.wav' on track 'Guitar AM Lead R' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{50B32E29-C0DA-F346-A24E-F5300A65EAD4}", "name": "R&H AndyM_Lead2-imported.wav", "details": {"track_name": "Guitar AM Lead R"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-28", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H AndyM1.wav' on track 'Guitar AndyG Rhythm-001' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{8E2F1349-F44D-BA40-B0EB-7227FD66FC15}", "name": "R&H AndyM1.wav", "details": {"track_name": "Guitar AndyG Rhythm-001"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-29", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H Run and Hide-001 AndyG dirty harry.wav' on track 'Git AndyG Rhythm-002' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{2AB349BD-61C9-D141-8B84-3F4AAC8BBBD0}", "name": "R&H Run and Hide-001 AndyG dirty harry.wav", "details": {"track_name": "Git AndyG Rhythm-002"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-30", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H Run and Hide-001 AndyG dirty harry.wav' on track 'Git AndyG Rhythm-002' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{F5A47BDE-E9B9-7449-8A5E-4A7710CA2D0B}", "name": "R&H Run and Hide-001 AndyG dirty harry.wav", "details": {"track_name": "Git AndyG Rhythm-002"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_END-31", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item 'Run and Hide-002 AndyG filthy harry.wav' on track 'Run and Hide-002 AndyG filthy harry' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{2C92BA29-94D3-7242-9D70-E33DEF8B2026}", "name": "Run and Hide-002 AndyG filthy harry.wav", "details": {"track_name": "Run and Hide-002 Andy<PERSON> filthy harry"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-32", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'r&h edits_stems_AndyM5-001.wav' on track 'Guitar Chug' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{*************-0E44-9962-C4BC6DEA2287}", "name": "r&h edits_stems_AndyM5-001.wav", "details": {"track_name": "Guitar Chug"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-33", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item 'r&h edits_stems_AndyM5-001.wav' on track 'Guitar Chug' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{*************-0E44-9962-C4BC6DEA2287}", "name": "r&h edits_stems_AndyM5-001.wav", "details": {"track_name": "Guitar Chug"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-34", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'r&h edits_stems_AndyM5-001.wav' on track 'Guitar Chug' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{20113E83-ABB8-E546-BF2D-054F1F054AE9}", "name": "r&h edits_stems_AndyM5-001.wav", "details": {"track_name": "Guitar Chug"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-35", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item 'r&h edits_stems_AndyM5-001.wav' on track 'Guitar Chug' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{20113E83-ABB8-E546-BF2D-054F1F054AE9}", "name": "r&h edits_stems_AndyM5-001.wav", "details": {"track_name": "Guitar Chug"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-36", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H  - stem - stem-001.wav' on track 'Vocals CF Lead' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{36A167F5-**************-B56CA5AD511B}", "name": "R&H  - stem - stem-001.wav", "details": {"track_name": "Vocals CF Lead"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-37", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H  - stem - stem-001.wav' on track 'Vocals CF Lead' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{1C2D524D-F395-974F-80A5-B982D263FED5}", "name": "R&H  - stem - stem-001.wav", "details": {"track_name": "Vocals CF Lead"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-38", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'r&hvox_stems.wav' on track 'r&hvox_stems' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{DD649090-192B-144C-A5A8-20B05AD6D801}", "name": "r&hvox_stems.wav", "details": {"track_name": "r&hvox_stems"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-39", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item 'r&hvox_stems.wav' on track 'r&hvox_stems' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{C6B61B93-69BE-3242-9DD7-43D3897D4AE3}", "name": "r&hvox_stems.wav", "details": {"track_name": "r&hvox_stems"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-40", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H  - stem - stem-002.wav' on track 'Vocals CF Double' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{1409E32F-8AA3-C142-A4D8-02F2C481D9B3}", "name": "R&H  - stem - stem-002.wav", "details": {"track_name": "Vocals CF Double"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-41", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item 'R&H  - stem - stem-002.wav' on track 'Vocals CF Double' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{1409E32F-8AA3-C142-A4D8-02F2C481D9B3}", "name": "R&H  - stem - stem-002.wav", "details": {"track_name": "Vocals CF Double"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-42", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{12B980ED-65D7-744F-A44F-5C6F84B8E736}", "name": "14-FX - Riser-MIDI", "details": {"track_name": "FX - Riser"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-43", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{12B980ED-65D7-744F-A44F-5C6F84B8E736}", "name": "14-FX - Riser-MIDI", "details": {"track_name": "FX - Riser"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-44", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{978455CB-1FF1-B74D-A332-CD9FA0426208}", "name": "14-FX - Riser-MIDI", "details": {"track_name": "FX - Riser"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-45", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{978455CB-1FF1-B74D-A332-CD9FA0426208}", "name": "14-FX - Riser-MIDI", "details": {"track_name": "FX - Riser"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-46", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{2FA57FE2-DB13-2A4A-82BD-B7B4AD195840}", "name": "14-FX - Riser-MIDI", "details": {"track_name": "FX - Riser"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-47", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{2FA57FE2-DB13-2A4A-82BD-B7B4AD195840}", "name": "14-FX - Riser-MIDI", "details": {"track_name": "FX - Riser"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-48", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "warning", "message": "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{E510AD53-E038-8245-9F6E-5E5D63BBF61C}", "name": "14-FX - Riser-MIDI", "details": {"track_name": "FX - Riser"}}], "recommendation": "Consider adding a short fade-in (e.g., 2-10ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-49", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "warning", "message": "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{E510AD53-E038-8245-9F6E-5E5D63BBF61C}", "name": "14-FX - Riser-MIDI", "details": {"track_name": "FX - Riser"}}], "recommendation": "Consider adding a short fade-out (e.g., 2-10ms) to avoid potential clicks or abrupt cuts."}, {"id": "PROJECT_INCOMPLETE_INFO-50", "rule_id": "PROJECT_INCOMPLETE_INFO", "category": "Project Settings", "severity": "info", "message": "Project metadata (e.g., title) is incomplete.", "affected_elements": [{"type": "project"}], "recommendation": "Consider filling in project title in REAPER's project settings for better organization."}, {"id": "PROJECT_ABRUPT_START_AT_ZERO-51", "rule_id": "PROJECT_ABRUPT_START_AT_ZERO", "category": "Project Structure", "severity": "warning", "message": "The first item in the project starts at 0:00 with an abrupt (short/no) fade-in.", "affected_elements": [{"type": "project"}], "recommendation": "Ensure there's adequate pre-roll or a gentle fade-in at the project start to avoid clicks or immediate transients."}, {"id": "TRACK_BYPASSED_FX-52", "rule_id": "TRACK_BYPASSED_FX", "category": "Track Settings", "severity": "info", "message": "Track 'Master' has bypassed FX in its chain: VST3: Gullfoss (Soundtheory), VST3: Spectre (Wavesfactory), AU: elysia karacter master (Plugin Alliance), VST3: Pro-L 2 (FabFilter).", "affected_elements": [{"type": "track", "guid": "MASTER_TRACK", "name": "Master", "details": {"bypassed_plugins": ["VST3: <PERSON><PERSON><PERSON>ss (Soundtheory)", "VST3: Spectre (Wavesfactory)", "AU: el<PERSON><PERSON> master (Plugin Alliance)", "VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)"]}}], "recommendation": "Review bypassed plugins to ensure they are intentionally inactive."}, {"id": "TRACK_MUTED-53", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'Run and Hide-002 AndyG filthy harry' is muted.", "affected_elements": [{"type": "track", "guid": "{27992955-5A8D-3A4D-83BF-C5C0A0CB832F}", "name": "Run and Hide-002 Andy<PERSON> filthy harry"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-54", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'Vocals CF BV' is muted.", "affected_elements": [{"type": "track", "guid": "{A8AEF14E-0AF0-F34E-B65C-1759342B1A3C}", "name": "Vocals CF BV"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-55", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'CF Vs BVS Vocals' is muted.", "affected_elements": [{"type": "track", "guid": "{A2443703-F9BE-6B4C-9A3C-AFCD5F96A005}", "name": "CF Vs BVS Vocals"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-56", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'r&hvox_stems' is muted.", "affected_elements": [{"type": "track", "guid": "{580CE93D-3C75-E141-9151-238E1916CAF7}", "name": "r&hvox_stems"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}, {"id": "TRACK_MUTED-57", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track 'FX - Riser' is muted.", "affected_elements": [{"type": "track", "guid": "{0B850461-A035-B044-997B-EC24D7274DA3}", "name": "FX - Riser"}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}], "detailed_analysis": {"plugin_analysis": [{"track_guid": "MASTER_TRACK", "track_name": "Master", "plugins": [{"plugin_guid": null, "plugin_name": "AU: Ozone 9 Imager (iZotope)", "is_bypassed": false, "category": "Mastering Suite", "issues": ["Plugin 'AU: Ozone 9 Imager (iZotope)' on the Master bus is not recommended for final mastering."]}, {"plugin_guid": null, "plugin_name": "VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "is_bypassed": true, "category": "Limiter", "issues": ["Bypassed plugin 'VST3: Pro-L 2 (FabFilter)' on the Master bus would be blacklisted if active."]}]}, {"track_guid": "{0B4A92D0-EE8C-194E-9F7D-10A5CE79525F}", "track_name": "Guitar AM Lead L", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Guitar AM Lead L' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{BD77FF2D-F7EB-4E40-813A-8BC4AE70ACFF}", "track_name": "Guitar AM Lead R", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Guitar AM Lead R' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{1A2D39A4-E390-C245-8B7A-A506C3D0794B}", "track_name": "Guitar AndyG Rhythm-001", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Guitar AndyG Rhythm-001' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{3793A8C4-8572-2C49-9470-3CD327463DB8}", "track_name": "Git AndyG Rhythm-002", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Git AndyG Rhythm-002' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{480476E1-67D4-6946-8715-BA94BE09F1FA}", "track_name": "Run and Hide-002 AndyG filthy harry - stem", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Run and Hide-002 AndyG filthy harry - stem' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{5FEB6651-56A3-614E-A92E-9780667A3384}", "track_name": "Guitar Chug", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Guitar Chug' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{3EBE6CDC-A60F-8048-9984-548C6D6207EF}", "track_name": "Vocals CF Lead", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Vocals CF Lead' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{A8AEF14E-0AF0-F34E-B65C-1759342B1A3C}", "track_name": "Vocals CF BV", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Vocals CF BV' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{A2443703-F9BE-6B4C-9A3C-AFCD5F96A005}", "track_name": "CF Vs BVS Vocals", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'CF Vs BVS Vocals' is generally not recommended for mastering or final mixdowns."]}]}, {"track_guid": "{D57D8F01-66E4-9146-834F-9AA435AB504A}", "track_name": "Vocals CF Double", "plugins": [{"plugin_guid": null, "plugin_name": "VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Limiter", "issues": ["Plugin 'VST3: UADx 1176 Rev A Compressor (Universal Audio (UADx))' on track 'Vocals CF Double' is generally not recommended for mastering or final mixdowns."]}]}], "master_bus_analysis": {"guid": "MASTER_TRACK", "name": "Master", "issues_found": []}, "session_hygiene_analysis": {"project_level": ["Project is missing regions, which can be useful for selections and exports.", "Project notes are empty."], "track_level": [{"track_guid": "{02823BF1-41A7-D14C-A3A8-FE93C6C1E2A2}", "track_name": "Bass", "issues": ["Track 'Bass' is not part of any folder structure."]}, {"track_guid": "{1F2980C9-B24F-744B-8B4D-9D4C9764F06C}", "track_name": "Drums", "issues": ["Track 'Drums' is not part of any folder structure."]}, {"track_guid": "{0B850461-A035-B044-997B-EC24D7274DA3}", "track_name": "FX - Riser", "issues": ["Track 'FX - Riser' is not part of any folder structure.", "Track name 'FX - Riser' is duplicated in the project."]}, {"track_guid": "{34C9C510-A077-F34D-8C70-A3E969B23AEC}", "track_name": "FX - Riser", "issues": ["Track 'FX - Riser' is not part of any folder structure.", "Track name 'FX - Riser' is duplicated in the project."]}, {"track_guid": "{41D5B075-9459-AC43-BA8A-7746BB8D7D59}", "track_name": "Room Verb", "issues": ["Track 'Room Verb' is not part of any folder structure."]}, {"track_guid": "{8EA3F33B-9A64-0043-B7C1-529261456CAF}", "track_name": "Guitar Delay", "issues": ["Track 'Guitar Delay' is not part of any folder structure."]}, {"track_guid": "{99037C75-5016-884B-B1DA-256073D112CD}", "track_name": "<PERSON><PERSON>", "issues": ["Track 'Slap Delay' is not part of any folder structure."]}, {"track_guid": "{C4858C29-4147-A34F-96C1-D9E69A0868EA}", "track_name": "Vox Long Delay", "issues": ["Track 'Vox Long Delay' is not part of any folder structure."]}]}, "file_reference_analysis": {"summary_messages": ["✅ No problematic file references detected based on path patterns and metadata."], "problematic_files": []}, "item_property_analysis": [{"item_guid": "{D6549952-E128-DE4F-8DE3-13D2AE8093E9}", "item_name": "EZbass Track 1 Stereo Mix (2).wav", "track_guid": "{02823BF1-41A7-D14C-A3A8-FE93C6C1E2A2}", "track_name": "Bass", "issues": ["Item 'EZbass Track 1 Stereo Mix (2).wav' on track 'Bass' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{5CB66E81-3083-4649-A3D5-8C431386F07F}", "item_name": "R&H AndyM_Lead2.wav", "track_guid": "{0B4A92D0-EE8C-194E-9F7D-10A5CE79525F}", "track_name": "Guitar AM Lead L", "issues": ["Item 'R&H AndyM_Lead2.wav' on track 'Guitar AM Lead L' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{50B32E29-C0DA-F346-A24E-F5300A65EAD4}", "item_name": "R&H AndyM_Lead2-imported.wav", "track_guid": "{BD77FF2D-F7EB-4E40-813A-8BC4AE70ACFF}", "track_name": "Guitar AM Lead R", "issues": ["Item 'R&H AndyM_Lead2-imported.wav' on track 'Guitar AM Lead R' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{8E2F1349-F44D-BA40-B0EB-7227FD66FC15}", "item_name": "R&H AndyM1.wav", "track_guid": "{1A2D39A4-E390-C245-8B7A-A506C3D0794B}", "track_name": "Guitar AndyG Rhythm-001", "issues": ["Item 'R&H AndyM1.wav' on track 'Guitar AndyG Rhythm-001' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{2AB349BD-61C9-D141-8B84-3F4AAC8BBBD0}", "item_name": "R&H Run and Hide-001 AndyG dirty harry.wav", "track_guid": "{3793A8C4-8572-2C49-9470-3CD327463DB8}", "track_name": "Git AndyG Rhythm-002", "issues": ["Item 'R&H Run and Hide-001 AndyG dirty harry.wav' on track 'Git AndyG Rhythm-002' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{F5A47BDE-E9B9-7449-8A5E-4A7710CA2D0B}", "item_name": "R&H Run and Hide-001 AndyG dirty harry.wav", "track_guid": "{3793A8C4-8572-2C49-9470-3CD327463DB8}", "track_name": "Git AndyG Rhythm-002", "issues": ["Item 'R&H Run and Hide-001 AndyG dirty harry.wav' on track 'Git AndyG Rhythm-002' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{2C92BA29-94D3-7242-9D70-E33DEF8B2026}", "item_name": "Run and Hide-002 AndyG filthy harry.wav", "track_guid": "{27992955-5A8D-3A4D-83BF-C5C0A0CB832F}", "track_name": "Run and Hide-002 Andy<PERSON> filthy harry", "issues": ["Item 'Run and Hide-002 AndyG filthy harry.wav' on track 'Run and Hide-002 AndyG filthy harry' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{*************-0E44-9962-C4BC6DEA2287}", "item_name": "r&h edits_stems_AndyM5-001.wav", "track_guid": "{5FEB6651-56A3-614E-A92E-9780667A3384}", "track_name": "Guitar Chug", "issues": ["Item 'r&h edits_stems_AndyM5-001.wav' on track 'Guitar Chug' has an abrupt start (short/no fade-in).", "Item 'r&h edits_stems_AndyM5-001.wav' on track 'Guitar Chug' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{20113E83-ABB8-E546-BF2D-054F1F054AE9}", "item_name": "r&h edits_stems_AndyM5-001.wav", "track_guid": "{5FEB6651-56A3-614E-A92E-9780667A3384}", "track_name": "Guitar Chug", "issues": ["Item 'r&h edits_stems_AndyM5-001.wav' on track 'Guitar Chug' has an abrupt start (short/no fade-in).", "Item 'r&h edits_stems_AndyM5-001.wav' on track 'Guitar Chug' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{36A167F5-**************-B56CA5AD511B}", "item_name": "R&H  - stem - stem-001.wav", "track_guid": "{3EBE6CDC-A60F-8048-9984-548C6D6207EF}", "track_name": "Vocals CF Lead", "issues": ["Item 'R&H  - stem - stem-001.wav' on track 'Vocals CF Lead' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{1C2D524D-F395-974F-80A5-B982D263FED5}", "item_name": "R&H  - stem - stem-001.wav", "track_guid": "{3EBE6CDC-A60F-8048-9984-548C6D6207EF}", "track_name": "Vocals CF Lead", "issues": ["Item 'R&H  - stem - stem-001.wav' on track 'Vocals CF Lead' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{DD649090-192B-144C-A5A8-20B05AD6D801}", "item_name": "r&hvox_stems.wav", "track_guid": "{580CE93D-3C75-E141-9151-238E1916CAF7}", "track_name": "r&hvox_stems", "issues": ["Item 'r&hvox_stems.wav' on track 'r&hvox_stems' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{C6B61B93-69BE-3242-9DD7-43D3897D4AE3}", "item_name": "r&hvox_stems.wav", "track_guid": "{580CE93D-3C75-E141-9151-238E1916CAF7}", "track_name": "r&hvox_stems", "issues": ["Item 'r&hvox_stems.wav' on track 'r&hvox_stems' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{1409E32F-8AA3-C142-A4D8-02F2C481D9B3}", "item_name": "R&H  - stem - stem-002.wav", "track_guid": "{D57D8F01-66E4-9146-834F-9AA435AB504A}", "track_name": "Vocals CF Double", "issues": ["Item 'R&H  - stem - stem-002.wav' on track 'Vocals CF Double' has an abrupt start (short/no fade-in).", "Item 'R&H  - stem - stem-002.wav' on track 'Vocals CF Double' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{12B980ED-65D7-744F-A44F-5C6F84B8E736}", "item_name": "14-FX - Riser-MIDI", "track_guid": "{0B850461-A035-B044-997B-EC24D7274DA3}", "track_name": "FX - Riser", "issues": ["Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt start (short/no fade-in).", "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{978455CB-1FF1-B74D-A332-CD9FA0426208}", "item_name": "14-FX - Riser-MIDI", "track_guid": "{0B850461-A035-B044-997B-EC24D7274DA3}", "track_name": "FX - Riser", "issues": ["Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt start (short/no fade-in).", "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{2FA57FE2-DB13-2A4A-82BD-B7B4AD195840}", "item_name": "14-FX - Riser-MIDI", "track_guid": "{34C9C510-A077-F34D-8C70-A3E969B23AEC}", "track_name": "FX - Riser", "issues": ["Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt start (short/no fade-in).", "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{E510AD53-E038-8245-9F6E-5E5D63BBF61C}", "item_name": "14-FX - Riser-MIDI", "track_guid": "{34C9C510-A077-F34D-8C70-A3E969B23AEC}", "track_name": "FX - Riser", "issues": ["Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt start (short/no fade-in).", "Item '14-FX - Riser-MI<PERSON>' on track 'FX - Riser' has an abrupt end (short/no fade-out)."]}], "project_settings_analysis": {"bit_depth": {"value": null, "is_suboptimal": false}, "sample_rate": {"value": 44100, "is_nonstandard": false}, "has_incomplete_info": true, "first_item_abrupt_start_at_zero": true, "stereo_balance_issue": null}, "track_specific_issues": [{"guid": "MASTER_TRACK", "name": "Master", "issues": ["Track 'Master' has bypassed FX in its chain: VST3: Gullfoss (Soundtheory), VST3: Spectre (Wavesfactory), AU: elysia karacter master (Plugin Alliance), VST3: Pro-L 2 (FabFilter)."]}, {"guid": "{27992955-5A8D-3A4D-83BF-C5C0A0CB832F}", "name": "Run and Hide-002 Andy<PERSON> filthy harry", "issues": ["Track 'Run and Hide-002 AndyG filthy harry' is muted."]}, {"guid": "{A8AEF14E-0AF0-F34E-B65C-1759342B1A3C}", "name": "Vocals CF BV", "issues": ["Track 'Vocals CF BV' is muted."]}, {"guid": "{A2443703-F9BE-6B4C-9A3C-AFCD5F96A005}", "name": "CF Vs BVS Vocals", "issues": ["Track 'CF Vs BVS Vocals' is muted."]}, {"guid": "{580CE93D-3C75-E141-9151-238E1916CAF7}", "name": "r&hvox_stems", "issues": ["Track 'r&hvox_stems' is muted."]}, {"guid": "{0B850461-A035-B044-997B-EC24D7274DA3}", "name": "FX - Riser", "issues": ["Track 'FX - Riser' is muted."]}]}}