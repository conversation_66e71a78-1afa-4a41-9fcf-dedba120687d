{"overall_health_score": 75, "genre": "rock", "strict_mode": false, "summary_metrics": {"critical_issues_count": 0, "warning_issues_count": 4, "info_issues_count": 28, "passed_checks_count": 3}, "issues": [{"id": "PLUGIN_MASTER_BUS_REVIEW-1", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'AU: elysia alpha master (Plugin Alliance)' on the Master bus should be reviewed - typically handled by mastering engineer.", "affected_elements": [{"type": "plugin", "guid": null, "name": "AU: elysia alpha master (Plugin Alliance)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'AU: elysia alpha master (Plugin Alliance)' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "PLUGIN_MASTER_BUS_REVIEW-2", "rule_id": "PLUGIN_MASTER_BUS_REVIEW", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Plugin 'VST3: UADx SSL G Bus Compressor (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: UADx SSL G Bus Compressor (Universal Audio (UADx))", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "Review 'VST3: UADx SSL G Bus Compressor (Universal Audio (UADx))' usage on master bus. Consider if this processing should be handled during mastering instead."}, {"id": "PLUGIN_BYPASSED_BLACKLISTED_MASTER-3", "rule_id": "PLUGIN_BYPASSED_BLACKLISTED_MASTER", "category": "Plugin Analysis - Master Bus", "severity": "info", "message": "Bypassed plugin 'VST3: Pro-L 2 (FabFilter)' on the Master bus would be blacklisted if active.", "affected_elements": [{"type": "plugin", "guid": null, "name": "VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "details": {"track_name": "Master", "track_guid": "MASTER_TRACK"}}], "recommendation": "If 'VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)' is reactivated on the master bus, review its suitability carefully. Consider removing if no longer needed."}, {"id": "HYGIENE_MISSING_REGIONS-4", "rule_id": "HYGIENE_MISSING_REGIONS", "category": "Session Hygiene", "severity": "info", "message": "Project is missing regions, which can be useful for selections and exports.", "affected_elements": [{"type": "project"}], "recommendation": "Consider adding regions for song sections or export areas."}, {"id": "HYGIENE_MISSING_NOTES-5", "rule_id": "HYGIENE_MISSING_NOTES", "category": "Session Hygiene", "severity": "info", "message": "Project notes are empty.", "affected_elements": [{"type": "project"}], "recommendation": "Consider adding project notes for mix versions, collaborators, or other relevant info."}, {"id": "HYGIENE_NOT_IN_FOLDER-6", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Drums' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{88652663-91E9-E54F-B601-480FE745A4BE}", "name": "Drums"}], "recommendation": "Consider organizing track 'Drums' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-7", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Bass' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{B4DF1191-5A86-3245-B206-3E934B39726F}", "name": "Bass"}], "recommendation": "Consider organizing track 'Bass' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-8", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'FX Pad' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{0286D29D-D960-A34F-9AF1-511013391EB7}", "name": "FX Pad"}], "recommendation": "Consider organizing track 'FX Pad' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-9", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'FX Riser' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{865B73A0-BC99-A44F-A591-B1E8C986600E}", "name": "FX Riser"}], "recommendation": "Consider organizing track 'FX Riser' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_DUPLICATE_TRACK_NAME-10", "rule_id": "HYGIENE_DUPLICATE_TRACK_NAME", "category": "Session Hygiene", "severity": "warning", "message": "Track name 'FX Riser' is duplicated in the project.", "affected_elements": [{"type": "track", "guid": "{865B73A0-BC99-A44F-A591-B1E8C986600E}", "name": "FX Riser"}], "recommendation": "Rename track 'FX Riser' to ensure all track names are unique for clarity."}, {"id": "HYGIENE_NOT_IN_FOLDER-11", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'FX Riser' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{1BE0C274-5278-D84E-BCEA-99B3A17C2DF2}", "name": "FX Riser"}], "recommendation": "Consider organizing track 'FX Riser' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_DUPLICATE_TRACK_NAME-12", "rule_id": "HYGIENE_DUPLICATE_TRACK_NAME", "category": "Session Hygiene", "severity": "warning", "message": "Track name 'FX Riser' is duplicated in the project.", "affected_elements": [{"type": "track", "guid": "{1BE0C274-5278-D84E-BCEA-99B3A17C2DF2}", "name": "FX Riser"}], "recommendation": "Rename track 'FX Riser' to ensure all track names are unique for clarity."}, {"id": "HYGIENE_NOT_IN_FOLDER-13", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Vocal Lead' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{1EA392B5-1C34-6449-9260-CA1AF0541282}", "name": "Vocal Lead"}], "recommendation": "Consider organizing track 'Vocal Lead' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-14", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Vocal BV' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{4E7FE7FC-0527-8B4E-A37C-964B171944ED}", "name": "Vocal BV"}], "recommendation": "Consider organizing track 'Vocal BV' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-15", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Vocal Bridge' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{9B1809BD-9F0E-9A45-8EEF-EC3921F5945A}", "name": "Vocal Bridge"}], "recommendation": "Consider organizing track 'Vocal Bridge' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-16", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Room Verb' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{EDE251B7-B545-6345-B64B-F371056FDA8D}", "name": "Room Verb"}], "recommendation": "Consider organizing track 'Room Verb' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-17", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Git Ld Delay' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{4D174DEB-CF59-814D-8E15-B2D591EF936B}", "name": "Git Ld Delay"}], "recommendation": "Consider organizing track 'Git Ld Delay' into a folder if appropriate for the project structure."}, {"id": "HYGIENE_NOT_IN_FOLDER-18", "rule_id": "HYGIENE_NOT_IN_FOLDER", "category": "Session Hygiene", "severity": "info", "message": "Track 'Vx Lng Delay' is not part of any folder structure.", "affected_elements": [{"type": "track", "guid": "{F809F7FA-4433-5646-A932-7A132971935C}", "name": "Vx Lng Delay"}], "recommendation": "Consider organizing track 'Vx Lng Delay' into a folder if appropriate for the project structure."}, {"id": "ITEM_ABRUPT_START-19", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-20", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}", "name": "EZbass Track 1 Stereo Mix (4).wav", "details": {"track_name": "Bass"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-21", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'Mid24 - No1 Vox Comp-002.wav' on track 'Guitar AG Vs' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{306ED79D-A8C4-A246-B56E-6ACC26FEF836}", "name": "Mid24 - No1 Vox Comp-002.wav", "details": {"track_name": "Guitar AG Vs"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-22", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'Mid24 - No1 Vox Comp-003.wav' on track 'Guitar AG Brg' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{45C2870C-673F-1E4E-B614-43F0247B99EF}", "name": "Mid24 - No1 Vox Comp-003.wav", "details": {"track_name": "Guitar AG Brg"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-23", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'Mid24 - No1 Vox Comp-005.wav' on track 'Guitar AM RL' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{D3FE22ED-E7A6-D94C-9CEB-94B8BE3D58BA}", "name": "Mid24 - No1 Vox Comp-005.wav", "details": {"track_name": "Guitar AM RL"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-24", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'Mid24 - No1 Vox Comp-006.wav' on track 'Guitar AM RR' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{EB0F3425-8D9F-8A41-8979-B35704099B6E}", "name": "Mid24 - No1 Vox Comp-006.wav", "details": {"track_name": "Guitar AM RR"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-25", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'Mid24 - No1 Vox Comp-007.wav' on track 'Guitar AM Ld' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{56A2C685-AD0E-534D-97C0-426C2FCD20D7}", "name": "Mid24 - No1 Vox Comp-007.wav", "details": {"track_name": "Guitar AM Ld"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_START-26", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item ' - stem' on track 'Chug' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{F7E1032D-85A8-6E41-B90B-7C7A1B67A8EA}", "name": " - stem", "details": {"track_name": "<PERSON><PERSON>"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "ITEM_ABRUPT_END-27", "rule_id": "ITEM_ABRUPT_END", "category": "Item Properties", "severity": "info", "message": "Item ' - stem' on track 'Chug' has an abrupt end (short/no fade-out).", "affected_elements": [{"type": "item", "guid": "{F7E1032D-85A8-6E41-B90B-7C7A1B67A8EA}", "name": " - stem", "details": {"track_name": "<PERSON><PERSON>"}}], "recommendation": "Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts."}, {"id": "ITEM_ABRUPT_START-28", "rule_id": "ITEM_ABRUPT_START", "category": "Item Properties", "severity": "info", "message": "Item 'Mid24 - No1 Vox Comp-008.wav' on track 'Vocal Lead' has an abrupt start (short/no fade-in).", "affected_elements": [{"type": "item", "guid": "{1971DA12-1621-4C4C-87DA-D8F846C72BC3}", "name": "Mid24 - No1 Vox Comp-008.wav", "details": {"track_name": "Vocal Lead"}}], "recommendation": "Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks."}, {"id": "PROJECT_INCOMPLETE_INFO-29", "rule_id": "PROJECT_INCOMPLETE_INFO", "category": "Project Settings", "severity": "info", "message": "Project metadata (e.g., title) is incomplete.", "affected_elements": [{"type": "project"}], "recommendation": "Consider filling in project title in REAPER's project settings for better organization."}, {"id": "PROJECT_ABRUPT_START_AT_ZERO-30", "rule_id": "PROJECT_ABRUPT_START_AT_ZERO", "category": "Project Structure", "severity": "warning", "message": "The first item in the project starts at 0:00 with an abrupt (short/no) fade-in.", "affected_elements": [{"type": "project"}], "recommendation": "Ensure there's adequate pre-roll or a gentle fade-in at the project start to avoid clicks or immediate transients."}, {"id": "TRACK_BYPASSED_FX-31", "rule_id": "TRACK_BYPASSED_FX", "category": "Track Settings", "severity": "info", "message": "Track 'Master' has bypassed FX in its chain: VST3: Pro-L 2 (FabFilter).", "affected_elements": [{"type": "track", "guid": "MASTER_TRACK", "name": "Master", "details": {"bypassed_plugins": ["VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)"]}}], "recommendation": "Review bypassed plugins to ensure they are intentionally inactive."}, {"id": "TRACK_MUTED-32", "rule_id": "TRACK_MUTED", "category": "Track Settings", "severity": "warning", "message": "Track '' is muted.", "affected_elements": [{"type": "track", "guid": "{A40EFDE7-0F00-554C-B635-27AF7124D3BC}", "name": ""}], "recommendation": "Ensure all necessary tracks are unmuted before mixdown."}], "detailed_analysis": {"plugin_analysis": [{"track_guid": "MASTER_TRACK", "track_name": "Master", "plugins": [{"plugin_guid": null, "plugin_name": "AU: elysia alpha master (Plugin Alliance)", "is_bypassed": false, "category": "Compressor", "issues": ["Plugin 'AU: elysia alpha master (Plugin Alliance)' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "VST3: UADx SSL G Bus Compressor (Universal Audio (UADx))", "is_bypassed": false, "category": "Compressor", "issues": ["Plugin 'VST3: UADx SSL G Bus Compressor (Universal Audio (UADx))' on the Master bus should be reviewed - typically handled by mastering engineer."]}, {"plugin_guid": null, "plugin_name": "VST3: Pro-L 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "is_bypassed": true, "category": "Limiter", "issues": ["Bypassed plugin 'VST3: Pro-L 2 (FabFilter)' on the Master bus would be blacklisted if active."]}]}], "master_bus_analysis": {"guid": "MASTER_TRACK", "name": "Master", "issues_found": []}, "session_hygiene_analysis": {"project_level": ["Project is missing regions, which can be useful for selections and exports.", "Project notes are empty."], "track_level": [{"track_guid": "{88652663-91E9-E54F-B601-480FE745A4BE}", "track_name": "Drums", "issues": ["Track 'Drums' is not part of any folder structure."]}, {"track_guid": "{B4DF1191-5A86-3245-B206-3E934B39726F}", "track_name": "Bass", "issues": ["Track 'Bass' is not part of any folder structure."]}, {"track_guid": "{0286D29D-D960-A34F-9AF1-511013391EB7}", "track_name": "FX Pad", "issues": ["Track 'FX Pad' is not part of any folder structure."]}, {"track_guid": "{865B73A0-BC99-A44F-A591-B1E8C986600E}", "track_name": "FX Riser", "issues": ["Track 'FX Riser' is not part of any folder structure.", "Track name 'FX Riser' is duplicated in the project."]}, {"track_guid": "{1BE0C274-5278-D84E-BCEA-99B3A17C2DF2}", "track_name": "FX Riser", "issues": ["Track 'FX Riser' is not part of any folder structure.", "Track name 'FX Riser' is duplicated in the project."]}, {"track_guid": "{1EA392B5-1C34-6449-9260-CA1AF0541282}", "track_name": "Vocal Lead", "issues": ["Track 'Vocal Lead' is not part of any folder structure."]}, {"track_guid": "{4E7FE7FC-0527-8B4E-A37C-964B171944ED}", "track_name": "Vocal BV", "issues": ["Track 'Vocal BV' is not part of any folder structure."]}, {"track_guid": "{9B1809BD-9F0E-9A45-8EEF-EC3921F5945A}", "track_name": "Vocal Bridge", "issues": ["Track 'Vocal Bridge' is not part of any folder structure."]}, {"track_guid": "{EDE251B7-B545-6345-B64B-F371056FDA8D}", "track_name": "Room Verb", "issues": ["Track 'Room Verb' is not part of any folder structure."]}, {"track_guid": "{4D174DEB-CF59-814D-8E15-B2D591EF936B}", "track_name": "Git Ld Delay", "issues": ["Track 'Git Ld Delay' is not part of any folder structure."]}, {"track_guid": "{F809F7FA-4433-5646-A932-7A132971935C}", "track_name": "Vx Lng Delay", "issues": ["Track 'Vx Lng Delay' is not part of any folder structure."]}]}, "file_reference_analysis": {"summary_messages": ["✅ No problematic file references detected based on path patterns and metadata."], "problematic_files": []}, "item_property_analysis": [{"item_guid": "{0DBD2DC8-04BF-644F-AE74-7D452D724BC6}", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "track_guid": "{B4DF1191-5A86-3245-B206-3E934B39726F}", "track_name": "Bass", "issues": ["Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{BE098DC2-CF2B-3C4E-B87D-3122438B07BD}", "item_name": "EZbass Track 1 Stereo Mix (4).wav", "track_guid": "{B4DF1191-5A86-3245-B206-3E934B39726F}", "track_name": "Bass", "issues": ["Item 'EZbass Track 1 Stereo Mix (4).wav' on track 'Bass' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{306ED79D-A8C4-A246-B56E-6ACC26FEF836}", "item_name": "Mid24 - No1 Vox Comp-002.wav", "track_guid": "{542623E9-23DF-8948-9401-7AA5C627B431}", "track_name": "Guitar AG Vs", "issues": ["Item 'Mid24 - No1 Vox Comp-002.wav' on track 'Guitar AG Vs' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{45C2870C-673F-1E4E-B614-43F0247B99EF}", "item_name": "Mid24 - No1 Vox Comp-003.wav", "track_guid": "{3EFA4245-5C24-5645-B6F6-F7C686F39273}", "track_name": "Guitar AG Brg", "issues": ["Item 'Mid24 - No1 Vox Comp-003.wav' on track 'Guitar AG Brg' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{D3FE22ED-E7A6-D94C-9CEB-94B8BE3D58BA}", "item_name": "Mid24 - No1 Vox Comp-005.wav", "track_guid": "{6C7AC72C-5D9E-A346-A437-176C3EA9558E}", "track_name": "Guitar AM RL", "issues": ["Item 'Mid24 - No1 Vox Comp-005.wav' on track 'Guitar AM RL' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{EB0F3425-8D9F-8A41-8979-B35704099B6E}", "item_name": "Mid24 - No1 Vox Comp-006.wav", "track_guid": "{1303983A-9666-6C46-BBD0-677688461E5A}", "track_name": "Guitar AM RR", "issues": ["Item 'Mid24 - No1 Vox Comp-006.wav' on track 'Guitar AM RR' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{56A2C685-AD0E-534D-97C0-426C2FCD20D7}", "item_name": "Mid24 - No1 Vox Comp-007.wav", "track_guid": "{ACD6E9A1-3D55-854B-82EB-7A2FB2564A50}", "track_name": "Guitar AM Ld", "issues": ["Item 'Mid24 - No1 Vox Comp-007.wav' on track 'Guitar AM Ld' has an abrupt start (short/no fade-in)."]}, {"item_guid": "{F7E1032D-85A8-6E41-B90B-7C7A1B67A8EA}", "item_name": " - stem", "track_guid": "{8E3104BF-9F9F-9D49-B1FE-FDD8A529FBA8}", "track_name": "<PERSON><PERSON>", "issues": ["Item ' - stem' on track 'Chug' has an abrupt start (short/no fade-in).", "Item ' - stem' on track 'Chug' has an abrupt end (short/no fade-out)."]}, {"item_guid": "{1971DA12-1621-4C4C-87DA-D8F846C72BC3}", "item_name": "Mid24 - No1 Vox Comp-008.wav", "track_guid": "{1EA392B5-1C34-6449-9260-CA1AF0541282}", "track_name": "Vocal Lead", "issues": ["Item 'Mid24 - No1 Vox Comp-008.wav' on track 'Vocal Lead' has an abrupt start (short/no fade-in)."]}], "project_settings_analysis": {"bit_depth": {"value": null, "is_suboptimal": false}, "sample_rate": {"value": 44100, "is_nonstandard": false}, "has_incomplete_info": true, "first_item_abrupt_start_at_zero": true, "stereo_balance_issue": null}, "track_specific_issues": [{"guid": "MASTER_TRACK", "name": "Master", "issues": ["Track 'Master' has bypassed FX in its chain: VST3: Pro-L 2 (FabFilter)."]}, {"guid": "{A40EFDE7-0F00-554C-B635-27AF7124D3BC}", "name": "", "issues": ["Track '' is muted."]}]}}