# Test Data Directory

This directory contains test data and fixtures used for testing the RPP
Analyser.

## Structure

-   `fixtures/`: Contains test REAPER project files and associated data
    -   `parameter_test/`: A test REAPER project for testing parameter
        extraction/handling
        -   `parameter_test.RPP`: The main REAPER project file
        -   `Backups/`: Auto-generated REAPER project backups
        -   `Media/`: Media files associated with the project

## Purpose

These test files provide consistent data for:

1. Development and debugging
2. Unit and integration testing
3. Reproducing specific parsing scenarios
4. Validating plugin detection and categorization

## Usage

You can load these test files in the application or use them in automated tests
when implementing new features or fixing bugs.
