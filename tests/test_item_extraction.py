import sys
import os

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from backend.parsing.core import parse_rpp_file


def test_item_extraction():
    try:
        result = parse_rpp_file("tests/fixtures/parameter_test/parameter_test.RPP")
        # Add assertions here to check the extracted items and takes
        # print(result) # temporary print for debugging
        assert result["tracks"][1]["items"][0]["name"] == "U&Me_stems.wav"
        assert (
            result["tracks"][1]["items"][0]["takes"][0]["source_file"]
            == "Media/U&Me_stems.wav"
        )
    except Exception as e:
        print(f"Error: {e}")
        assert False, f"Item extraction test failed: {e}"
