"""
Database connection pooling and async operations for high-performance analysis.
"""
import asyncio
import aiosqlite
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
import logging
from pathlib import Path


class ConnectionPool:
    """
    Manages a pool of SQLite database connections for high-performance operations.
    """
    
    def __init__(self, db_path: str, pool_size: int = 5):
        self.db_path = db_path
        self.pool_size = pool_size
        self._pool: asyncio.Queue[aiosqlite.Connection] = asyncio.Queue(maxsize=pool_size)
        self._active_connections = 0
        self._lock = asyncio.Lock()
        self._initialized = False
        
    async def initialize(self):
        """Initialize the connection pool."""
        if self._initialized:
            return
            
        async with self._lock:
            if self._initialized:
                return
                
            # Ensure database directory exists
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Create initial connections
            for _ in range(self.pool_size):
                conn = await aiosqlite.connect(self.db_path)
                await conn.execute("PRAGMA journal_mode=WAL")  # Enable WAL mode for better concurrency
                await conn.execute("PRAGMA synchronous=NORMAL")  # Faster writes
                await conn.execute("PRAGMA cache_size=10000")  # Increase cache size
                await conn.execute("PRAGMA temp_store=MEMORY")  # Use memory for temp storage
                await self._pool.put(conn)
                self._active_connections += 1
            
            self._initialized = True
    
    @asynccontextmanager
    async def get_connection(self):
        """Get a connection from the pool (context manager)."""
        if not self._initialized:
            await self.initialize()
            
        conn = await self._pool.get()
        try:
            yield conn
        finally:
            await self._pool.put(conn)
    
    async def close_all(self):
        """Close all connections in the pool."""
        async with self._lock:
            while not self._pool.empty():
                conn = await self._pool.get()
                await conn.close()
                self._active_connections -= 1
            
            self._initialized = False


class AsyncCacheManager:
    """
    High-performance async cache manager with connection pooling.
    """
    
    def __init__(self, db_path: str = "analysis_cache.db", pool_size: int = 5):
        self.pool = ConnectionPool(db_path, pool_size)
        self._schema_initialized = False
    
    async def _ensure_schema(self, conn: aiosqlite.Connection):
        """Ensure database schema exists."""
        if self._schema_initialized:
            return
            
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS analysis_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_hash TEXT UNIQUE NOT NULL,
                filename TEXT NOT NULL,
                file_size INTEGER,
                genre TEXT,
                strict_mode BOOLEAN,
                analysis_type TEXT NOT NULL,
                result_data TEXT NOT NULL,
                created_at REAL NOT NULL,
                accessed_at REAL NOT NULL,
                access_count INTEGER DEFAULT 1
            )
        """)
        
        # Create optimized indexes
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_file_hash ON analysis_cache(file_hash)")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_analysis_type ON analysis_cache(analysis_type)")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_accessed_at ON analysis_cache(accessed_at)")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_compound ON analysis_cache(file_hash, analysis_type)")
        
        await conn.commit()
        self._schema_initialized = True
    
    async def batch_get_cached_results(self, queries: List[Dict[str, Any]]) -> List[Optional[Dict[str, Any]]]:
        """
        Efficiently retrieve multiple cached results in a single transaction.
        """
        if not queries:
            return []
            
        async with self.pool.get_connection() as conn:
            await self._ensure_schema(conn)
            
            results = []
            
            # Build batch query with UNION ALL
            placeholders = []
            values = []
            
            for i, query in enumerate(queries):
                file_hash = self._calculate_file_hash(
                    query["filename"], query["file_size"], 
                    query["genre"], query["strict_mode"]
                )
                placeholders.append(f"SELECT {i} as query_idx, result_data FROM analysis_cache WHERE file_hash = ? AND analysis_type = ?")
                values.extend([file_hash, query["analysis_type"]])
            
            if placeholders:
                batch_query = " UNION ALL ".join(placeholders)
                cursor = await conn.execute(batch_query, values)
                batch_results = await cursor.fetchall()
                
                # Initialize results array
                results = [None] * len(queries)
                
                # Fill in the results
                for query_idx, result_data in batch_results:
                    try:
                        import json
                        results[query_idx] = json.loads(result_data)
                    except json.JSONDecodeError:
                        results[query_idx] = None
            
            return results
    
    async def batch_cache_results(self, cache_entries: List[Dict[str, Any]]) -> int:
        """
        Efficiently cache multiple results in a single transaction.
        Returns number of successfully cached entries.
        """
        if not cache_entries:
            return 0
            
        async with self.pool.get_connection() as conn:
            await self._ensure_schema(conn)
            
            current_time = asyncio.get_event_loop().time()
            successful_count = 0
            
            # Prepare batch insert
            insert_data = []
            for entry in cache_entries:
                try:
                    import json
                    file_hash = self._calculate_file_hash(
                        entry["filename"], entry["file_size"],
                        entry["genre"], entry["strict_mode"]
                    )
                    result_json = json.dumps(entry["result_data"], default=str)
                    
                    insert_data.append((
                        file_hash, entry["filename"], entry["file_size"],
                        entry["genre"], entry["strict_mode"], entry["analysis_type"],
                        result_json, current_time, current_time, 1
                    ))
                    successful_count += 1
                    
                except (json.JSONEncodeError, KeyError) as e:
                    logging.warning(f"Failed to prepare cache entry: {e}")
                    continue
            
            if insert_data:
                await conn.executemany("""
                    INSERT OR REPLACE INTO analysis_cache 
                    (file_hash, filename, file_size, genre, strict_mode, analysis_type, 
                     result_data, created_at, accessed_at, access_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, insert_data)
                
                await conn.commit()
            
            return successful_count
    
    def _calculate_file_hash(self, filename: str, file_size: int, genre: str, strict_mode: bool) -> str:
        """Calculate a unique hash for the file and analysis parameters."""
        import hashlib
        content = f"{filename}_{file_size}_{genre}_{strict_mode}"
        return hashlib.sha256(content.encode()).hexdigest()
    
    async def optimize_database(self):
        """Run database optimization operations."""
        async with self.pool.get_connection() as conn:
            await self._ensure_schema(conn)
            
            # Analyze tables for query optimization
            await conn.execute("ANALYZE")
            
            # Vacuum to reclaim space and optimize layout
            await conn.execute("VACUUM")
            
            await conn.commit()
    
    async def get_detailed_stats(self) -> Dict[str, Any]:
        """Get comprehensive database statistics."""
        async with self.pool.get_connection() as conn:
            await self._ensure_schema(conn)
            
            # Basic statistics
            cursor = await conn.execute("""
                SELECT 
                    COUNT(*) as total_entries,
                    COUNT(DISTINCT file_hash) as unique_files,
                    COUNT(DISTINCT analysis_type) as analysis_types,
                    AVG(access_count) as avg_access_count,
                    MAX(accessed_at) as last_access,
                    MIN(created_at) as oldest_entry,
                    SUM(LENGTH(result_data)) as total_data_size
                FROM analysis_cache
            """)
            basic_stats = await cursor.fetchone()
            
            # Analysis type breakdown
            cursor = await conn.execute("""
                SELECT analysis_type, COUNT(*) as count
                FROM analysis_cache
                GROUP BY analysis_type
                ORDER BY count DESC
            """)
            type_breakdown = await cursor.fetchall()
            
            # Recent activity
            import time
            one_hour_ago = time.time() - 3600
            one_day_ago = time.time() - 86400
            
            cursor = await conn.execute("""
                SELECT 
                    COUNT(CASE WHEN accessed_at > ? THEN 1 END) as accessed_last_hour,
                    COUNT(CASE WHEN accessed_at > ? THEN 1 END) as accessed_last_day,
                    COUNT(CASE WHEN created_at > ? THEN 1 END) as created_last_day
                FROM analysis_cache
            """, (one_hour_ago, one_day_ago, one_day_ago))
            activity_stats = await cursor.fetchone()
            
            return {
                "basic_stats": {
                    "total_entries": basic_stats[0] if basic_stats else 0,
                    "unique_files": basic_stats[1] if basic_stats else 0,
                    "analysis_types": basic_stats[2] if basic_stats else 0,
                    "avg_access_count": round(basic_stats[3] or 0, 2),
                    "last_access": basic_stats[4],
                    "oldest_entry": basic_stats[5],
                    "total_data_size_mb": round((basic_stats[6] or 0) / (1024 * 1024), 2)
                },
                "type_breakdown": [{"type": row[0], "count": row[1]} for row in type_breakdown],
                "activity": {
                    "accessed_last_hour": activity_stats[0] if activity_stats else 0,
                    "accessed_last_day": activity_stats[1] if activity_stats else 0,
                    "created_last_day": activity_stats[2] if activity_stats else 0
                }
            }
    
    async def close(self):
        """Close the connection pool."""
        await self.pool.close_all()


# Global async cache manager instance
_async_cache_manager = None

async def get_async_cache_manager() -> AsyncCacheManager:
    """Get the global async cache manager instance."""
    global _async_cache_manager
    if _async_cache_manager is None:
        _async_cache_manager = AsyncCacheManager()
        await _async_cache_manager.pool.initialize()
    return _async_cache_manager


class PerformanceMonitor:
    """
    Monitor and log performance metrics for database operations.
    """
    
    def __init__(self):
        self.operation_times: Dict[str, List[float]] = {}
        self.operation_counts: Dict[str, int] = {}
    
    async def time_operation(self, operation_name: str, operation_func):
        """Time a database operation and record metrics."""
        import time
        start_time = time.time()
        
        try:
            result = await operation_func()
            return result
        finally:
            duration = time.time() - start_time
            
            if operation_name not in self.operation_times:
                self.operation_times[operation_name] = []
                self.operation_counts[operation_name] = 0
            
            self.operation_times[operation_name].append(duration)
            self.operation_counts[operation_name] += 1
            
            # Keep only last 100 measurements to avoid memory bloat
            if len(self.operation_times[operation_name]) > 100:
                self.operation_times[operation_name] = self.operation_times[operation_name][-100:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for all operations."""
        stats = {}
        
        for operation, times in self.operation_times.items():
            if times:
                stats[operation] = {
                    "count": self.operation_counts[operation],
                    "avg_time": round(sum(times) / len(times), 4),
                    "min_time": round(min(times), 4),
                    "max_time": round(max(times), 4),
                    "recent_avg": round(sum(times[-10:]) / min(len(times), 10), 4) if times else 0
                }
        
        return stats


# Global performance monitor
performance_monitor = PerformanceMonitor()