"""
Wrapper module for compatibility with existing code.

This module provides the same interface as the original parser_wrapper.py,
but delegates to the new modular parsing package.
"""

# Import all necessary functions and re-export them
from parsing.core import (
    parse_rpp_content as core_parse_rpp_content,
    parse_rpp_file,
    parse_rpp_fileobj,
)
from parsing.generators import (
    generate_markdown,
    generate_rfxchain,
    generate_tracktemplate,
)
from parsing.genre_rules import Genre  # Import Genre type
from parsing.models import ParsedSession  # Import ParsedSession
from typing import (
    Dict,
    Any,
    BinaryIO,
)  # Dict, Any might not be needed for this function's return anymore


def parse_rpp_content(
    rpp_content: bytes,
    filename: str = "Unknown.rpp",
    genre: Genre = "general",
    strict_mode: bool = False,
) -> ParsedSession:  # Changed return type
    """
    Parses the raw bytes of an RPP file content and structures it into a ParsedSession object.
    This wrapper passes genre and strict_mode to the core parser.
    """
    return core_parse_rpp_content(rpp_content, filename, genre, strict_mode)


# Re-export the functions that were in the original parser_wrapper.py
__all__ = [
    "parse_rpp_content",
    "parse_rpp_file",
    "parse_rpp_fileobj",
    "generate_markdown",
    "generate_rfxchain",
    "generate_tracktemplate",
]
