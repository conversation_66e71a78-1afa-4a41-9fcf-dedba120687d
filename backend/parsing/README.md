# RPP Parser Module

This module provides functionality for parsing REAPER project files (.rpp) into
structured data.

## Module Structure

-   `__init__.py` - Package initialization, exports the main API
-   `models.py` - Data classes representing REAPER project structures
-   `extractors.py` - Functions to extract data from the RPP Element tree
-   `core.py` - Main entry points for parsing functionality
-   `generators.py` - Functions to generate output formats (Markdown, RfxChain,
    TrackTemplate)

## Usage

The main entry point is the `parse_rpp_content` function:

```python
from backend.parsing import parse_rpp_content

# Parse RPP content
with open('project.rpp', 'rb') as f:
    rpp_content = f.read()

session_data = parse_rpp_content(rpp_content, filename='project.rpp')
```

Alternatively, you can use the convenience functions:

```python
from backend.parsing import parse_rpp_file

# Parse directly from file path
session_data = parse_rpp_file('path/to/project.rpp')
```

## Data Structure

The parser extracts the following information:

-   **Metadata** - Project-level information such as title, REAPER version, etc.
-   **Tracks** - List of tracks with properties (name, volume, pan, FX, etc.)
-   **Routing** - Send/receive connections between tracks

## Output Generators

Several output formats can be generated from the parsed data:

```python
from backend.parsing import ParsedSession, generate_markdown

# Generate Markdown summary
markdown_text = generate_markdown(session)

# Export FX chains
rfxchain_text = generate_rfxchain(session, [0, 1])  # Export FX from tracks 0 and 1

# Export track templates
template_text = generate_tracktemplate(session, [0, 1])  # Export tracks 0 and 1
```
