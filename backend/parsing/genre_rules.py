"""
Genre-specific rules and thresholds for mastering analysis.

This module defines baseline mastering guidelines and provides functions
to adjust these guidelines based on the selected music genre.
"""

from typing import Dict, Any, Literal

# Define valid genre literals for type hinting and validation
Genre = Literal[
    "general",
    "electronic",
    "hiphop",
    "rock",
    "pop",
    "acoustic",
    "jazz_classical",
    "experimental",
]

# --- Baseline Mastering Rules ---
# These are conservative defaults, suitable for a "general" or "unknown" genre.
# They will be adjusted by genre-specific modifiers.
BASELINE_RULES: Dict[str, Any] = {
    # Plugin Blacklists (True means flag if found on master bus, unless context allows)
    "plugin_blacklist_master": {
        "limiters": True,  # e.g., Pro-L, Ozone Maximizer, Elevate
        "clippers": True,  # e.g., StandardClip (unless creative intent is clear)
        "ozone_maximizer_specific": True,
        "spectral_balancers": True,  # e.g., Gullfoss, Soothe on master
        "compressors_on_master": "review_recommended",  # e.g., SSL G-Bus, API 2500
        "multiband_on_master": "review_recommended",  # e.g., C4, Pro-MB, Ozone Dynamic EQ
        "saturation_on_master": "review_recommended",  # e.g., HG-2, Tape saturation on master
        "eq_on_master": "review_recommended",  # e.g., Pro-Q, SSL EQ on master bus
        "stereo_imaging_on_master": "review_recommended",  # e.g., Ozone Imager, stereo processing
    },
    "plugin_blacklist_track": {  # Plugins that generally shouldn't be on individual tracks pre-mastering
        "mastering_limiters_on_tracks": True,
        "finalizers_on_tracks": True,  # e.g. Ozone on a single guitar track
    },
    # Gain Staging Thresholds (Item/Take Level)
    "gain_staging_item_max_db": 6.0,  # Max recommended peak for an item/take
    "gain_staging_item_min_db": -20.0,  # Flag if significantly lower, might be intentional noise floor
    "gain_staging_track_max_peak_db": -6.0,  # Target peak for track outputs feeding the master
    # Master Bus Analysis
    "master_bus_target_peak_db": -1.0,  # If a limiter is used, its ceiling should be around here
    "master_bus_target_lufs_integrated": -14.0,  # General target for many platforms (very context-dependent)
    "master_bus_automation_flag": True,  # Flag if master fader has volume automation
    # Session Hygiene
    "flag_generic_track_names": True,
    "flag_empty_tracks_or_items": True,
    "flag_tracks_not_in_folders": False,  # Only if project uses folders extensively
    "require_markers_regions": False,  # Reminder, not a hard rule
    # Technical Issues
    "flag_sample_rate_mismatch": True,
    "flag_bit_depth_mismatch": False,  # Harder to detect reliably from RPP alone
}

# --- Genre-Specific Modifiers ---
# These dictionaries will override or add to the BASELINE_RULES.
GENRE_MODIFIERS: Dict[Genre, Dict[str, Any]] = {
    "general": {
        # General uses baseline as is, or with minor common-sense tweaks
    },
    "electronic": {
        "plugin_blacklist_master": {
            "limiters": "warn_settings",  # More tolerant, but check settings (e.g. ceiling)
            "clippers": "warn_settings",  # Often used creatively
            "compressors_on_master": "review_recommended",  # Keep default
            "multiband_on_master": "review_recommended",  # Keep default
        },
        "gain_staging_item_max_db": 8.0,
        "gain_staging_track_max_peak_db": -3.0,
        "master_bus_target_lufs_integrated": -9.0,  # Often louder
    },
    "hiphop": {
        "plugin_blacklist_master": {
            "clippers": "warn_settings",  # Common creative tool
        },
        "gain_staging_item_max_db": 7.0,
        "master_bus_target_lufs_integrated": -10.0,
    },
    "rock": {
        "gain_staging_item_max_db": 7.0,
        "master_bus_target_lufs_integrated": -12.0,
    },
    "pop": {
        "master_bus_target_lufs_integrated": -11.0,
    },
    "acoustic": {
        "gain_staging_item_max_db": 4.0,  # Stricter dynamics
        "gain_staging_track_max_peak_db": -8.0,
        "master_bus_target_lufs_integrated": -16.0,  # More dynamic range
        "plugin_blacklist_master": {
            "limiters": True,  # Stricter on limiters
            "clippers": True,  # Stricter on clippers
            "compressors_on_master": "review_recommended",  # Keep default
            "multiband_on_master": "review_recommended",  # Keep default
        },
    },
    "jazz_classical": {  # Similar to acoustic, even more emphasis on dynamics
        "gain_staging_item_max_db": 3.0,
        "gain_staging_track_max_peak_db": -9.0,
        "master_bus_target_lufs_integrated": -18.0,
        "plugin_blacklist_master": {
            "limiters": True,
            "clippers": True,
            "compressors_on_master": "review_recommended",  # Keep default
            "multiband_on_master": "review_recommended",  # Keep default
        },
    },
    "experimental": {
        # For experimental, many rules might be relaxed or just warnings
        "plugin_blacklist_master": {
            key: "warn_only" for key in BASELINE_RULES["plugin_blacklist_master"]
        },
        "master_bus_automation_flag": False,  # Automation might be part of the art
    },
}


def get_rules_for_genre(
    genre: Genre = "general", strict_mode: bool = False
) -> Dict[str, Any]:
    """
    Computes the effective set of mastering rules for a given genre.
    Starts with baseline rules and applies genre-specific modifications.

    Args:
        genre: The selected music genre. Defaults to "general".
        strict_mode: If True, enforces the most conservative rules, largely ignoring genre.

    Returns:
        A dictionary of effective rules for the specified genre.
    """
    if genre not in GENRE_MODIFIERS:
        genre = "general"  # Fallback to general if an invalid genre is passed

    # Start with a deep copy of baseline rules
    effective_rules = {
        k: (v.copy() if isinstance(v, dict) else v) for k, v in BASELINE_RULES.items()
    }

    if strict_mode:
        # In strict mode, we might want to enforce the most conservative settings
        # For example, ensure all blacklisted plugins are flagged regardless of genre.
        # This part can be expanded based on how "strict" should behave.
        # For now, strict mode will mostly use baseline, but ensure critical flags are True.
        for key, sub_dict in effective_rules.items():
            if isinstance(sub_dict, dict) and "blacklist" in key:
                for plugin_type in sub_dict:
                    sub_dict[plugin_type] = True  # Enforce blacklisting in strict mode
        effective_rules["gain_staging_item_max_db"] = min(
            effective_rules["gain_staging_item_max_db"], 4.0
        )  # Example strict gain
        return effective_rules

    # Get genre-specific modifiers
    modifiers = GENRE_MODIFIERS.get(genre, {})

    # Apply modifiers
    # This is a simple merge; more complex logic might be needed for nested dicts
    for key, value in modifiers.items():
        if (
            isinstance(value, dict)
            and key in effective_rules
            and isinstance(effective_rules[key], dict)
        ):
            effective_rules[key].update(value)  # Merge dictionaries
        else:
            effective_rules[key] = value  # Override or add

    return effective_rules


# Example usage:
if __name__ == "__main__":
    print("--- General Rules ---")
    general_rules = get_rules_for_genre("general")
    for k, v in general_rules.items():
        print(f"{k}: {v}")

    print("\n--- Electronic Rules ---")
    electronic_rules = get_rules_for_genre("electronic")
    for k, v in electronic_rules.items():
        print(f"{k}: {v}")

    print("\n--- Acoustic Rules (Strict Mode) ---")
    acoustic_strict_rules = get_rules_for_genre("acoustic", strict_mode=True)
    for k, v in acoustic_strict_rules.items():
        print(f"{k}: {v}")
