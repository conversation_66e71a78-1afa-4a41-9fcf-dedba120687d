from __future__ import annotations
from typing import List, Dict, Any, Optional
from functools import lru_cache
import hashlib
import json

from parsing.models import (
    ParsedSession,
    MasteringAnalysisIssue,
    AffectedElement,
    Track,
    Metadata,
    Item,
    PluginAnalysisResult,
    IssueGroup,
    GroupedMasteringAnalysis,
    MasterOutputInfo,
)
from parsing.genre_rules import Genre, get_rules_for_genre
from database import get_cache

# Placeholder for a potential ParsingContext import if needed directly
# from parsing.infrastructure.parsing_context import ParsingContext


class MasteringAnalysisEngine:
    """
    Orchestrates the analysis of a parsed session to generate a "Ready for Mastering" report.
    Enhanced with caching for better performance.
    """

    def __init__(
        self,
        parsed_session: ParsedSession,
        genre: Genre = "general",
        strict_mode: bool = False,
    ):
        self.parsed_session = parsed_session
        self.genre = genre
        self.genre_rules = get_rules_for_genre(genre, strict_mode)
        self.strict_mode = strict_mode
        self.issues: List[MasteringAnalysisIssue] = []
        self.detailed_analysis: Dict[str, Any] = (
            {}
        )  # To store section-specific detailed findings
        
        # Caching for expensive operations
        self._analysis_cache: Dict[str, Any] = {}
        self._session_hash = self._calculate_session_hash()

    def _calculate_session_hash(self) -> str:
        """Calculate a hash of the session for caching purposes."""
        try:
            # Create a simplified representation for hashing
            session_data = {
                "track_count": len(self.parsed_session.tracks),
                "genre": self.genre,
                "strict_mode": self.strict_mode,
                "filename": self.parsed_session.metadata.filename,
                "track_names": [track.name for track in self.parsed_session.tracks[:10]]  # Sample for performance
            }
            session_str = json.dumps(session_data, sort_keys=True)
            return hashlib.md5(session_str.encode()).hexdigest()
        except Exception:
            # Fallback to simple hash if serialization fails
            return f"{len(self.parsed_session.tracks)}_{self.genre}_{self.strict_mode}"

    def _get_cached_analysis(self, analysis_type: str) -> Optional[Any]:
        """Get cached analysis result if available."""
        cache_key = f"{self._session_hash}_{analysis_type}"
        return self._analysis_cache.get(cache_key)

    def _cache_analysis(self, analysis_type: str, result: Any) -> None:
        """Cache analysis result for future use."""
        cache_key = f"{self._session_hash}_{analysis_type}"
        self._analysis_cache[cache_key] = result

    def analyse(self) -> Dict[str, Any]:
        """
        Performs all mastering analysis checks and returns a comprehensive report.
        Uses database caching for improved performance.
        """
        # Temporarily disable caching to debug the issue
        # cache = get_cache()
        # file_complexity = len(self.parsed_session.tracks)
        # cached_result = cache.get_cached_result(
        #     filename=self.parsed_session.metadata.filename,
        #     file_size=file_complexity,
        #     genre=self.genre,
        #     strict_mode=self.strict_mode,
        #     analysis_type="mastering_analysis"
        # )
        # 
        # if cached_result:
        #     return cached_result
        
        # Perform fresh analysis
        self.issues = []  # Reset issues for a fresh analysis
        self.detailed_analysis = {}

        # --- Phase 1 Analysis Checks (and beyond) will be called here ---
        self._analyse_plugin_issues()
        self._analyse_master_bus_chain()
        self._analyse_master_output()  # Phase D2: Master output validation
        self._analyse_offline_media()  # Phase D2: Offline media detection
        self._analyse_sample_rate_consistency()  # Phase D2: Sample rate consistency
        self._analyse_time_stretch_and_playrate()  # Phase D2: Enhanced playrate detection
        self._analyse_record_arm_safety()  # Phase D2: Enhanced record arm safety
        self._analyse_session_hygiene()
        self._analyse_file_references()
        self._analyse_item_properties()  # Fades, reversed, playrate
        self._analyse_project_settings()  # Bit depth, sample rate, record-armed tracks
        self._analyse_track_specific_issues()  # Muted, bypassed FX, etc.

        # --- Calculate overall score and summary metrics ---
        summary_metrics = self._calculate_summary_metrics()
        overall_health_score = self._calculate_overall_health_score(summary_metrics)

        # Build result dictionary
        result = {
            "overall_health_score": overall_health_score,
            "genre": self.genre,  # Use the genre string directly
            "strict_mode": self.strict_mode,
            "summary_metrics": summary_metrics,
            "issues": [issue.to_dict() for issue in self.issues],
            "detailed_analysis": self.detailed_analysis,
        }
        
        # Temporarily disable caching to debug the issue
        # cache = get_cache()
        # cache.cache_result(
        #     filename=self.parsed_session.metadata.filename,
        #     file_size=file_complexity,
        #     genre=self.genre,
        #     strict_mode=self.strict_mode,
        #     analysis_type="mastering_analysis",
        #     result_data=result
        # )
        
        return result

    def analyse_grouped(self) -> Dict[str, Any]:
        """
        Performs all mastering analysis checks and returns a grouped report for better UX.
        """
        # First run regular analysis to populate issues
        regular_analysis = self.analyse()
        
        # Group the issues
        issue_groups, ungrouped_issues = self._group_issues(self.issues)
        
        # Create grouped analysis
        grouped_analysis = GroupedMasteringAnalysis(
            overall_health_score=regular_analysis["overall_health_score"],
            genre=regular_analysis["genre"],
            strict_mode=regular_analysis["strict_mode"],
            summary_metrics=regular_analysis["summary_metrics"],
            issue_groups=issue_groups,
            ungrouped_issues=ungrouped_issues,
            detailed_analysis=regular_analysis["detailed_analysis"]
        )
        
        return grouped_analysis.to_dict()

    def _add_issue(
        self,
        rule_id: str,
        category: str,
        severity: str,
        message: str,
        recommendation: Optional[str] = None,
        affected_elements: Optional[List[AffectedElement]] = None,
        details_key: Optional[str] = None,
        is_mastering_critical: bool = True,
    ):
        """Helper to create and add an issue."""
        issue_id = f"{rule_id}-{len(self.issues) + 1}"  # Simple unique ID for now
        self.issues.append(
            MasteringAnalysisIssue(
                id=issue_id,
                rule_id=rule_id,
                category=category,
                severity=severity,
                message=message,
                recommendation=recommendation,
                is_mastering_critical=is_mastering_critical,
                affected_elements=affected_elements or [],
                details_key=details_key,
            )
        )

    def _calculate_summary_metrics(self) -> Dict[str, int]:
        critical_issues_count = sum(
            1 for issue in self.issues if issue.severity == "critical"
        )
        warning_issues_count = sum(
            1 for issue in self.issues if issue.severity == "warning"
        )
        info_issues_count = sum(1 for issue in self.issues if issue.severity == "info")
        # Calculate passed checks - areas where no issues were found
        total_checks_performed = 0
        
        # Count major analysis areas performed
        if hasattr(self.parsed_session, 'tracks') and self.parsed_session.tracks:
            total_checks_performed += 1  # Plugin analysis performed
            total_checks_performed += 1  # Track settings analysis performed
            
        if hasattr(self.parsed_session, 'metadata'):
            total_checks_performed += 1  # Project settings analysis performed
            total_checks_performed += 1  # File reference analysis performed
            
        # Count specific check categories that passed
        passed_checks_count = 0
        
        # File references passed if no issues detected
        if not any(issue.rule_id.startswith("FILE_REF") for issue in self.issues):
            passed_checks_count += 1
            
        # Master bus chain passed if no structural issues
        master_bus_issues = [issue for issue in self.issues if "MASTERBUS_" in issue.rule_id]
        if not master_bus_issues:
            passed_checks_count += 1
            
        # Add more passed checks based on absence of critical issues
        if critical_issues_count == 0:
            passed_checks_count += 1  # No critical issues found

        return {
            "critical_issues_count": critical_issues_count,
            "warning_issues_count": warning_issues_count,
            "info_issues_count": info_issues_count,
            "passed_checks_count": passed_checks_count,  # Needs refinement
        }

    def _calculate_overall_health_score(self, summary_metrics: Dict[str, int]) -> int:
        # More balanced scoring that doesn't penalize minor issues as harshly
        score = 100
        
        # Critical issues are serious problems
        score -= summary_metrics["critical_issues_count"] * 25
        
        # Warning issues are moderate concerns (reduced penalty)
        score -= summary_metrics["warning_issues_count"] * 3
        
        # Info issues are minor suggestions (minimal penalty)
        score -= summary_metrics["info_issues_count"] * 1
        
        # Bonus for passed checks
        score += summary_metrics["passed_checks_count"] * 5
        
        return max(0, min(100, score))

    # --------------------------------------------------------------------------
    # Placeholder methods for individual analysis categories.
    # These will be implemented based on the checks in implementation_plan_mastering_report_enhancements.md
    # --------------------------------------------------------------------------

    def _analyse_plugin_issues(self):
        """
        Analyzes plugins on all tracks for blacklisting, problematic parameters,
        and other plugin-related concerns based on PluginAnalysisResult.
        Refactored for better performance and maintainability with caching.
        """
        # Check cache first
        cached_result = self._get_cached_analysis("plugin_issues")
        if cached_result is not None:
            # Apply cached issues and detailed analysis
            for issue in cached_result["issues"]:
                self.issues.append(issue)
            if cached_result["detailed_analysis"]:
                self.detailed_analysis["plugin_analysis"] = cached_result["detailed_analysis"]
            return

        plugin_issues_details = []  # For self.detailed_analysis
        cached_issues = []  # Track issues for caching

        # Process tracks in batches for better memory management
        tracks_with_fx = [track for track in self.parsed_session.tracks if track.fx]
        
        for track in tracks_with_fx:
            track_plugin_issues = self._analyze_track_plugins(track)
            if track_plugin_issues["plugins"]:
                plugin_issues_details.append(track_plugin_issues)

        if plugin_issues_details:
            self.detailed_analysis["plugin_analysis"] = plugin_issues_details

        # Cache the results (issues are tracked in _add_issue calls)
        cached_issues = [issue for issue in self.issues if issue.category == "Plugin Analysis" or "Plugin Analysis" in issue.category]
        self._cache_analysis("plugin_issues", {
            "issues": cached_issues,
            "detailed_analysis": plugin_issues_details
        })

    def _analyze_track_plugins(self, track):
        """
        Analyze all plugins on a single track for issues.
        """
        track_plugin_issues = {
            "track_guid": track.guid,
            "track_name": track.name,
            "plugins": [],
        }

        is_master_track = track.type == "MASTER"
        
        for fx_plugin_result in track.fx:
            plugin_issues = self._analyze_single_plugin(fx_plugin_result, track, is_master_track)
            if plugin_issues["issues"]:
                track_plugin_issues["plugins"].append(plugin_issues)

        return track_plugin_issues

    def _analyze_single_plugin(self, fx_plugin_result, track, is_master_track):
        """
        Analyze a single plugin for all types of issues.
        """
        plugin_name = fx_plugin_result.get("name", "Unknown Plugin")
        plugin_guid = fx_plugin_result.get("guid")
        is_bypassed = fx_plugin_result.get("is_bypassed", False)
        current_plugin_issues = []

        # Check different types of plugin issues
        current_plugin_issues.extend(
            self._check_general_blacklist_issues(fx_plugin_result, track, is_bypassed)
        )
        
        if is_master_track:
            current_plugin_issues.extend(
                self._check_master_blacklist_issues(fx_plugin_result, track, is_bypassed)
            )

        return {
            "plugin_guid": plugin_guid,
            "plugin_name": plugin_name,
            "is_bypassed": is_bypassed,
            "category": fx_plugin_result.get("category"),
            "issues": current_plugin_issues,
        }

    def _check_general_blacklist_issues(self, fx_plugin_result, track, is_bypassed):
        """
        Check for general blacklist issues on any track.
        """
        plugin_name = fx_plugin_result.get("name", "Unknown Plugin")
        plugin_guid = fx_plugin_result.get("guid")
        issues = []

        # General Blacklist Check (applies if plugin is active)
        if fx_plugin_result.get("is_blacklisted_general") and not is_bypassed:
            message = f"Plugin '{plugin_name}' on track '{track.name}' is generally not recommended for mastering or final mixdowns."
            self._add_issue(
                rule_id="PLUGIN_BLACKLISTED_GENERAL",
                category="Plugin Analysis",
                severity="warning",
                message=message,
                recommendation=f"Consider bypassing or removing '{plugin_name}' or replacing it with an alternative suitable for mastering.",
                affected_elements=[
                    self._create_plugin_affected_element(plugin_guid, plugin_name, track)
                ],
            )
            issues.append(message)

        # General Blacklist Check (for bypassed plugins that WOULD be an issue if active)
        if fx_plugin_result.get("would_be_blacklisted_general_if_active") and is_bypassed:
            message = f"Bypassed plugin '{plugin_name}' on track '{track.name}' would be generally blacklisted if active."
            self._add_issue(
                rule_id="PLUGIN_BYPASSED_BLACKLISTED_GENERAL",
                category="Plugin Analysis",
                severity="info",
                message=message,
                recommendation=f"If '{plugin_name}' is reactivated, review its suitability. Consider removing if no longer needed.",
                affected_elements=[
                    self._create_plugin_affected_element(plugin_guid, plugin_name, track)
                ],
            )
            issues.append(message)

        return issues

    def _check_master_blacklist_issues(self, fx_plugin_result, track, is_bypassed):
        """
        Check for master bus specific blacklist issues.
        """
        plugin_name = fx_plugin_result.get("name", "Unknown Plugin")
        plugin_guid = fx_plugin_result.get("guid")
        issues = []

        # Master Bus Specific Blacklist (only for master track and if plugin is active)
        if fx_plugin_result.get("is_blacklisted_master") and not is_bypassed:
            severity = "critical" if self.strict_mode else "warning"
            message = f"Plugin '{plugin_name}' on the Master bus is not recommended for final mastering."
            self._add_issue(
                rule_id="PLUGIN_BLACKLISTED_MASTER",
                category="Plugin Analysis - Master Bus",
                severity=severity,
                message=message,
                recommendation=f"Critically review '{plugin_name}'. Consider bypassing or removing it before sending to mastering, or ensure it's used with extreme care.",
                affected_elements=[
                    self._create_plugin_affected_element(plugin_guid, plugin_name, track)
                ],
            )
            issues.append(message)

        # Master Bus Specific Blacklist (for bypassed plugins that WOULD be an issue if active)
        if fx_plugin_result.get("would_be_blacklisted_master_if_active") and is_bypassed:
            message = f"Bypassed plugin '{plugin_name}' on the Master bus would be blacklisted if active."
            self._add_issue(
                rule_id="PLUGIN_BYPASSED_BLACKLISTED_MASTER",
                category="Plugin Analysis - Master Bus",
                severity="info",
                message=message,
                recommendation=f"If '{plugin_name}' is reactivated on the master bus, review its suitability carefully. Consider removing if no longer needed.",
                affected_elements=[
                    self._create_plugin_affected_element(plugin_guid, plugin_name, track)
                ],
            )
            issues.append(message)

        # Master Bus Warning Type
        if (fx_plugin_result.get("master_bus_warning_type") == "review_recommended" 
            and not is_bypassed):
            message = f"Plugin '{plugin_name}' on the Master bus should be reviewed - typically handled by mastering engineer."
            self._add_issue(
                rule_id="PLUGIN_MASTER_BUS_REVIEW",
                category="Plugin Analysis - Master Bus",
                severity="info",
                message=message,
                recommendation=f"Review '{plugin_name}' usage on master bus. Consider if this processing should be handled during mastering instead.",
                affected_elements=[
                    self._create_plugin_affected_element(plugin_guid, plugin_name, track)
                ],
            )
            issues.append(message)

        return issues

    def _create_plugin_affected_element(self, plugin_guid, plugin_name, track):
        """
        Helper method to create consistent plugin affected element structures.
        """
        return {
            "type": "plugin",
            "guid": plugin_guid,
            "name": plugin_name,
            "details": {
                "track_name": track.name,
                "track_guid": track.guid,
            },
        }

    def _analyse_master_bus_chain(self):
        """Analyzes the master bus for specific chain issues (limiters, order, etc.)."""
        master_track = next(
            (t for t in self.parsed_session.tracks if t.type == "MASTER"), None
        )
        if not master_track:
            # This should ideally not happen if parsing guarantees a master track object
            self._add_issue(
                rule_id="MASTERBUS_MISSING",
                category="Master Bus Analysis",
                severity="critical",
                message="Master track data not found in the parsed session.",
                recommendation="Ensure the RPP file is valid and contains a master track.",
            )
            return

        bus_analysis_details: Dict[str, Any] = {
            "guid": master_track.guid,
            "name": master_track.name,
            "issues_found": [],
        }

        # Check for flags set by MasterTrackExtractor or PluginAnalysisEngine on the MasterTrack object
        if (
            master_track.has_limiter_on_master is False
        ):  # Explicitly check for False if True means a problem
            # This might be too noisy if has_limiter_on_master is just a presence flag.
            # The .clinerules suggest flagging limiters for removal/bypass.
            # Let's assume for now that the *presence* of a limiter is not an issue itself, but *how* it's used.
            pass

        if master_track.has_multiple_limiters_on_master:
            message = "Multiple limiters detected on the master bus."
            self._add_issue(
                rule_id="MASTERBUS_MULTIPLE_LIMITERS",
                category="Master Bus Analysis",
                severity="critical",
                message=message,
                recommendation="Typically, only one final limiter should be used on the master bus. Consolidate or remove redundant limiters.",
                affected_elements=[
                    {
                        "type": "track",
                        "guid": master_track.guid,
                        "name": master_track.name,
                    }
                ],
            )
            bus_analysis_details["issues_found"].append(message)

        if master_track.limiter_not_last_on_master:
            message = "A limiter is present on the master bus but is not the last active non-metering plugin."
            self._add_issue(
                rule_id="MASTERBUS_LIMITER_NOT_LAST",
                category="Master Bus Analysis",
                severity="critical",
                message=message,
                recommendation="Ensure your final mastering limiter is the last plugin in the active chain (before any final metering).",
                affected_elements=[
                    {
                        "type": "track",
                        "guid": master_track.guid,
                        "name": master_track.name,
                    }
                ],
            )
            bus_analysis_details["issues_found"].append(message)

        if master_track.has_mixing_plugins_on_master:
            # Identify which plugins are considered "mixing plugins"
            mixing_plugins_on_master = [
                fx.get("name")
                for fx in master_track.fx
                if fx.get("is_mixing_plugin_on_master_candidate")
                and not fx.get("is_bypassed")
            ]
            if mixing_plugins_on_master:
                message = f"Mixing-type plugins found on the master bus: {', '.join(mixing_plugins_on_master)}. These are typically better suited for individual tracks or buses."
                self._add_issue(
                    rule_id="MASTERBUS_MIXING_PLUGINS",
                    category="Master Bus Analysis",
                    severity="warning",
                    message=message,
                    recommendation="Review these plugins. Consider if their effect is truly global or if they should be moved to appropriate tracks/buses.",
                    affected_elements=[
                        {
                            "type": "track",
                            "guid": master_track.guid,
                            "name": master_track.name,
                            "details": {"plugins": mixing_plugins_on_master},
                        }
                    ],
                )
                bus_analysis_details["issues_found"].append(message)

        if master_track.has_clipper_on_master:  # Assuming this flag is set
            message = "A clipper plugin is active on the master bus."
            self._add_issue(
                rule_id="MASTERBUS_CLIPPER_ACTIVE",
                category="Master Bus Analysis",
                severity="warning",  # Or critical depending on mastering philosophy / genre
                message=message,
                recommendation="Clippers on the master bus can be destructive if not used carefully. Ensure this is intentional and appropriate for pre-mastering.",
                affected_elements=[
                    {
                        "type": "track",
                        "guid": master_track.guid,
                        "name": master_track.name,
                    }
                ],
            )
            bus_analysis_details["issues_found"].append(message)

        if (
            master_track.has_master_bus_automation
        ):  # Volume or Pan automation on master fader
            message = "Master fader has volume or pan automation."
            self._add_issue(
                rule_id="MASTERBUS_FADER_AUTOMATION",
                category="Master Bus Analysis",
                severity="warning",
                message=message,
                recommendation="Mastering engineers usually prefer a static master fader. If automation is for creative fades, print it to a pre-master track or communicate this clearly.",
                affected_elements=[
                    {
                        "type": "track",
                        "guid": master_track.guid,
                        "name": master_track.name,
                    }
                ],
            )
            bus_analysis_details["issues_found"].append(message)

        # TODO: Add check for master_track.has_problematic_master_chain_order if this flag is more generic

        if bus_analysis_details["issues_found"]:
            self.detailed_analysis["master_bus_analysis"] = bus_analysis_details
        elif master_track:  # Ensure master_track exists before adding empty details
            self.detailed_analysis["master_bus_analysis"] = {
                "guid": master_track.guid,
                "name": master_track.name,
                "issues_found": [],
            }

    def _analyse_session_hygiene(self):
        """Analyzes session hygiene (track names, folder structure, empty items, etc.)."""
        hygiene_details: Dict[str, Any] = {"project_level": [], "track_level": []}
        
        # Analyze project-level hygiene first
        self._analyze_project_hygiene(hygiene_details)
        
        # Analyze track-level hygiene efficiently
        self._analyze_track_hygiene(hygiene_details)

        if hygiene_details["project_level"] or hygiene_details["track_level"]:
            self.detailed_analysis["session_hygiene_analysis"] = hygiene_details

    def _analyze_project_hygiene(self, hygiene_details: Dict[str, Any]):
        """Analyze project-level hygiene issues."""
        metadata = self.parsed_session.metadata
        
        # Batch project-level checks for efficiency
        project_checks = [
            (metadata.has_missing_markers, "HYGIENE_MISSING_MARKERS", 
             "Project is missing markers, which can help with navigation and song structure.",
             "Consider adding markers for key song sections."),
            (metadata.has_missing_regions, "HYGIENE_MISSING_REGIONS",
             "Project is missing regions, which can be useful for selections and exports.",
             "Consider adding regions for song sections or export areas."),
            (metadata.has_missing_notes, "HYGIENE_MISSING_NOTES",
             "Project notes are empty.",
             "Consider adding project notes for mix versions, collaborators, or other relevant info.")
        ]
        
        for has_issue, rule_id, message, recommendation in project_checks:
            if has_issue:
                self._add_issue(
                    rule_id=rule_id,
                    category="Session Hygiene",
                    severity="info",
                    message=message,
                    recommendation=recommendation,
                    affected_elements=[{"type": "project"}],
                    is_mastering_critical=False,
                )
                hygiene_details["project_level"].append(message)

    def _analyze_track_hygiene(self, hygiene_details: Dict[str, Any]):
        """Analyze track-level hygiene issues efficiently."""
        # Pre-filter tracks with potential hygiene issues to reduce processing
        tracks_to_check = [
            track for track in self.parsed_session.tracks
            if (track.has_generic_name or track.not_in_folder_structure or 
                track.has_empty_items or track.has_duplicate_name)
        ]

        # Process only tracks with potential hygiene issues for efficiency
        for track in tracks_to_check:
            track_hygiene_issues = []
            
            # Check for plugin hygiene issues on individual tracks
            track_plugin_hygiene = self._check_track_level_plugin_hygiene(track)
            track_hygiene_issues.extend(track_plugin_hygiene)
            
            # Batch track hygiene checks
            track_checks = [
                (track.has_generic_name, "HYGIENE_GENERIC_TRACK_NAME",
                 f"Track '{track.name}' has a generic name.",
                 f"Consider renaming track '{track.name}' to be more descriptive."),
                (track.has_empty_items, "HYGIENE_EMPTY_ITEMS_ON_TRACK",
                 f"Track '{track.name}' contains one or more empty items.",
                 f"Review track '{track.name}' for empty items and remove them if they are not needed."),
                (track.has_duplicate_name, "HYGIENE_DUPLICATE_TRACK_NAME",
                 f"Track name '{track.name}' is duplicated in the project.",
                 f"Rename track '{track.name}' to ensure all track names are unique for clarity.")
            ]
            
            for has_issue, rule_id, message, recommendation in track_checks:
                if has_issue:
                    severity = "warning" if rule_id == "HYGIENE_DUPLICATE_TRACK_NAME" else "info"
                    self._add_issue(
                        rule_id=rule_id,
                        category="Session Hygiene",
                        severity=severity,
                        message=message,
                        recommendation=recommendation,
                        affected_elements=[{"type": "track", "guid": track.guid, "name": track.name}],
                        is_mastering_critical=False,
                    )
                    track_hygiene_issues.append(message)
            
            # Special check for folder structure (requires checking other tracks)
            if track.not_in_folder_structure and any(t.is_folder for t in self.parsed_session.tracks):
                message = f"Track '{track.name}' is not part of any folder structure."
                self._add_issue(
                    rule_id="HYGIENE_NOT_IN_FOLDER",
                    category="Session Hygiene",
                    severity="info",
                    message=message,
                    recommendation=f"Consider organizing track '{track.name}' into a folder if appropriate for the project structure.",
                    affected_elements=[
                        {"type": "track", "guid": track.guid, "name": track.name}
                    ],
                    is_mastering_critical=False,
                )
                track_hygiene_issues.append(message)

            if track_hygiene_issues:
                hygiene_details["track_level"].append(
                    {
                        "track_guid": track.guid,
                        "track_name": track.name,
                        "issues": track_hygiene_issues,
                    }
                )
                track_hygiene_issues.append(message)

            if track_hygiene_issues:
                hygiene_details["track_level"].append(
                    {
                        "track_guid": track.guid,
                        "track_name": track.name,
                        "issues": track_hygiene_issues,
                    }
                )

    def _check_track_level_plugin_hygiene(self, track):
        """Check for mixing best practices on individual tracks"""
        hygiene_issues = []
        
        # Skip master track - that's handled by master bus analysis
        if track.type == "MASTER":
            return hygiene_issues
            
        for fx_plugin_result in track.fx:
            plugin_name = fx_plugin_result.get("name", "Unknown Plugin")
            plugin_category = fx_plugin_result.get("category", "Unknown").lower()
            is_bypassed = fx_plugin_result.get("is_bypassed", False)
            plugin_confidence = fx_plugin_result.get("confidence", 0.0)
            
            # Flag mastering tools on individual tracks as hygiene issue
            if not is_bypassed and plugin_category == "limiter" and plugin_confidence >= 0.8:
                MASTERING_LIMITERS = ["pro-l", "ozone maximizer", "elevate", "l1", "l2", "l3", "limitless", "elephant"]
                if any(limiter in plugin_name.lower() for limiter in MASTERING_LIMITERS):
                    message = f"Mastering limiter '{plugin_name}' found on individual track '{track.name}' - consider moving to master bus or removing for mix"
                    self._add_issue(
                        rule_id="HYGIENE_MASTERING_TOOL_ON_TRACK",
                        category="Session Hygiene",
                        severity="info",
                        message=message,
                        recommendation=f"Review '{plugin_name}' on track '{track.name}'. Mastering tools are typically applied during mastering, not mixing.",
                        affected_elements=[
                            {
                                "type": "plugin",
                                "guid": fx_plugin_result.get("guid"),
                                "name": plugin_name,
                                "details": {
                                    "track_name": track.name,
                                    "track_guid": track.guid,
                                },
                            }
                        ],
                        is_mastering_critical=False,
                    )
                    hygiene_issues.append(message)
        
        return hygiene_issues

    def _analyse_file_references(self):
        """Analyzes file references for absolute paths, missing files (heuristic), sample rate/bit depth mismatches."""
        metadata = self.parsed_session.metadata
        file_ref_summary = metadata.file_reference_analysis or {}

        analysis_details: Dict[str, Any] = {
            "summary_messages": file_ref_summary.get("summary_messages", []),
            "problematic_files": [],
        }

        # Project-level summary issues from FileReferenceExtractor
        if file_ref_summary.get("absolute_paths_detected"):
            self._add_issue(
                rule_id="FILE_REF_ABSOLUTE_PATHS",
                category="File References",
                severity="critical",  # Often critical for portability
                message="Project contains absolute file paths. This can cause issues when moving the project to another system.",
                recommendation="Convert absolute paths to relative paths or ensure all media is within the project directory.",
                affected_elements=[{"type": "project"}],
            )
        if file_ref_summary.get("system_temp_paths_detected"):
            self._add_issue(
                rule_id="FILE_REF_TEMP_PATHS",
                category="File References",
                severity="critical",
                message="Project references files in system temporary directories. These files may be lost.",
                recommendation="Copy temporary files into the project directory and update references.",
                affected_elements=[{"type": "project"}],
            )
        if file_ref_summary.get(
            "external_paths_detected"
        ):  # Relative paths outside common media folders
            self._add_issue(
                rule_id="FILE_REF_EXTERNAL_RELATIVE_PATHS",
                category="File References",
                severity="warning",
                message="Project uses relative paths pointing outside common project media folders. This might affect portability.",
                recommendation="Consider consolidating all media into a 'Media' or 'Audio' subfolder within the project directory.",
                affected_elements=[{"type": "project"}],
            )
        if file_ref_summary.get("sample_rate_mismatches_detected"):
            self._add_issue(
                rule_id="FILE_REF_SAMPLE_RATE_MISMATCH",
                category="File References",
                severity="warning",
                message="One or more media files have sample rates different from the project sample rate.",
                recommendation="Review mismatched files. Consider converting them to the project sample rate if appropriate.",
                affected_elements=[{"type": "project"}],  # Specific files listed below
            )
        if file_ref_summary.get("bit_depth_mismatches_detected"):
            self._add_issue(
                rule_id="FILE_REF_BIT_DEPTH_MISMATCH",
                category="File References",
                severity="info",  # Usually less critical than sample rate
                message="One or more media files have bit depths different from the project bit depth.",
                recommendation="Review mismatched files. This is often acceptable but be aware of potential conversions.",
                affected_elements=[{"type": "project"}],  # Specific files listed below
            )

        # Item-specific file reference issues (already flagged on Item models by FileReferenceExtractor)
        for track in self.parsed_session.tracks:
            for item in track.items:
                item_file_issues = []
                if item.has_absolute_path:
                    msg = f"Item '{item.name}' on track '{track.name}' uses an absolute file path."
                    # This specific issue might be redundant if a project-level one is already raised,
                    # but can be useful for pinpointing.
                    # self._add_issue(...) # Decided not to add individual item issues if project-level covers it broadly.
                    item_file_issues.append("Absolute path")
                if item.has_system_temp_path:
                    msg = f"Item '{item.name}' on track '{track.name}' references a system/temp path."
                    item_file_issues.append("System/temp path")
                if item.has_external_relative_path:
                    msg = f"Item '{item.name}' on track '{track.name}' uses an external relative path."
                    item_file_issues.append("External relative path")

                # Sample rate/bit depth mismatches for individual items (if Source model has this info)
                for take in item.takes:
                    if take.source:
                        if (
                            take.source.sample_rate
                            and take.source.sample_rate != metadata.sample_rate
                        ):
                            msg = f"Take '{take.name}' in item '{item.name}' (track '{track.name}') has sample rate {take.source.sample_rate} Hz, project is {metadata.sample_rate} Hz."
                            # This is more specific than the project-level flag.
                            self._add_issue(
                                rule_id="TAKE_SAMPLE_RATE_MISMATCH",
                                category="File References",
                                severity="warning",
                                message=msg,
                                recommendation=f"Consider converting '{take.source.file_path}' to {metadata.sample_rate} Hz.",
                                affected_elements=[
                                    {
                                        "type": "take",
                                        "guid": take.guid,
                                        "name": take.name,
                                        "details": {
                                            "item_name": item.name,
                                            "track_name": track.name,
                                            "file_path": take.source.file_path,
                                        },
                                    }
                                ],
                            )
                            item_file_issues.append(
                                f"Sample rate mismatch ({take.source.sample_rate} Hz)"
                            )
                        if (
                            take.source.bit_depth
                            and metadata.project_bit_depth
                            and take.source.bit_depth != metadata.project_bit_depth
                        ):
                            msg = f"Take '{take.name}' in item '{item.name}' (track '{track.name}') has bit depth {take.source.bit_depth}-bit, project is {metadata.project_bit_depth}-bit."
                            self._add_issue(
                                rule_id="TAKE_BIT_DEPTH_MISMATCH",
                                category="File References",
                                severity="info",
                                message=msg,
                                recommendation="Be aware of potential bit depth conversions during processing or export.",
                                affected_elements=[
                                    {
                                        "type": "take",
                                        "guid": take.guid,
                                        "name": take.name,
                                        "details": {
                                            "item_name": item.name,
                                            "track_name": track.name,
                                            "file_path": take.source.file_path,
                                        },
                                    }
                                ],
                            )
                            item_file_issues.append(
                                f"Bit depth mismatch ({take.source.bit_depth}-bit)"
                            )

                if item_file_issues:
                    analysis_details["problematic_files"].append(
                        {
                            "item_guid": item.guid,
                            "item_name": item.name,
                            "track_name": track.name,
                            "issues": item_file_issues,
                            "source_file": (
                                item.takes[0].source.file_path
                                if item.takes and item.takes[0].source
                                else "N/A"
                            ),
                        }
                    )

        if (
            analysis_details["problematic_files"]
            or analysis_details["summary_messages"]
        ):
            self.detailed_analysis["file_reference_analysis"] = analysis_details

    def _analyse_item_properties(self):
        """Analyzes item properties like fades, reverse status, playrate."""
        item_issues_details = []
        for track in self.parsed_session.tracks:
            for item in track.items:
                item_detail = {
                    "item_guid": item.guid,
                    "item_name": item.name,
                    "track_guid": track.guid,
                    "track_name": track.name,
                    "issues": [],
                }

                # Skip fade analysis for MIDI items
                is_midi_item = item.name.lower().endswith("-midi") or "midi" in item.name.lower()
                
                # Abrupt Start (skip MIDI items and reduce severity)
                if item.has_abrupt_start and not is_midi_item:
                    message = f"Item '{item.name}' on track '{track.name}' has an abrupt start (short/no fade-in)."
                    self._add_issue(
                        rule_id="ITEM_ABRUPT_START",
                        category="Item Properties",
                        severity="info",  # Reduced from warning to info
                        message=message,
                        recommendation="Consider adding a short fade-in (e.g., 10-50ms) to avoid potential clicks.",
                        affected_elements=[
                            {
                                "type": "item",
                                "guid": item.guid,
                                "name": item.name,
                                "details": {"track_name": track.name},
                            }
                        ],
                    )
                    item_detail["issues"].append(message)

                # Abrupt End (skip MIDI items and reduce severity)
                if item.has_abrupt_end and not is_midi_item:
                    message = f"Item '{item.name}' on track '{track.name}' has an abrupt end (short/no fade-out)."
                    self._add_issue(
                        rule_id="ITEM_ABRUPT_END",
                        category="Item Properties",
                        severity="info",  # Reduced from warning to info
                        message=message,
                        recommendation="Consider adding a short fade-out (e.g., 10-50ms) to avoid potential clicks or abrupt cuts.",
                        affected_elements=[
                            {
                                "type": "item",
                                "guid": item.guid,
                                "name": item.name,
                                "details": {"track_name": track.name},
                            }
                        ],
                    )
                    item_detail["issues"].append(message)

                # Reversed Item
                if item.is_reversed:
                    message = f"Item '{item.name}' on track '{track.name}' is reversed."
                    self._add_issue(
                        rule_id="ITEM_REVERSED",
                        category="Item Properties",
                        severity="info",  # Usually informational, but could be warning depending on context/genre
                        message=message,
                        recommendation="Verify if the item is intentionally reversed.",
                        affected_elements=[
                            {
                                "type": "item",
                                "guid": item.guid,
                                "name": item.name,
                                "details": {"track_name": track.name},
                            }
                        ],
                    )
                    item_detail["issues"].append(message)

                # Non-standard Playrate
                # Using a small epsilon for float comparison
                if abs(item.playrate - 1.0) > 0.00001:
                    message = f"Item '{item.name}' on track '{track.name}' has a non-standard playrate of {item.playrate:.2f}."
                    self._add_issue(
                        rule_id="ITEM_NONSTANDARD_PLAYRATE",
                        category="Item Properties",
                        severity="warning",
                        message=message,
                        recommendation="Ensure non-standard playrate is intentional. This can affect pitch and timing.",
                        affected_elements=[
                            {
                                "type": "item",
                                "guid": item.guid,
                                "name": item.name,
                                "details": {
                                    "track_name": track.name,
                                    "playrate": item.playrate,
                                },
                            }
                        ],
                    )
                    item_detail["issues"].append(message)

                # Check for unusual properties warning string set by core.py (consolidates multiple minor item issues)
                if item.has_unusual_properties_warning:
                    # This message from core.py might be a bit redundant if we're creating specific issues above,
                    # but can serve as a fallback or summary.
                    # For now, let's not add it as a separate issue if more specific ones are generated.
                    # If specific checks above are not comprehensive, this could be an 'info' issue.
                    pass

                if item_detail["issues"]:
                    item_issues_details.append(item_detail)

        if item_issues_details:
            self.detailed_analysis["item_property_analysis"] = item_issues_details

    def _analyse_project_settings(self):
        """Analyzes project settings like bit depth, sample rate, project info, boundaries, and stereo balance."""
        metadata = self.parsed_session.metadata
        project_settings_details: Dict[str, Any] = {}  # For self.detailed_analysis

        # Bit Depth
        if metadata.is_suboptimal_bit_depth:
            self._add_issue(
                rule_id="PROJECT_SUBOPTIMAL_BIT_DEPTH",
                category="Project Settings",
                severity="warning",
                message=f"Project bit depth is {metadata.project_bit_depth}-bit, which may be suboptimal for mastering.",
                recommendation="Consider working at 24-bit or higher for mastering.",
                affected_elements=[{"type": "project"}],
            )
        project_settings_details["bit_depth"] = {
            "value": metadata.project_bit_depth,
            "is_suboptimal": metadata.is_suboptimal_bit_depth,
        }

        # Sample Rate
        if metadata.is_nonstandard_sample_rate:
            self._add_issue(
                rule_id="PROJECT_NONSTANDARD_SAMPLE_RATE",
                category="Project Settings",
                severity="warning",
                message=f"Project sample rate is {metadata.sample_rate} Hz, which is non-standard.",
                recommendation="Ensure the sample rate is appropriate for the target medium (e.g., 44.1kHz, 48kHz, 88.2kHz, 96kHz).",
                affected_elements=[{"type": "project"}],
            )
        project_settings_details["sample_rate"] = {
            "value": metadata.sample_rate,
            "is_nonstandard": metadata.is_nonstandard_sample_rate,
        }

        # Project Info Completeness
        if metadata.has_incomplete_project_info:
            self._add_issue(
                rule_id="PROJECT_INCOMPLETE_INFO",
                category="Project Settings",
                severity="info",
                message="Project metadata (e.g., title) is incomplete.",
                recommendation="Consider filling in project title in REAPER's project settings for better organization.",
                affected_elements=[{"type": "project"}],
            )
        project_settings_details["has_incomplete_info"] = (
            metadata.has_incomplete_project_info
        )

        # Project Boundary / Abrupt Start
        if metadata.first_item_has_abrupt_start_at_zero:
            self._add_issue(
                rule_id="PROJECT_ABRUPT_START_AT_ZERO",
                category="Project Structure",
                severity="warning",
                message="The first item in the project starts at 0:00 with an abrupt (short/no) fade-in.",
                recommendation="Ensure there's adequate pre-roll or a gentle fade-in at the project start to avoid clicks or immediate transients.",
                affected_elements=[{"type": "project"}],
            )
        project_settings_details["first_item_abrupt_start_at_zero"] = (
            metadata.first_item_has_abrupt_start_at_zero
        )

        # Stereo Balance
        if metadata.stereo_balance_issue_description:
            self._add_issue(
                rule_id="PROJECT_STEREO_BALANCE",
                category="Project Mix",
                severity="warning",  # Could be info depending on strictness
                message=f"Potential stereo balance issue: {metadata.stereo_balance_issue_description}",
                recommendation="Review overall stereo balance and panning.",
                affected_elements=[{"type": "project"}],
            )
        project_settings_details["stereo_balance_issue"] = (
            metadata.stereo_balance_issue_description
        )

        self.detailed_analysis["project_settings_analysis"] = project_settings_details

    def _analyse_track_specific_issues(self):
        """Analyzes track-specific issues like muted tracks, bypassed FX, record-armed."""
        track_issues_details = []
        for track in self.parsed_session.tracks:
            track_detail = {"guid": track.guid, "name": track.name, "issues": []}
            if track.muted:
                message = f"Track '{track.name}' is muted."
                self._add_issue(
                    rule_id="TRACK_MUTED",
                    category="Track Settings",
                    severity="warning",
                    message=message,
                    recommendation="Ensure all necessary tracks are unmuted before mixdown.",
                    affected_elements=[
                        {"type": "track", "guid": track.guid, "name": track.name}
                    ],
                )
                track_detail["issues"].append(message)

            if track.is_record_armed:
                message = f"Track '{track.name}' is record-armed."
                severity = (
                    "critical" if self.strict_mode else "warning"
                )  # More critical in strict mode
                self._add_issue(
                    rule_id="TRACK_RECORD_ARMED",
                    category="Track Settings",
                    severity=severity,
                    message=message,
                    recommendation="Disarm tracks not intended for recording before mixdown/export.",
                    affected_elements=[
                        {"type": "track", "guid": track.guid, "name": track.name}
                    ],
                )
                track_detail["issues"].append(message)

            if track.has_bypassed_fx_in_chain:
                # Check if any specific FX are bypassed
                bypassed_fx_names = [
                    fx.get("name", "Unknown FX")
                    for fx in track.fx
                    if fx.get("is_bypassed")
                ]
                if bypassed_fx_names:
                    message = f"Track '{track.name}' has bypassed FX in its chain: {', '.join(bypassed_fx_names)}."
                    self._add_issue(
                        rule_id="TRACK_BYPASSED_FX",
                        category="Track Settings",
                        severity="info",  # Usually info, could be warning if it's unusual for the track type/genre
                        message=message,
                        recommendation="Review bypassed plugins to ensure they are intentionally inactive.",
                        affected_elements=[
                            {
                                "type": "track",
                                "guid": track.guid,
                                "name": track.name,
                                "details": {"bypassed_plugins": bypassed_fx_names},
                            }
                        ],
                    )
                    track_detail["issues"].append(message)

            if track_detail["issues"]:
                track_issues_details.append(track_detail)

        if track_issues_details:
            self.detailed_analysis["track_specific_issues"] = track_issues_details

    def _analyse_master_output(self):
        """
        Analyzes master output configuration for mastering readiness.
        Phase D2: Critical technical checks - Master output validation.
        """
        from ..extractors.master_output_extractor import MasterOutputExtractor
        from ..infrastructure.parsing_context import ParsingContext
        
        # Create parsing context from current session data
        # Note: This requires access to the original project tree
        # For now, we'll analyze from the parsed session data
        master_track = next(
            (t for t in self.parsed_session.tracks if t.type == "MASTER"), None
        )
        
        if not master_track:
            self._add_issue(
                rule_id="MASTER_OUTPUT_NO_MASTER_TRACK",
                category="Master Output Analysis",
                severity="critical",
                message="No master track found for output analysis.",
                recommendation="Ensure the project has a properly configured master track.",
                affected_elements=[{"type": "project"}],
            )
            return
        
        master_output_details = {
            "master_track_guid": master_track.guid,
            "master_track_name": master_track.name,
            "issues_found": [],
        }
        
        # Analyze master track volume (if available in track data)
        # Note: This assumes track volume is stored in the track model
        if hasattr(master_track, 'volume'):
            master_volume = master_track.volume
            
            # Check for excessive gain (> +3dB potential for clipping)
            if master_volume > 3.0:
                message = f"Master track volume is set to +{master_volume:.1f}dB, which may cause clipping."
                self._add_issue(
                    rule_id="MASTER_OUTPUT_EXCESSIVE_GAIN",
                    category="Master Output Analysis", 
                    severity="critical",
                    message=message,
                    recommendation="Reduce master volume to ensure adequate headroom for mastering (typically -6dB to 0dB).",
                    affected_elements=[
                        {
                            "type": "track",
                            "guid": master_track.guid,
                            "name": master_track.name,
                            "details": {"volume": master_volume}
                        }
                    ],
                )
                master_output_details["issues_found"].append(message)
            
            # Check for insufficient headroom (< -12dB might indicate gain staging issues)
            elif master_volume < -12.0:
                message = f"Master track volume is set to {master_volume:.1f}dB, which may indicate gain staging issues."
                self._add_issue(
                    rule_id="MASTER_OUTPUT_INSUFFICIENT_HEADROOM",
                    category="Master Output Analysis",
                    severity="warning", 
                    message=message,
                    recommendation="Review gain staging throughout the mix. Master volume should typically be between -6dB and 0dB.",
                    affected_elements=[
                        {
                            "type": "track",
                            "guid": master_track.guid,
                            "name": master_track.name,
                            "details": {"volume": master_volume}
                        }
                    ],
                )
                master_output_details["issues_found"].append(message)
        
        # Check for muted master track
        if master_track.muted:
            message = "Master track is muted - this will result in no audio output."
            self._add_issue(
                rule_id="MASTER_OUTPUT_MUTED",
                category="Master Output Analysis",
                severity="critical",
                message=message,
                recommendation="Unmute the master track before rendering or export.",
                affected_elements=[
                    {
                        "type": "track",
                        "guid": master_track.guid,
                        "name": master_track.name,
                    }
                ],
            )
            master_output_details["issues_found"].append(message)
        
        # Check for master bus automation (informational warning)
        if hasattr(master_track, 'has_master_bus_automation') and master_track.has_master_bus_automation:
            message = "Master track has volume or pan automation."
            self._add_issue(
                rule_id="MASTER_OUTPUT_AUTOMATION_PRESENT", 
                category="Master Output Analysis",
                severity="info",
                message=message,
                recommendation="Ensure master fader automation is intentional. Mastering engineers typically prefer a static master fader.",
                affected_elements=[
                    {
                        "type": "track",
                        "guid": master_track.guid,
                        "name": master_track.name,
                    }
                ],
            )
            master_output_details["issues_found"].append(message)
        
        # Store analysis details
        self.detailed_analysis["master_output_analysis"] = master_output_details

    def _analyse_offline_media(self):
        """
        Analyzes offline and missing media files for mastering readiness.
        Phase D2: Critical technical checks - Offline media detection.
        """
        offline_media_details = {
            "offline_files": [],
            "missing_files": [],
            "total_media_files": 0,
            "total_offline": 0,
            "total_missing": 0,
        }
        
        # Track unique file paths to avoid duplicates
        offline_file_paths = set()
        missing_file_paths = set()
        
        # Track file path to affected elements mapping for detailed reporting
        offline_files_map = {}
        missing_files_map = {}
        
        # Check all items and their takes for offline media
        for track in self.parsed_session.tracks:
            for item in track.items:
                for take in item.takes:
                    if take.source and take.source.file_path:
                        offline_media_details["total_media_files"] += 1
                        
                        # Check for offline media
                        if take.source.is_offline:
                            if take.source.file_path not in offline_file_paths:
                                offline_file_paths.add(take.source.file_path)
                                offline_media_details["total_offline"] += 1
                                offline_files_map[take.source.file_path] = []
                            
                            offline_file_info = {
                                "file_path": take.source.file_path,
                                "track_name": track.name,
                                "item_name": item.name,
                                "take_name": take.name,
                            }
                            offline_media_details["offline_files"].append(offline_file_info)
                            offline_files_map[take.source.file_path].append({
                                "type": "take",
                                "guid": take.guid,
                                "name": take.name,
                                "details": {
                                    "track_name": track.name,
                                    "item_name": item.name,
                                    "file_path": take.source.file_path,
                                },
                            })
                        
                        # Check for missing media
                        if take.source.is_missing:
                            if take.source.file_path not in missing_file_paths:
                                missing_file_paths.add(take.source.file_path)
                                offline_media_details["total_missing"] += 1
                                missing_files_map[take.source.file_path] = []
                            
                            missing_file_info = {
                                "file_path": take.source.file_path,
                                "track_name": track.name,
                                "item_name": item.name,
                                "take_name": take.name,
                            }
                            offline_media_details["missing_files"].append(missing_file_info)
                            missing_files_map[take.source.file_path].append({
                                "type": "take",
                                "guid": take.guid,
                                "name": take.name,
                                "details": {
                                    "track_name": track.name,
                                    "item_name": item.name,
                                    "file_path": take.source.file_path,
                                },
                            })
        
        # Create deduplicated issues for offline files
        for file_path, affected_elements in offline_files_map.items():
            track_names = list(set(elem["details"]["track_name"] for elem in affected_elements))
            track_list = ", ".join(track_names[:3])
            if len(track_names) > 3:
                track_list += f" and {len(track_names) - 3} more"
            
            message = f"Offline media file: '{file_path}' (used on {track_list})"
            self._add_issue(
                rule_id="OFFLINE_MEDIA_DETECTED",
                category="Media Files",
                severity="critical",
                message=message,
                recommendation="Reconnect offline media files before rendering or export. Check that all media files are accessible.",
                affected_elements=affected_elements,
            )
        
        # Create deduplicated issues for missing files
        for file_path, affected_elements in missing_files_map.items():
            track_names = list(set(elem["details"]["track_name"] for elem in affected_elements))
            track_list = ", ".join(track_names[:3])
            if len(track_names) > 3:
                track_list += f" and {len(track_names) - 3} more"
            
            message = f"Missing media file: '{file_path}' (used on {track_list})"
            self._add_issue(
                rule_id="MISSING_MEDIA_FILE",
                category="Media Files",
                severity="critical",
                message=message,
                recommendation="Locate and reconnect missing media files. Consider consolidating project media into the project directory.",
                affected_elements=affected_elements,
            )
        
        # Generate summary issues if offline/missing media found
        if offline_media_details["total_offline"] > 0:
            summary_message = f"Project contains {offline_media_details['total_offline']} offline media file(s)"
            self._add_issue(
                rule_id="PROJECT_HAS_OFFLINE_MEDIA",
                category="Media Files",
                severity="critical",
                message=summary_message,
                recommendation="Reconnect all offline media before proceeding to mastering. Offline media will result in silent or missing audio.",
                affected_elements=[{"type": "project"}],
            )
        
        if offline_media_details["total_missing"] > 0:
            summary_message = f"Project contains {offline_media_details['total_missing']} unique missing media file(s)"
            self._add_issue(
                rule_id="PROJECT_HAS_MISSING_MEDIA",
                category="Media Files", 
                severity="critical",
                message=summary_message,
                recommendation="Locate and reconnect all missing media files. Missing media will result in silent gaps in the audio.",
                affected_elements=[{"type": "project"}],
            )
        
        # Store analysis details
        if offline_media_details["total_offline"] > 0 or offline_media_details["total_missing"] > 0:
            self.detailed_analysis["offline_media_analysis"] = offline_media_details

    def _analyse_sample_rate_consistency(self):
        """
        Analyzes sample rate consistency across all media files for mastering readiness.
        Phase D2: Critical technical checks - Sample rate consistency.
        """
        project_sample_rate = self.parsed_session.metadata.sample_rate
        sample_rate_details = {
            "project_sample_rate": project_sample_rate,
            "mismatched_files": [],
            "sample_rates_found": set(),
            "total_files_checked": 0,
            "total_mismatches": 0,
        }
        
        # Check all media files for sample rate consistency
        for track in self.parsed_session.tracks:
            for item in track.items:
                for take in item.takes:
                    if take.source and take.source.file_path and take.source.sample_rate:
                        sample_rate_details["total_files_checked"] += 1
                        sample_rate_details["sample_rates_found"].add(take.source.sample_rate)
                        
                        # Check for sample rate mismatch
                        if take.source.sample_rate != project_sample_rate:
                            sample_rate_details["total_mismatches"] += 1
                            mismatch_info = {
                                "file_path": take.source.file_path,
                                "file_sample_rate": take.source.sample_rate,
                                "track_name": track.name,
                                "item_name": item.name,
                                "take_name": take.name,
                            }
                            sample_rate_details["mismatched_files"].append(mismatch_info)
                            
                            # Determine severity based on sample rate relationship
                            if take.source.sample_rate % project_sample_rate == 0 or project_sample_rate % take.source.sample_rate == 0:
                                # Clean conversion (e.g., 48kHz <-> 96kHz)
                                severity = "warning"
                                recommendation = f"Sample rate can be cleanly converted from {take.source.sample_rate}Hz to {project_sample_rate}Hz, but consider matching project sample rate for optimal quality."
                            else:
                                # Messy conversion (e.g., 44.1kHz <-> 48kHz)
                                severity = "critical"
                                recommendation = f"Sample rate conversion from {take.source.sample_rate}Hz to {project_sample_rate}Hz may introduce artifacts. Consider using media at the project sample rate."
                            
                            message = f"Sample rate mismatch: '{take.source.file_path}' is {take.source.sample_rate}Hz, project is {project_sample_rate}Hz"
                            self._add_issue(
                                rule_id="SAMPLE_RATE_MISMATCH",
                                category="Media Files",
                                severity=severity,
                                message=message,
                                recommendation=recommendation,
                                affected_elements=[
                                    {
                                        "type": "take",
                                        "guid": take.guid,
                                        "name": take.name,
                                        "details": {
                                            "track_name": track.name,
                                            "item_name": item.name,
                                            "file_path": take.source.file_path,
                                            "file_sample_rate": take.source.sample_rate,
                                            "project_sample_rate": project_sample_rate,
                                        },
                                    }
                                ],
                            )
        
        # Convert set to list for JSON serialization
        sample_rate_details["sample_rates_found"] = list(sample_rate_details["sample_rates_found"])
        
        # Generate summary issue if mismatches found
        if sample_rate_details["total_mismatches"] > 0:
            unique_rates = len(sample_rate_details["sample_rates_found"])
            if unique_rates > 1:
                summary_message = f"Project contains media files with {unique_rates} different sample rates: {', '.join(map(str, sorted(sample_rate_details['sample_rates_found'])))}Hz"
                self._add_issue(
                    rule_id="PROJECT_MIXED_SAMPLE_RATES",
                    category="Media Files",
                    severity="warning",
                    message=summary_message,
                    recommendation="For optimal quality, consider converting all media to the project sample rate before mastering.",
                    affected_elements=[{"type": "project"}],
                )
        
        # Store analysis details
        if sample_rate_details["total_mismatches"] > 0:
            self.detailed_analysis["sample_rate_analysis"] = sample_rate_details

    def _analyse_time_stretch_and_playrate(self):
        """
        Analyzes time-stretch and playrate issues for mastering readiness.
        Phase D2: Critical technical checks - Time-stretch and playrate detection.
        """
        playrate_details = {
            "modified_playrate_items": [],
            "stretched_items": [],
            "pitch_shifted_items": [],
            "total_items_checked": 0,
            "total_modified": 0,
        }
        
        # Check all items for playrate and time-stretch issues
        for track in self.parsed_session.tracks:
            for item in track.items:
                playrate_details["total_items_checked"] += 1
                
                # Check for non-standard playrate (not 1.0)
                if abs(item.playrate - 1.0) > 0.00001:
                    playrate_details["total_modified"] += 1
                    
                    # Categorize the type of modification
                    if item.playrate > 1.0:
                        # Faster playrate - pitch and tempo increase
                        playrate_type = "Speed increase"
                        severity = "warning"
                        pitch_change = f"+{20 * abs(item.playrate - 1.0):.1f} cents"
                    else:
                        # Slower playrate - pitch and tempo decrease
                        playrate_type = "Speed decrease" 
                        severity = "warning"
                        pitch_change = f"-{20 * abs(item.playrate - 1.0):.1f} cents"
                    
                    # Check for extreme playrate changes
                    if abs(item.playrate - 1.0) > 0.1:  # More than 10% change
                        severity = "critical"
                        
                    playrate_info = {
                        "item_name": item.name,
                        "track_name": track.name,
                        "playrate": item.playrate,
                        "type": playrate_type,
                        "pitch_change": pitch_change,
                    }
                    playrate_details["modified_playrate_items"].append(playrate_info)
                    
                    message = f"Item '{item.name}' on track '{track.name}' has playrate {item.playrate:.3f} ({playrate_type})"
                    self._add_issue(
                        rule_id="ITEM_MODIFIED_PLAYRATE",
                        category="Item Properties",
                        severity=severity,
                        message=message,
                        recommendation=f"Playrate modification affects both pitch and timing. Consider if this is intentional or if time-stretching should be used instead.",
                        affected_elements=[
                            {
                                "type": "item",
                                "guid": item.guid,
                                "name": item.name,
                                "details": {
                                    "track_name": track.name,
                                    "playrate": item.playrate,
                                    "pitch_change": pitch_change,
                                },
                            }
                        ],
                    )
                
                # Check for stretched items (this would require parsing stretch markers or algorithms)
                # For now, we'll flag items with significant length vs source length differences
                # This is a heuristic - actual time-stretch detection would require more RPP parsing
                
                # Note: REAPER stores time-stretch information in complex ways
                # This is a simplified check for obvious issues
                pass  # Placeholder for more sophisticated stretch detection
        
        # Generate summary issue if modified playrates found
        if playrate_details["total_modified"] > 0:
            summary_message = f"Project contains {playrate_details['total_modified']} item(s) with modified playrate"
            self._add_issue(
                rule_id="PROJECT_HAS_MODIFIED_PLAYRATES",
                category="Item Properties",
                severity="info",
                message=summary_message,
                recommendation="Review all playrate modifications to ensure they are intentional. Consider using time-stretching algorithms for tempo changes without pitch alteration.",
                affected_elements=[{"type": "project"}],
            )
        
        # Store analysis details
        if playrate_details["total_modified"] > 0:
            self.detailed_analysis["playrate_analysis"] = playrate_details

    def _analyse_record_arm_safety(self):
        """
        Analyzes record-armed tracks for mastering readiness safety.
        Phase D2: Critical technical checks - Record arm safety.
        """
        record_arm_details = {
            "armed_tracks": [],
            "total_tracks_checked": 0,
            "total_armed": 0,
            "master_armed": False,
        }
        
        # Check all tracks for record arm status
        for track in self.parsed_session.tracks:
            record_arm_details["total_tracks_checked"] += 1
            
            if track.is_record_armed:
                record_arm_details["total_armed"] += 1
                
                armed_track_info = {
                    "track_name": track.name,
                    "track_guid": track.guid,
                    "track_type": track.type,
                }
                record_arm_details["armed_tracks"].append(armed_track_info)
                
                # Determine severity based on track type
                if track.type == "MASTER":
                    record_arm_details["master_armed"] = True
                    severity = "critical"
                    message = f"Master track is record-armed - this is extremely dangerous for mixdown/export"
                    recommendation = "IMMEDIATELY disarm the master track. Recording to the master track during mixdown will destroy your mix."
                else:
                    # Regular track armed
                    severity = "critical" if self.strict_mode else "warning"
                    message = f"Track '{track.name}' is record-armed during mixdown preparation"
                    recommendation = f"Disarm track '{track.name}' unless you intend to record to it. Armed tracks can accidentally overwrite audio during playback."
                
                self._add_issue(
                    rule_id="TRACK_RECORD_ARMED_SAFETY",
                    category="Track Safety",
                    severity=severity,
                    message=message,
                    recommendation=recommendation,
                    affected_elements=[
                        {
                            "type": "track",
                            "guid": track.guid,
                            "name": track.name,
                            "details": {
                                "track_type": track.type,
                                "is_master": track.type == "MASTER",
                            },
                        }
                    ],
                )
        
        # Generate critical summary if master is armed
        if record_arm_details["master_armed"]:
            self._add_issue(
                rule_id="MASTER_TRACK_RECORD_ARMED",
                category="Track Safety",
                severity="critical",
                message="CRITICAL: Master track is record-armed - risk of destroying mix",
                recommendation="STOP: Disarm the master track immediately before any playback or export. This is a critical safety issue.",
                affected_elements=[{"type": "project"}],
            )
        
        # Generate summary if other tracks are armed
        if record_arm_details["total_armed"] > 0:
            if record_arm_details["master_armed"]:
                non_master_armed = record_arm_details["total_armed"] - 1
                if non_master_armed > 0:
                    summary_message = f"Project has {non_master_armed} additional record-armed track(s) besides master"
                    severity = "warning"
                else:
                    summary_message = None  # Already covered by master armed issue
                    severity = None
            else:
                summary_message = f"Project has {record_arm_details['total_armed']} record-armed track(s)"
                severity = "warning"
            
            if summary_message:
                self._add_issue(
                    rule_id="PROJECT_HAS_ARMED_TRACKS",
                    category="Track Safety",
                    severity=severity,
                    message=summary_message,
                    recommendation="Review all record-armed tracks. Disarm tracks unless you intend to record to them during this session.",
                    affected_elements=[{"type": "project"}],
                )
        
        # Store analysis details
        if record_arm_details["total_armed"] > 0:
            self.detailed_analysis["record_arm_analysis"] = record_arm_details


    def _group_issues(self, issues: List[MasteringAnalysisIssue]) -> tuple[List[IssueGroup], List[MasteringAnalysisIssue]]:
        """
        Groups similar issues together to reduce visual clutter.
        Returns (issue_groups, ungrouped_issues)
        """
        from collections import defaultdict
        
        # Group issues by rule_id and category
        groups = defaultdict(list)
        ungrouped = []
        
        for issue in issues:
            # Only group issues with the same rule_id 
            group_key = f"{issue.rule_id}_{issue.category}"
            groups[group_key].append(issue)
        
        issue_groups = []
        
        for group_key, group_issues in groups.items():
            if len(group_issues) == 1:
                # Single issues remain ungrouped for detailed display
                ungrouped.extend(group_issues)
            else:
                # Multiple issues get grouped
                first_issue = group_issues[0]
                
                # Determine highest severity in group
                severity_priority = {"critical": 3, "warning": 2, "info": 1, "pass": 0}
                highest_severity = max(
                    group_issues, 
                    key=lambda x: severity_priority.get(x.severity, 0)
                ).severity
                
                # Collect all affected elements
                all_affected_elements = []
                for issue in group_issues:
                    all_affected_elements.extend(issue.affected_elements)
                
                # Create descriptive title and summary
                element_types = set()
                for element in all_affected_elements:
                    element_types.add(element.get("type", "unknown"))
                
                if "track" in element_types:
                    title = f"{len(group_issues)} tracks with {first_issue.rule_id.lower().replace('_', ' ')}"
                elif "plugin" in element_types:
                    title = f"{len(group_issues)} plugins with {first_issue.rule_id.lower().replace('_', ' ')}"
                elif "item" in element_types:
                    title = f"{len(group_issues)} items with {first_issue.rule_id.lower().replace('_', ' ')}"
                else:
                    title = f"{len(group_issues)} issues: {first_issue.rule_id.lower().replace('_', ' ')}"
                
                # Create combined description
                track_names = []
                for element in all_affected_elements:
                    if element.get("type") == "track" and element.get("name"):
                        track_names.append(element["name"])
                    elif element.get("details", {}).get("track_name"):
                        track_names.append(element["details"]["track_name"])
                
                if track_names:
                    description = f"Affects tracks: {', '.join(set(track_names[:5]))}"
                    if len(set(track_names)) > 5:
                        description += f" and {len(set(track_names)) - 5} more"
                else:
                    description = first_issue.message
                
                # Create the issue group
                issue_group = IssueGroup(
                    rule_id=first_issue.rule_id,
                    category=first_issue.category,
                    severity=highest_severity,
                    title=title,
                    description=description,
                    count=len(group_issues),
                    is_mastering_critical=first_issue.is_mastering_critical,
                    affected_elements=all_affected_elements,
                    sample_issue=first_issue  # Keep first issue as representative sample
                )
                
                issue_groups.append(issue_group)
        
        return issue_groups, ungrouped
