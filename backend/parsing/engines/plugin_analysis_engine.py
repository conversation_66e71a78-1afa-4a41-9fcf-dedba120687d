import re
from typing import List, Any, Dict, Optional, TypedDict, <PERSON><PERSON>

from rpp import Element

from ..infrastructure.parsing_context import ParsingContext

# AutomationLane and AutomationPoint will be available via PluginAnalysisResult from models.py
from ..models import (
    PluginAnalysisResult,
    AutomationLane,
    AutomationPoint,
)  # Import the moved TypedDict and automation models
from ..extractors.automation_utils import (
    extract_automation_attributes,
    extract_automation_points,
    calculate_effective_points,
    extract_envelope_guid,
)

from .plugin_registry import PluginRegistry
from .plugin_blacklist_rules import PluginBlacklistRules
from .plugin_parameter_decoder import PluginParameterDecoder

# PluginAnalysisResult class definition removed from here, will be imported from models.py


class PluginAnalysisEngine:
    def __init__(self, context: ParsingContext):
        self.context = context
        self.registry = PluginRegistry()
        self.blacklist_rules = PluginBlacklistRules(context)
        self.parameter_decoder = PluginParameterDecoder(context)

    def _parse_parmenv_info(self, parm_env_element: Element, plugin_name: str) -> Tuple[
        Optional[int],
        Optional[str],
        str,
        Optional[float],
        Optional[float],
        Optional[float],
    ]:
        """
        Parses parameter information from a PARMENV element's first line.
        Adapted from plugin_automation_extractor.parse_parameter_info.
        Returns: (param_index, param_name, display_name, min_value, max_value, default_value)
        """
        param_index: Optional[int] = None
        param_name: Optional[str] = None
        display_name: str = f"{plugin_name}: Unknown Parameter"
        min_value: Optional[float] = None
        max_value: Optional[float] = None
        default_value: Optional[float] = None

        first_line = ""
        if isinstance(parm_env_element, Element):
            # For RPP library, parameter info is typically in attrib[0]
            if (
                parm_env_element.attrib and len(parm_env_element.attrib) > 0
            ):  # Check attrib for safety
                first_line = parm_env_element.attrib[0]

        if not first_line:
            # Try to find a simple text child if .text or .attrib[0] was empty/None
            # This is less common for PARMENV's info line but a fallback.
            if parm_env_element.children and isinstance(
                parm_env_element.children[0], str
            ):
                first_line = parm_env_element.children[0].strip()
            else:
                self.context.add_warning(
                    f"PARMENV for {plugin_name} has no parsable info line."
                )
                return (
                    param_index,
                    param_name,
                    display_name,
                    min_value,
                    max_value,
                    default_value,
                )

        parts = first_line.split(None, 1)  # Split at most once on whitespace
        param_part = parts[0]
        rest_of_line = parts[1] if len(parts) > 1 else ""

        if ":" in param_part:
            try:
                idx_str, name_str = param_part.split(":", 1)
                param_index = int(idx_str)
                param_name = name_str
            except ValueError:  # Not int:name, so treat whole as name
                param_name = param_part
        else:  # No colon, could be just index or just name
            try:
                param_index = int(param_part)
            except ValueError:
                param_name = param_part

        # Process numeric values and display name from rest_of_line
        # Example: 0 1 0.5 "Display Name" or "Display Name" 0 1 0.5 or just 0 1 0.5
        numeric_values_str = []

        # Try to extract quoted display name first
        match_quoted_name = re.search(r'"([^"]*)"', rest_of_line)
        if match_quoted_name:
            display_name_extracted = match_quoted_name.group(1)
            # Remove the quoted part to isolate numeric values
            numeric_part_str = rest_of_line.replace(
                f'"{display_name_extracted}"', ""
            ).strip()
            numeric_values_str = numeric_part_str.split()
        else:  # No quoted name, assume all are numbers or display name is unquoted
            # This part is tricky. If unquoted name, it could be anywhere.
            # For simplicity, assume if no quotes, rest_of_line is numbers or empty.
            numeric_values_str = rest_of_line.split()

        try:
            if len(numeric_values_str) >= 1:
                min_value = float(numeric_values_str[0])
            if len(numeric_values_str) >= 2:
                max_value = float(numeric_values_str[1])
            if len(numeric_values_str) >= 3:
                default_value = float(numeric_values_str[2])
        except (ValueError, IndexError):
            pass  # Keep them None if conversion fails

        if match_quoted_name:  # If we got a quoted name, use it
            display_name = display_name_extracted
        elif param_name:  # Fallback to constructed name
            display_name = f"{plugin_name}: {param_name}"
        elif param_index is not None:
            display_name = f"{plugin_name}: Parameter {param_index}"
        # else display_name remains default "Plugin: Unknown Parameter"

        return (
            param_index,
            param_name,
            display_name,
            min_value,
            max_value,
            default_value,
        )

    def _process_parmenv_element(
        self, parm_env_element: Element, plugin_name_for_lane: str
    ) -> Optional[AutomationLane]:
        """Helper to process a single PARMENV element into an AutomationLane."""
        (param_idx, param_name_str, disp_name, min_val, max_val, def_val) = (
            self._parse_parmenv_info(parm_env_element, plugin_name_for_lane)
        )

        if not disp_name and not param_name_str and param_idx is None:
            self.context.add_warning(
                f"Skipping PARMENV for {plugin_name_for_lane} due to missing identifier info."
            )
            return None

        lane_guid = extract_envelope_guid(parm_env_element)

        lane = AutomationLane(
            display_name=disp_name,
            guid=(
                lane_guid
                if lane_guid
                else f"NO_GUID_PARMENV_{plugin_name_for_lane}_{param_idx or param_name_str or 'unknown'}"
            ),
            parameter_name=param_name_str,
            parameter_index=param_idx,
            plugin_name=plugin_name_for_lane,
            min_value=min_val,
            max_value=max_val,
            default_value=def_val,
            points=[],  # Initialize empty
            effective_points=[],  # Initialize empty
        )
        extract_automation_attributes(parm_env_element, lane)
        lane.points = extract_automation_points(parm_env_element)
        lane.effective_points = calculate_effective_points(lane.points)
        return lane

    def _analyze_plugin_automation(
        self, plugin_data: PluginAnalysisResult, fx_element: Element
    ) -> List[AutomationLane]:
        """
        Analyzes automation for a specific plugin by finding PARMENV children of the FX element.
        """
        automation_lanes: List[AutomationLane] = []
        plugin_name_for_lane = plugin_data.get("name", "Unknown Plugin")
        if plugin_name_for_lane is None:
            plugin_name_for_lane = "Unknown Plugin"

        for parm_env_element in fx_element.findall("PARMENV"):
            lane = self._process_parmenv_element(parm_env_element, plugin_name_for_lane)
            if lane:
                automation_lanes.append(lane)

        return automation_lanes

    def _extract_vendor_hint(self, plugin_name: str) -> Optional[str]:
        """
        Extract vendor hint from plugin name for fuzzy matching.
        Examples:
        - "VST3: Pro-Q 4 (FabFilter)" -> "FabFilter"
        - "AU: elysia alpha master (Plugin Alliance)" -> "Plugin Alliance"
        """
        if not plugin_name:
            return None
            
        # Look for vendor in parentheses at the end
        import re
        # Match the last parentheses group, handling nested parentheses
        vendor_match = re.search(r'\(([^)]*(?:\([^)]*\)[^)]*)*)\)\s*$', plugin_name)
        if vendor_match:
            vendor = vendor_match.group(1).strip()
            # For nested cases like "Universal Audio (UADx)", extract the main vendor
            if '(' in vendor:
                vendor = vendor.split('(')[0].strip()
            return vendor
        
        return None

    def _extract_plugin_details(
        self, fx_element: Element, is_bypassed: bool, oversampling_rate: int
    ) -> Optional[PluginAnalysisResult]:
        details: PluginAnalysisResult = {
            "name": None,
            "type": None,
            "guid": None,
            "raw_text": str(fx_element.attrib) if fx_element.attrib else "",
            "encoded_params": None,
            "program_chunk": None,
            "is_bypassed": is_bypassed,
            "has_oversampling": oversampling_rate > 0,
            "oversampling_rate": oversampling_rate,
            "is_blacklisted_general": False,
            "is_blacklisted_master": False,
            "would_be_blacklisted_general_if_active": False,
            "would_be_blacklisted_master_if_active": False,
            "category": "Unknown",
            "parameters": None,
            "automation": [],  # Initialize automation as list
            "track_guid": "",
            "track_name": "",
        }

        # Use the working pattern from old fx_extractor.py
        # Plugin type comes from element.tag
        details["type"] = fx_element.tag

        # Plugin name comes from element.attrib[0] (the working pattern)
        if fx_element.attrib and len(fx_element.attrib) > 0:
            plugin_name = fx_element.attrib[0]
            # Remove quotes if present
            if plugin_name.startswith('"') and plugin_name.endswith('"'):
                plugin_name = plugin_name[1:-1]
            details["name"] = plugin_name
        else:
            self.context.add_warning(
                f"Plugin element {fx_element.tag} has no name in attrib[0]"
            )
            return None

        # Extract GUID from attrib if available (usually in later positions)
        for attr in fx_element.attrib:
            if attr.startswith("ID{") and attr.endswith("}"):
                match = re.search(r"\{([0-9A-Fa-f]{32})\}", attr)
                if match:
                    details["guid"] = match.group(1)
                    break

        # Extract parameter chunks
        for child_tag, detail_key in [
            ("VST_PARAM_CHUNK", "encoded_params"),
            ("VST_PROG_CHUNK", "program_chunk"),
            ("JS_STATE", "encoded_params"),  # JS often uses this for params
            ("AU_PRESET_CHUNK", "program_chunk"),
        ]:
            chunk = fx_element.find(child_tag)
            if chunk is not None:
                # For RPP library, chunk data might be in attrib or children
                if hasattr(chunk, "attrib") and chunk.attrib:
                    details[detail_key] = chunk.attrib[0]  # type: ignore
                elif (
                    hasattr(chunk, "children")
                    and chunk.children
                    and isinstance(chunk.children[0], str)
                ):
                    details[detail_key] = chunk.children[0]  # type: ignore

        if not details["name"] or details["name"].isspace():  # type: ignore
            self.context.add_warning(
                f"Failed to extract plugin name from element: {fx_element.tag}"
            )
            return None

        return details

    def analyze_fx_chain(
        self,
        fxchain_parent_element: Element,
        track_guid: str,
        track_name: str,
        is_master_bus: bool = False,
    ) -> List[PluginAnalysisResult]:
        plugin_results: List[PluginAnalysisResult] = []
        if fxchain_parent_element is None:
            return plugin_results

        fxchain_to_scan = fxchain_parent_element.find("FXCHAIN")
        if fxchain_to_scan is None:
            fxchain_to_scan = fxchain_parent_element

        pending_oversampling = 0
        pending_bypass = False
        for child_node in fxchain_to_scan.children:
            if isinstance(child_node, Element):
                # Check for plugin elements using correct tags (VST, AU, JS, etc.)
                if child_node.tag in ["VST", "VST3", "AU", "JS", "REWIRE", "DX"]:
                    plugin_data = self._extract_plugin_details(
                        child_node, pending_bypass, pending_oversampling
                    )
                    if not plugin_data:
                        pending_bypass = False
                        pending_oversampling = 0
                        continue
                    plugin_data["track_guid"] = track_guid
                    plugin_data["track_name"] = track_name

                    # Extract vendor hint from plugin name for fuzzy matching
                    vendor_hint = self._extract_vendor_hint(plugin_data["name"])
                    registry_info = self.registry.get_plugin_info(plugin_data["name"], vendor_hint)
                    plugin_data["category"] = registry_info.get("category", "Unknown")
                    plugin_data["confidence"] = self.registry.get_confidence(
                        plugin_data["name"]
                    )  # Add confidence score
                    decoder_key = registry_info.get("decoder")
                    self.blacklist_rules.check_plugin(plugin_data, is_master_bus)
                    if decoder_key:
                        plugin_data["parameters"] = self.parameter_decoder.decode(
                            child_node, decoder_key
                        )

                    # Analyze and add plugin automation
                    plugin_data["automation"] = self._analyze_plugin_automation(
                        plugin_data, child_node
                    )  # child_node is fx_element

                    plugin_results.append(plugin_data)
                    pending_bypass = False
                    pending_oversampling = 0
                elif (
                    child_node.tag == "BYPASS"
                    and hasattr(child_node, "attrib")
                    and child_node.attrib
                ):
                    try:
                        pending_bypass = child_node.attrib[0] == "1"
                    except (IndexError, ValueError):
                        pending_bypass = False
                elif (
                    child_node.tag == "NEXT_FX_OVERSAMPLE"
                    and hasattr(child_node, "attrib")
                    and child_node.attrib
                ):
                    try:
                        pending_oversampling = int(child_node.attrib[0])
                    except (ValueError, IndexError):
                        pending_oversampling = 0
            elif isinstance(child_node, list):
                if len(child_node) > 1 and child_node[0] == "NEXT_FX_OVERSAMPLE":
                    try:
                        pending_oversampling = int(child_node[1])
                    except (ValueError, IndexError):
                        pending_oversampling = 0
                elif len(child_node) > 1 and child_node[0] == "BYPASS":
                    try:
                        pending_bypass = child_node[1] == "1"
                    except IndexError:
                        pending_bypass = False

        if is_master_bus and plugin_results:
            master_bus_analysis_flags = self.blacklist_rules.analyze_master_bus_chain(plugin_results)  # type: ignore
            if not hasattr(self.context, "custom_analysis_results"):
                self.context.custom_analysis_results = {}  # type: ignore
            self.context.custom_analysis_results["master_bus_chain"] = master_bus_analysis_flags  # type: ignore
        return plugin_results
