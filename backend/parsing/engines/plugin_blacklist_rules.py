from typing import List, Dict, Any
from ..infrastructure.parsing_context import <PERSON>rsing<PERSON>ontex<PERSON>
from .plugin_data import KNOWN_METERING_PLUGINS  # Import from new plugin_data structure

# Default patterns if not found in genre_rules, can be expanded or moved to genre_rules.py baseline
DEFAULT_GENERAL_BLACKLIST_KEYWORDS = ["demo", "trial"]
# Use KNOWN_METERING_PLUGINS directly or as a fallback if genre_rules don't specify
DEFAULT_MASTER_BUS_ACCEPTABLE_METERING_KEYWORDS = KNOWN_METERING_PLUGINS


class PluginBlacklistRules:
    def __init__(self, context: ParsingContext):
        self.context = context
        # genre_rules are already loaded in parsing_context by __init__
        # self.genre_rules: Dict[str, Any] = self.context.genre_rules

    def check_plugin(
        self, plugin_data: Dict[str, Any], is_master_bus: bool
    ) -> None:  # plugin_data is PluginAnalysisResult (a Dict)
        """
        Checks a plugin against blacklist rules from genre_rules and updates its flags.
        'plugin_data' object is expected to have attributes like 'name', 'category', 'is_bypassed', etc.
        """
        if (
            not plugin_data.get("name")
            or plugin_data.get("is_blacklisted_general") is None
        ):
            self.context.add_warning(
                f"Plugin data for blacklist check is malformed: {plugin_data.get('name')}"
            )
            return

        plugin_name_lower = plugin_data["name"].lower()
        plugin_category = plugin_data.get("category", "Unknown").lower()
        plugin_confidence = plugin_data.get("confidence", 0.0)

        # --- General Blacklist (Applies to all tracks) ---
        # Uses keywords and checks against plugin_blacklist_track from genre_rules

        # 1. Keyword-based general blacklist (e.g., "demo", "trial")
        general_keywords = self.context.genre_rules.get(
            "plugin_blacklist_track", {}
        ).get("keywords", DEFAULT_GENERAL_BLACKLIST_KEYWORDS)
        for pattern in general_keywords:
            if pattern.lower() in plugin_name_lower:
                plugin_data["is_blacklisted_general"] = True
                break

        # 2. Type-based general blacklist removed - focus on master bus analysis only
        # Individual track plugin choice is left to mixing engineer discretion

        # --- Master Bus Specific Checks ---
        if is_master_bus:
            master_rules = self.context.genre_rules.get("plugin_blacklist_master", {})
            is_acceptable_on_master = False

            # Check if it's an acceptable metering plugin (these are usually fine)
            metering_keywords = self.context.genre_rules.get(
                "master_bus_acceptable_metering_keywords",
                DEFAULT_MASTER_BUS_ACCEPTABLE_METERING_KEYWORDS,
            )
            for pattern in metering_keywords:
                if pattern.lower() in plugin_name_lower:
                    is_acceptable_on_master = True
                    break

            if not is_acceptable_on_master:
                # Check against master bus blacklist rules by category and confidence
                category_to_rule_key = {
                    "limiter": "limiters",
                    "clipper": "clippers",
                    "mastering suite": "ozone_maximizer_specific",
                    "compressor": "compressors_on_master",
                    "multiband compressor": "multiband_on_master",
                    "spectral": "spectral_balancers",
                    "saturation": "saturation_on_master",
                    "eq": "eq_on_master",
                    "stereo imaging": "stereo_imaging_on_master",
                }

                rule_key = category_to_rule_key.get(plugin_category)
                if rule_key:
                    rule_for_category = master_rules.get(rule_key)

                    # Apply different treatment based on category
                    review_categories = [
                        "compressors_on_master", "multiband_on_master",
                        "saturation_on_master", "eq_on_master", "stereo_imaging_on_master"
                    ]
                    if rule_key in review_categories:
                        # These get review_recommended treatment
                        if rule_for_category == "review_recommended" and plugin_confidence >= 0.7:
                            plugin_data["master_bus_warning_type"] = "review_recommended"
                            # Don't set is_blacklisted_master = True for these
                    else:
                        # Traditional blacklist categories (limiters, clippers, mastering suites, spectral)
                        if rule_for_category is True and plugin_confidence >= 0.7:
                            plugin_data["is_blacklisted_master"] = True
                        elif rule_for_category in ["warn_settings", "warn_only"]:
                            plugin_data["master_bus_warning_type"] = rule_for_category

        # --- Handle Bypassed Plugins ---
        if plugin_data.get("is_bypassed", False):
            if plugin_data["is_blacklisted_general"]:
                plugin_data["would_be_blacklisted_general_if_active"] = True
                plugin_data["is_blacklisted_general"] = False  # Clear active flag
            if plugin_data.get("is_blacklisted_master", False):
                plugin_data["would_be_blacklisted_master_if_active"] = True
                plugin_data["is_blacklisted_master"] = False  # Clear active flag

    def analyze_master_bus_chain(
        self, plugins_on_master: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Analyzes the order and types of plugins on the master bus.
        Uses categories and confidence from plugin_data.
        """
        analysis_results = {
            "has_multiple_limiters": False,
            "limiter_not_last_excluding_metering": False,
            "mixing_plugin_before_final_limiter": False,
            "notes": [],
        }

        if not plugins_on_master:
            return analysis_results

        limiter_indices = []
        metering_indices = []

        for i, p_data in enumerate(plugins_on_master):
            if p_data.get("is_bypassed", False):
                continue

            category = p_data.get("category", "Unknown").lower()
            confidence = p_data.get("confidence", 0.0)

            # Only consider as a limiter if confidence is high enough
            if category == "limiter" and confidence >= 0.7:  # Added confidence check
                limiter_indices.append(i)

            is_metering = False
            metering_keywords = self.context.genre_rules.get(
                "master_bus_acceptable_metering_keywords",
                DEFAULT_MASTER_BUS_ACCEPTABLE_METERING_KEYWORDS,
            )
            for pattern in metering_keywords:
                if pattern.lower() in p_data.get("name", "").lower():
                    is_metering = True
                    break
            if is_metering or category == "metering":
                metering_indices.append(i)

        if len(limiter_indices) > 1:
            analysis_results["has_multiple_limiters"] = True

        if limiter_indices:
            last_limiter_idx = limiter_indices[-1]
            for i in range(last_limiter_idx + 1, len(plugins_on_master)):
                plugin_after_limiter_data = plugins_on_master[i]
                if plugin_after_limiter_data.get("is_bypassed", False):
                    continue

                is_metering_after = False
                for pattern in metering_keywords:
                    if (
                        pattern.lower()
                        in plugin_after_limiter_data.get("name", "").lower()
                    ):
                        is_metering_after = True
                        break
                if plugin_after_limiter_data.get("category", "").lower() == "metering":
                    is_metering_after = True

                if not is_metering_after:
                    analysis_results["limiter_not_last_excluding_metering"] = True
                    break

            first_limiter_idx = limiter_indices[0]
            for i in range(first_limiter_idx):
                plugin_before_limiter_data = plugins_on_master[i]
                if plugin_before_limiter_data.get("is_bypassed", False):
                    continue

                category_before = plugin_before_limiter_data.get(
                    "category", "Unknown"
                ).lower()
                confidence_before = plugin_before_limiter_data.get("confidence", 0.0)

                is_metering_before = False
                for pattern in metering_keywords:
                    if (
                        pattern.lower()
                        in plugin_before_limiter_data.get("name", "").lower()
                    ):
                        is_metering_before = True
                        break
                if category_before == "metering":
                    is_metering_before = True

                # Flag if a non-limiter/clipper/metering plugin with reasonable confidence is before the first limiter
                if (
                    category_before not in ["limiter", "clipper"]
                    and not is_metering_before
                    and confidence_before
                    >= 0.5  # Only flag if we're reasonably sure about its category
                ):
                    analysis_results["mixing_plugin_before_final_limiter"] = True
                    break

        return analysis_results
