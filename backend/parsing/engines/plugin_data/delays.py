# -*- coding: utf-8 -*-
"""
List of plugins in the delays category.
"""
from .base import PluginInfo

DELAYS = {
    "Delay Pro": PluginInfo(name="Delay Pro", category="Delay", vendor="AIR Music Technology"),
    "Blast Delay": PluginInfo(name="Blast Delay", category="Delay", vendor="Audio Blast"),
    "Dubstation 2": PluginInfo(name="Dubstation 2", category="Delay", vendor="Audio Damage"),
    "Space Station UM282": PluginInfo(name="Space Station UM282", category="Delay", vendor="Audiority"),
    "Mantis": PluginInfo(name="Man<PERSON>", category="Delay", vendor="AudioThing"),
    "Comeback Kid": PluginInfo(name="Comeback Kid", category="Delay", vendor="Baby Audio"),
    "BLEASS Tides": PluginInfo(name="BLEASS Tides", category="Delay", vendor="BLEASS"),
    "ADA STD-1 Stereo Tapped Delay": PluginInfo(
        name="ADA STD-1 Stereo Tapped Delay", category="Delay", vendor="Brainworx"
    ),
    "bx_delay 2500": PluginInfo(name="bx_delay 2500", category="Delay", vendor="Brainworx"),
    "NEOLD OLDTIMER": PluginInfo(name="NEOLD OLDTIMER", category="Delay", vendor="Brainworx"),
    "Unfiltered Audio Instant Delay": PluginInfo(
        name="Unfiltered Audio Instant Delay", category="Delay", vendor="Brainworx"
    ),
    "Unfiltered Audio Sandman": PluginInfo(name="Unfiltered Audio Sandman", category="Delay", vendor="Brainworx"),
    "Unfiltered Audio Sandman Pro": PluginInfo(
        name="Unfiltered Audio Sandman Pro", category="Delay", vendor="Brainworx"
    ),
    "Repeater": PluginInfo(name="Repeater", category="Delay", vendor="D16 Group"),
    "Sigmund 2": PluginInfo(name="Sigmund 2", category="Delay", vendor="D16 Group"),
    "H3000 Band Delays Mk II": PluginInfo(name="H3000 Band Delays Mk II", category="Delay", vendor="Eventide"),
    "H3000 Band Delays Mk II Crossgrade from Mk I": PluginInfo(
        name="H3000 Band Delays Mk II Crossgrade from Mk I",
        category="Delay",
        vendor="Eventide",
    ),
    "FabFilter Timeless 3": PluginInfo(name="FabFilter Timeless 3", category="Delay", vendor="FabFilter"),
    "FabFilter Timeless 3 & Upgrade": PluginInfo(
        name="FabFilter Timeless 3 & Upgrade", category="Delay", vendor="FabFilter"
    ),
    "BUCKET-500 Analog Delay": PluginInfo(
        name="BUCKET-500 Analog Delay", category="Delay", vendor="Fuse Audio Labs"
    ),
    "Space Delay": PluginInfo(name="Space Delay", category="Delay", vendor="IK Multimedia"),
    "DLYM - Delay Modulator": PluginInfo(name="DLYM - Delay Modulator", category="Delay", vendor="Imaginando"),
    "Dynamic Delay": PluginInfo(name="Dynamic Delay", category="Delay", vendor="Initial Audio"),
    "Cascadia": PluginInfo(name="Cascadia", category="Delay", vendor="iZotope"),
    "Matra Plus": PluginInfo(name="Matra Plus", category="Delay", vendor="Karanyi Sounds"),
    "Recurse": PluginInfo(name="Recurse", category="Delay", vendor="Lese"),
    "Strum": PluginInfo(name="Strum", category="Delay", vendor="Lese"),
    "EC-300 Echo Collection Native v7": PluginInfo(
        name="EC-300 Echo Collection Native v7", category="Delay", vendor="McDSP"
    ),
    "Cluster Delay": PluginInfo(name="Cluster Delay", category="Delay", vendor="Minimal Audio"),
    "Moogerfooger MF-104S Analog Delay": PluginInfo(
        name="Moogerfooger MF-104S Analog Delay", category="Delay", vendor="Moog"
    ),
    "Replika XT": PluginInfo(name="Replika XT", category="Delay", vendor="Native Instruments"),
    "Recirculate": PluginInfo(name="Recirculate", category="Delay", vendor="Newfangled Audio"),
    "Ripple": PluginInfo(name="Ripple", category="Delay", vendor="PROCESS.AUDIO"),
    "Lexicon PSP 42": PluginInfo(name="Lexicon PSP 42", category="Delay", vendor="PSP Audioware"),
    "PSP 285": PluginInfo(name="PSP 285", category="Delay", vendor="PSP Audioware"),
    "PSP stompDelay": PluginInfo(name="PSP stompDelay", category="Delay", vendor="PSP Audioware"),
    "Echorec": PluginInfo(name="Echorec", category="Delay", vendor="Pulsar Audio"),
    "DelSane": PluginInfo(name="DelSane", category="Delay", vendor="Rob Papen"),
    "Integer": PluginInfo(name="Integer", category="Delay", vendor="Sinevibes"),
    "Echoes": PluginInfo(name="Echoes", category="Delay", vendor="Softube"),
    "Tape Echoes": PluginInfo(name="Tape Echoes", category="Delay", vendor="Softube"),
    "Tube Delay": PluginInfo(name="Tube Delay", category="Delay", vendor="Softube"),
    "Echobode": PluginInfo(name="Echobode", category="Delay", vendor="Sonic Charge"),
    "Permut8": PluginInfo(name="Permut8", category="Delay", vendor="Sonic Charge"),
    "inDelay": PluginInfo(name="inDelay", category="Delay", vendor="Sound Particles"),
    "inDelay EDU Version": PluginInfo(name="inDelay EDU Version", category="Delay", vendor="Sound Particles"),
    "Diffuse": PluginInfo(name="Diffuse", category="Delay", vendor="Surreal Machines"),
    "TC 2290 - Legendary Dynamic Delay": PluginInfo(
        name="TC 2290 - Legendary Dynamic Delay",
        category="Delay",
        vendor="TC Electronic",
    ),
    "UFX DELAY": PluginInfo(name="UFX DELAY", category="Delay", vendor="UJAM"),
    "UFX DELAY Loyalty Upgrade from any paid UJAM product": PluginInfo(
        name="UFX DELAY Loyalty Upgrade from any paid UJAM product",
        category="Delay",
        vendor="UJAM",
    ),
    "Pluralis": PluginInfo(name="Pluralis", category="Delay", vendor="United Plugins"),
    "Galaxy Tape Echo": PluginInfo(name="Galaxy Tape Echo", category="Delay", vendor="Universal Audio"),
    "Dual Delay X": PluginInfo(name="Dual Delay X", category="Delay", vendor="UVI"),
    "Spectron by Curbi": PluginInfo(name="Spectron by Curbi", category="Delay", vendor="W. A. Production"),
    "SphereDelay 2": PluginInfo(name="SphereDelay 2", category="Delay", vendor="W. A. Production"),
    "SphereDelay 2 Upgrade from SphereDelay": PluginInfo(
        name="SphereDelay 2 Upgrade from SphereDelay",
        category="Delay",
        vendor="W. A. Production",
    ),
    "H-Delay Hybrid Delay": PluginInfo(name="H-Delay Hybrid Delay", category="Delay", vendor="Waves"),
    "Manny Marroquin Delay": PluginInfo(name="Manny Marroquin Delay", category="Delay", vendor="Waves"),
    "SuperTap": PluginInfo(name="SuperTap", category="Delay", vendor="Waves"),
    "Echo Cat": PluginInfo(name="Echo Cat", category="Delay", vendor="Wavesfactory"),
}
