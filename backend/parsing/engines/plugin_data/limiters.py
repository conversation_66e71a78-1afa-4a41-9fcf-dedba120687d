from .base import PluginInfo

LIMITERS = {
    # FabFilter
    "FabFilter Pro-L": PluginInfo(
        name="FabFilter Pro-L",
        category="Limiter",
        vendor="FabFilter",
        confidence=1.0,
        aliases=["FF Pro-L", "Pro-L"],
        decoder="ProL2Decoder",
    ),
    "FabFilter Pro-L 2": PluginInfo(
        name="FabFilter Pro-L 2",
        category="Limiter",
        vendor="FabFilter",
        confidence=1.0,
        aliases=["FF Pro-L2", "Pro-L2"],
        decoder="ProL2Decoder",
    ),
    # Waves
    "Waves L1": PluginInfo(
        name="Waves L1", category="Limiter", vendor="Waves", confidence=1.0
    ),
    "Waves L2": PluginInfo(
        name="Waves L2", category="Limiter", vendor="Waves", confidence=1.0
    ),
    "Waves L3": PluginInfo(
        name="Waves L3", category="Limiter", vendor="Waves", confidence=1.0
    ),
    "L1 Ultramaximizer": PluginInfo(
        name="L1 Ultramaximizer", category="Limiter", vendor="Waves", confidence=1.0
    ),
    "L2 Ultramaximizer": PluginInfo(
        name="L2 Ultramaximizer", category="Limiter", vendor="Waves", confidence=1.0
    ),
    "L3 Ultramaximizer": PluginInfo(
        name="L3 Ultramaximizer", category="Limiter", vendor="Waves", confidence=1.0
    ),
    "L3-16 Ultramaximizer": PluginInfo(
        name="L3-16 Ultramaximizer", category="Limiter", vendor="Waves", confidence=1.0
    ),
    "L3 Multimaximizer": PluginInfo(
        name="L3 Multimaximizer", category="Limiter", vendor="Waves", confidence=1.0
    ),
    "C1 Compressor": PluginInfo(
        name="C1 Compressor", category="Limiter", vendor="Waves", confidence=0.9
    ),
    "Renaissance Compressor": PluginInfo(
        name="Renaissance Compressor",
        category="Limiter",
        vendor="Waves",
        confidence=0.8,
    ),
    # iZotope
    "Ozone Maximizer": PluginInfo(
        name="Ozone Maximizer", category="Limiter", vendor="iZotope", confidence=1.0
    ),
    "Ozone 9 Maximizer": PluginInfo(
        name="Ozone 9 Maximizer", category="Limiter", vendor="iZotope", confidence=1.0
    ),
    "Ozone 10 Maximizer": PluginInfo(
        name="Ozone 10 Maximizer", category="Limiter", vendor="iZotope", confidence=1.0
    ),
    "Ozone 11 Maximizer": PluginInfo(
        name="Ozone 11 Maximizer", category="Limiter", vendor="iZotope", confidence=1.0
    ),
    # Eventide
    "Elevate": PluginInfo(
        name="Elevate", category="Limiter", vendor="Eventide", confidence=1.0
    ),
    # PSP
    "PSP Xenon": PluginInfo(
        name="PSP Xenon", category="Limiter", vendor="PSP", confidence=1.0
    ),
    "PSP Vintage Warmer": PluginInfo(
        name="PSP Vintage Warmer", category="Limiter", vendor="PSP", confidence=1.0
    ),
    "PSP Vintage Warmer 2": PluginInfo(
        name="PSP Vintage Warmer 2", category="Limiter", vendor="PSP", confidence=1.0
    ),
    "PSP InfiniStrip": PluginInfo(
        name="PSP InfiniStrip", category="Limiter", vendor="PSP", confidence=0.9
    ),
    # Slate Digital
    "FG-X": PluginInfo(
        name="FG-X", category="Limiter", vendor="Slate Digital", confidence=1.0
    ),
    "FG-Bomber": PluginInfo(
        name="FG-Bomber", category="Limiter", vendor="Slate Digital", confidence=1.0
    ),
    # Universal Audio
    "Precision Limiter": PluginInfo(
        name="Precision Limiter",
        category="Limiter",
        vendor="Universal Audio",
        confidence=1.0,
    ),
    "Fairchild 670": PluginInfo(
        name="Fairchild 670",
        category="Limiter",
        vendor="Universal Audio",
        confidence=0.9,
    ),
    # Sonnox
    "Oxford Limiter": PluginInfo(
        name="Oxford Limiter", category="Limiter", vendor="Sonnox", confidence=1.0
    ),
    "Oxford Inflator": PluginInfo(
        name="Oxford Inflator", category="Limiter", vendor="Sonnox", confidence=0.9
    ),
    # DMG Audio
    "Limitless": PluginInfo(
        name="Limitless", category="Limiter", vendor="DMG Audio", confidence=1.0
    ),
    # Softube
    "Weiss MM-1": PluginInfo(
        name="Weiss MM-1", category="Limiter", vendor="Softube", confidence=1.0
    ),
    "Weiss DS1-MK3": PluginInfo(
        name="Weiss DS1-MK3", category="Limiter", vendor="Softube", confidence=1.0
    ),
    # Plugin Alliance
    "Brainworx bx_limiter": PluginInfo(
        name="Brainworx bx_limiter",
        category="Limiter",
        vendor="Plugin Alliance",
        confidence=1.0,
    ),
    "Shadow Hills Mastering Compressor": PluginInfo(
        name="Shadow Hills Mastering Compressor",
        category="Limiter",
        vendor="Plugin Alliance",
        confidence=0.9,
    ),
    # Cockos/Reaper
    "ReaLimit": PluginInfo(
        name="ReaLimit", category="Limiter", vendor="Cockos", confidence=1.0
    ),
    # IK Multimedia
    "T-RackS Brickwall Limiter": PluginInfo(
        name="T-RackS Brickwall Limiter",
        category="Limiter",
        vendor="IK Multimedia",
        confidence=1.0,
    ),
    "T-RackS Master X": PluginInfo(
        name="T-RackS Master X",
        category="Limiter",
        vendor="IK Multimedia",
        confidence=1.0,
    ),
    "T-RackS Vintage Tube Compressor": PluginInfo(
        name="T-RackS Vintage Tube Compressor",
        category="Limiter",
        vendor="IK Multimedia",
        confidence=0.8,
    ),
    # Voxengo
    "Elephant": PluginInfo(
        name="Elephant", category="Limiter", vendor="Voxengo", confidence=1.0
    ),
    # Tokyo Dawn Labs
    "TDR Limiter 6 GE": PluginInfo(
        name="TDR Limiter 6 GE",
        category="Limiter",
        vendor="Tokyo Dawn Labs",
        confidence=1.0,
    ),
    "TDR Kotelnikov": PluginInfo(
        name="TDR Kotelnikov",
        category="Limiter",
        vendor="Tokyo Dawn Labs",
        confidence=0.9,
    ),
    # Klanghelm
    "MJUC": PluginInfo(
        name="MJUC", category="Limiter", vendor="Klanghelm", confidence=0.8
    ),
    # Cytomic
    "The Glue": PluginInfo(
        name="The Glue", category="Limiter", vendor="Cytomic", confidence=0.7
    ),
    # Native Instruments
    "Solid Bus Comp": PluginInfo(
        name="Solid Bus Comp",
        category="Limiter",
        vendor="Native Instruments",
        confidence=0.8,
    ),
    "VC 2A": PluginInfo(
        name="VC 2A", category="Limiter", vendor="Native Instruments", confidence=0.8
    ),
    "VC 76": PluginInfo(
        name="VC 76", category="Limiter", vendor="Native Instruments", confidence=0.8
    ),
    # Steinberg
    "Maximizer": PluginInfo(
        name="Maximizer", category="Limiter", vendor="Steinberg", confidence=1.0
    ),
    "Brickwall Limiter": PluginInfo(
        name="Brickwall Limiter", category="Limiter", vendor="Steinberg", confidence=1.0
    ),
    # Logic Pro
    "Adaptive Limiter": PluginInfo(
        name="Adaptive Limiter", category="Limiter", vendor="Apple", confidence=1.0
    ),
    "Multipressor": PluginInfo(
        name="Multipressor", category="Limiter", vendor="Apple", confidence=0.8
    ),
    # Ableton Live
    "Limiter": PluginInfo(
        name="Limiter", category="Limiter", vendor="Ableton", confidence=1.0
    ),
    "Glue Compressor": PluginInfo(
        name="Glue Compressor", category="Limiter", vendor="Ableton", confidence=0.7
    ),
    # Pro Tools
    "Maxim": PluginInfo(
        name="Maxim", category="Limiter", vendor="Avid", confidence=1.0
    ),
    "Dyn3 Compressor/Limiter": PluginInfo(
        name="Dyn3 Compressor/Limiter",
        category="Limiter",
        vendor="Avid",
        confidence=1.0,
    ),
}
