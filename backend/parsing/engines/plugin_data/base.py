from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from enum import Enum, auto


@dataclass
class PluginInfo:
    name: str
    category: str
    vendor: Optional[str] = None
    confidence: float = 0.0
    aliases: List[str] = field(default_factory=list)
    decoder: Optional[str] = None
    mastering_flags: Dict[str, Any] = field(default_factory=dict)
    subcategory: Optional[str] = (
        None  # For more granular categorization (e.g., "Bus Compressor")
    )


class PluginType(Enum):
    ANY = auto()
    VST = auto()
    AU = auto()
    JS = auto()
    # Add other types as needed
