# -*- coding: utf-8 -*-
"""
List of plugins in the referencing category.
"""
from .base import PluginInfo

REFERENCING = [
    PluginInfo(name="MixChecker Pro", category="Referencing", vendor="Audified"),
    PluginInfo(name="MixChecker Ultra", category="Referencing", vendor="Audified"),
    PluginInfo(
        name="MixChecker Ultra Upgrade from MixChecker Pro",
        category="Referencing",
        vendor="Audified",
    ),
    PluginInfo(name="REFERENCE 2", category="Referencing", vendor="Mastering The Mix"),
    PluginInfo(
        name="Apollo Monitor Correction Add-on for SoundID Reference",
        category="Referencing",
        vendor="Sonarworks",
    ),
    PluginInfo(
        name="Sound ID Reference for Speakers & Headphones Upgrade from SoundID Reference Headphones",
        category="Referencing",
        vendor="Sonarworks",
    ),
    PluginInfo(
        name="SoundID Reference for Headphones",
        category="Referencing",
        vendor="Sonarworks",
    ),
    PluginInfo(
        name="SoundID Reference for Headphones Upgrade from Sonarworks Reference 3 or 4 Headphone Edition",
        category="Referencing",
        vendor="Sonarworks",
    ),
    PluginInfo(
        name="SoundID Reference for Multichannel Upgrade from Reference 3 or 4 Studio Edition",
        category="Referencing",
        vendor="Sonarworks",
    ),
    PluginInfo(
        name="SoundID Reference for Speakers & Headphones",
        category="Referencing",
        vendor="Sonarworks",
    ),
    PluginInfo(
        name="SoundID Reference for Speakers & Headphones Upgrade from Sonarworks Reference 3 or 4 Studio Edition",
        category="Referencing",
        vendor="Sonarworks",
    ),
    PluginInfo(
        name="Virtual Monitoring Add-On for SoundID Reference",
        category="Referencing",
        vendor="Sonarworks",
    ),
]
