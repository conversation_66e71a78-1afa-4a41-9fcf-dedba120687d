# -*- coding: utf-8 -*-
"""
List of plugins in the stereo_imaging category.
"""
from .base import PluginInfo

STEREO_IMAGING = {
    "Ozone 9 Imager": PluginInfo(
        name="Ozone 9 Imager", 
        category="Stereo Imaging", 
        vendor="iZotope",
        confidence=0.95
    ),
    "Ozone 10 Imager": PluginInfo(
        name="Ozone 10 Imager", 
        category="Stereo Imaging", 
        vendor="iZotope",
        confidence=0.95
    ),
    "Ozone 11 Imager": PluginInfo(
        name="Ozone 11 Imager", 
        category="Stereo Imaging", 
        vendor="iZotope",
        confidence=0.95
    ),
    "Ozone Imager": PluginInfo(
        name="Ozone Imager", 
        category="Stereo Imaging", 
        vendor="iZotope",
        confidence=0.95
    ),
    "Panagement 2": PluginInfo(name="Panagement 2", category="Stereo Imaging", vendor="Auburn Sounds"),
    "Blue Cat's StereoScope Pro": PluginInfo(
        name="Blue Cat's StereoScope Pro",
        category="Stereo Imaging",
        vendor="Blue Cat Audio",
    ),
    "RECENTER": PluginInfo(name="RECENTER", category="Stereo Imaging", vendor="Boom"),
    "STEREOLAB": PluginInfo(name="STEREOLAB", category="Stereo Imaging", vendor="Boom"),
    "Mongoose": PluginInfo(name="Mongoose", category="Stereo Imaging", vendor="Boz Digital Labs"),
    "bx_stereomaker": PluginInfo(name="bx_stereomaker", category="Stereo Imaging", vendor="Brainworx"),
    "fiedler audio stage": PluginInfo(
        name="fiedler audio stage", category="Stereo Imaging", vendor="Brainworx"
    ),
    "SPL BiG": PluginInfo(name="SPL BiG", category="Stereo Imaging", vendor="Brainworx"),
    "PanShaper 4": PluginInfo(name="PanShaper 4", category="Stereo Imaging", vendor="Cableguys"),
    "WidthShaper 3": PluginInfo(name="WidthShaper 3", category="Stereo Imaging", vendor="Cableguys"),
    "Spread": PluginInfo(name="Spread", category="Stereo Imaging", vendor="DJ Swivel"),
    "MicroPitch Immersive": PluginInfo(
        name="MicroPitch Immersive", category="Stereo Imaging", vendor="Eventide"
    ),
    "Lifeline Width": PluginInfo(name="Lifeline Width", category="Stereo Imaging", vendor="Excite Audio"),
    "OCELOT Upmixer": PluginInfo(
        name="OCELOT Upmixer", category="Stereo Imaging", vendor="Fuse Audio Labs"
    ),
    "AVA Tremolo Panner": PluginInfo(
        name="AVA Tremolo Panner", category="Stereo Imaging", vendor="Harrison Consoles"
    ),
    "Quad Image": PluginInfo(name="Quad Image", category="Stereo Imaging", vendor="IK Multimedia"),
    "Haaze 2": PluginInfo(name="Haaze 2", category="Stereo Imaging", vendor="Klevgrand"),
    "Pana": PluginInfo(name="Pana", category="Stereo Imaging", vendor="Klevgrand"),
    "CenterOne": PluginInfo(name="CenterOne", category="Stereo Imaging", vendor="Leapwing Audio"),
    "StageOne 2": PluginInfo(name="StageOne 2", category="Stereo Imaging", vendor="Leapwing Audio"),
    "StageOne 2 Upgrade from StageOne": PluginInfo(
        name="StageOne 2 Upgrade from StageOne",
        category="Stereo Imaging",
        vendor="Leapwing Audio",
    ),
    "Frahm": PluginInfo(name="Frahm", category="Stereo Imaging", vendor="Lese"),
    "Transfer": PluginInfo(name="Transfer", category="Stereo Imaging", vendor="Lese"),
    "MAutopanMB": PluginInfo(name="MAutopanMB", category="Stereo Imaging", vendor="MeldaProduction"),
    "MCenter": PluginInfo(name="MCenter", category="Stereo Imaging", vendor="MeldaProduction"),
    "MStereoGenerator": PluginInfo(
        name="MStereoGenerator", category="Stereo Imaging", vendor="MeldaProduction"
    ),
    "MStereoProcessor": PluginInfo(
        name="MStereoProcessor", category="Stereo Imaging", vendor="MeldaProduction"
    ),
    "MStereoSpread": PluginInfo(
        name="MStereoSpread", category="Stereo Imaging", vendor="MeldaProduction"
    ),
    "Halo Downmix": PluginInfo(name="Halo Downmix", category="Stereo Imaging", vendor="NUGEN Audio"),
    "Halo Downmix with 3D Immersive Extension": PluginInfo(
        name="Halo Downmix with 3D Immersive Extension",
        category="Stereo Imaging",
        vendor="NUGEN Audio",
    ),
    "Halo Upmix": PluginInfo(name="Halo Upmix", category="Stereo Imaging", vendor="NUGEN Audio"),
    "Halo Upmix 3D Immersive Extension": PluginInfo(
        name="Halo Upmix 3D Immersive Extension",
        category="Stereo Imaging",
        vendor="NUGEN Audio",
    ),
    "Halo Upmix with 3D Immersive Extension": PluginInfo(
        name="Halo Upmix with 3D Immersive Extension",
        category="Stereo Imaging",
        vendor="NUGEN Audio",
    ),
    "Stereoizer": PluginInfo(name="Stereoizer", category="Stereo Imaging", vendor="NUGEN Audio"),
    "Stereoizer Elements": PluginInfo(
        name="Stereoizer Elements", category="Stereo Imaging", vendor="NUGEN Audio"
    ),
    "Stereoizer Upgrade from Stereoizer Elements": PluginInfo(
        name="Stereoizer Upgrade from Stereoizer Elements",
        category="Stereo Imaging",
        vendor="NUGEN Audio",
    ),
    "StereoSavage 2": PluginInfo(
        name="StereoSavage 2", category="Stereo Imaging", vendor="Plugin Boutique"
    ),
    "StereoSavage 2 Elements": PluginInfo(
        name="StereoSavage 2 Elements",
        category="Stereo Imaging",
        vendor="Plugin Boutique",
    ),
    "StereoSavage 2 Upgrade from StereoSavage 2 Elements": PluginInfo(
        name="StereoSavage 2 Upgrade from StereoSavage 2 Elements",
        category="Stereo Imaging",
        vendor="Plugin Boutique",
    ),
    "PSP StereoController2": PluginInfo(
        name="PSP StereoController2", category="Stereo Imaging", vendor="PSP Audioware"
    ),
    "Sonsig ACE": PluginInfo(
        name="Sonsig ACE", category="Stereo Imaging", vendor="Relab Development"
    ),
    "V-Pan": PluginInfo(name="V-Pan", category="Stereo Imaging", vendor="Rhodes"),
    "Magic Stereo": PluginInfo(name="Magic Stereo", category="Stereo Imaging", vendor="Singomakers"),
    "Layers": PluginInfo(name="Layers", category="Stereo Imaging", vendor="Softube"),
    "SSL Fusion Stereo Image": PluginInfo(
        name="SSL Fusion Stereo Image",
        category="Stereo Imaging",
        vendor="Solid State Logic",
    ),
    "Brightness Panner": PluginInfo(
        name="Brightness Panner", category="Stereo Imaging", vendor="Sound Particles"
    ),
    "Brightness Panner | Educational Version": PluginInfo(
        name="Brightness Panner | Educational Version",
        category="Stereo Imaging",
        vendor="Sound Particles",
    ),
    "Energy Panner": PluginInfo(
        name="Energy Panner", category="Stereo Imaging", vendor="Sound Particles"
    ),
    "Energy Panner | Educational Version": PluginInfo(
        name="Energy Panner | Educational Version",
        category="Stereo Imaging",
        vendor="Sound Particles",
    ),
    "Space Controller Standard": PluginInfo(
        name="Space Controller Standard",
        category="Stereo Imaging",
        vendor="Sound Particles",
    ),
    "Space Controller Standard | Educational Version": PluginInfo(
        name="Space Controller Standard | Educational Version",
        category="Stereo Imaging",
        vendor="Sound Particles",
    ),
    "Space Controller Studio": PluginInfo(
        name="Space Controller Studio",
        category="Stereo Imaging",
        vendor="Sound Particles",
    ),
    "Space Controller Studio | Educational Version": PluginInfo(
        name="Space Controller Studio | Educational Version",
        category="Stereo Imaging",
        vendor="Sound Particles",
    ),
    "MicroShift": PluginInfo(name="MicroShift", category="Stereo Imaging", vendor="Soundtoys"),
    "PanMan": PluginInfo(name="PanMan", category="Stereo Imaging", vendor="Soundtoys"),
    "TC 1210 - Unique Spatial Expander": PluginInfo(
        name="TC 1210 - Unique Spatial Expander",
        category="Stereo Imaging",
        vendor="TC Electronic",
    ),
    "dime[ms]": PluginInfo(name="dime[ms]", category="Stereo Imaging", vendor="time off audio"),
    "Expanse 3D": PluginInfo(name="Expanse 3D", category="Stereo Imaging", vendor="United Plugins"),
    "Dynawide": PluginInfo(name="Dynawide", category="Stereo Imaging", vendor="W. A. Production"),
    "Brauer Motion": PluginInfo(name="Brauer Motion", category="Stereo Imaging", vendor="Waves"),
    "Center": PluginInfo(name="Center", category="Stereo Imaging", vendor="Waves"),
    "MondoMod": PluginInfo(name="MondoMod", category="Stereo Imaging", vendor="Waves"),
    "PS22 Stereo Maker": PluginInfo(name="PS22 Stereo Maker", category="Stereo Imaging", vendor="Waves"),
    "S1 Stereo Imager": PluginInfo(name="S1 Stereo Imager", category="Stereo Imaging", vendor="Waves"),
}
