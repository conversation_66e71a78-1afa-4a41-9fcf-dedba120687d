# -*- coding: utf-8 -*-
"""
List of plugins in the metering category.
"""
from .base import PluginInfo

METERING = {
    "Blue Cat's StereoScope Multi": PluginInfo(
        name="Blue Cat's StereoScope Multi",
        category="Metering",
        vendor="Blue Cat Audio",
    ),
    "SPL HawkEye": PluginInfo(name="SPL HawkEye", category="Metering", vendor="Brainworx"),
    "Full Metering": PluginInfo(name="Full Metering", category="Metering", vendor="IK Multimedia"),
    "DRMeter": PluginInfo(name="DRMeter", category="Metering", vendor="MAAT"),
    "DRMeter MkII": PluginInfo(name="DRMeter MkII", category="Metering", vendor="MAAT"),
    "DRMeter MkII Upgrade from DRMeter": PluginInfo(
        name="DRMeter MkII Upgrade from DRMeter", category="Metering", vendor="MAAT"
    ),
    "DROffline": PluginInfo(name="DROffline", category="Metering", vendor="MAAT"),
    "DROffline MkII": PluginInfo(name="DROffline MkII", category="Metering", vendor="MAAT"),
    "MAAT DRMeter": PluginInfo(name="MAAT DRMeter", category="Metering", vendor="MAAT"),
    "MAAT DROffline": PluginInfo(name="MAAT DROffline", category="Metering", vendor="MAAT"),
    "LEVELS": PluginInfo(name="LEVELS", category="Metering", vendor="Mastering The Mix"),
    "Dynameter": PluginInfo(name="Dynameter", category="Metering", vendor="MeterPlugs"),
    "K-Meter": PluginInfo(name="K-Meter", category="Metering", vendor="MeterPlugs"),
    "LCAST Stereo": PluginInfo(name="LCAST Stereo", category="Metering", vendor="MeterPlugs"),
    "LCAST Surround": PluginInfo(name="LCAST Surround", category="Metering", vendor="MeterPlugs"),
    "Loudness Penalty": PluginInfo(name="Loudness Penalty", category="Metering", vendor="MeterPlugs"),
    "Perception AB": PluginInfo(name="Perception AB", category="Metering", vendor="MeterPlugs"),
    "Perception AB Upgrade from Perception": PluginInfo(
        name="Perception AB Upgrade from Perception",
        category="Metering",
        vendor="MeterPlugs",
    ),
    "Halo Vision": PluginInfo(name="Halo Vision", category="Metering", vendor="NUGEN Audio"),
    "VisLM": PluginInfo(name="VisLM", category="Metering", vendor="NUGEN Audio"),
    "Visualizer": PluginInfo(name="Visualizer", category="Metering", vendor="NUGEN Audio"),
    "Decibel": PluginInfo(name="Decibel", category="Metering", vendor="PROCESS.AUDIO"),
    "BUTE Loudness Analyser 2": PluginInfo(
        name="BUTE Loudness Analyser 2", category="Metering", vendor="Signum Audio"
    ),
    "BUTE Loudness Analyser 2 (Surround)": PluginInfo(
        name="BUTE Loudness Analyser 2 (Surround)",
        category="Metering",
        vendor="Signum Audio",
    ),
    "SSL Meter": PluginInfo(name="SSL Meter", category="Metering", vendor="Solid State Logic"),
    "SSL Meter Pro": PluginInfo(name="SSL Meter Pro", category="Metering", vendor="Solid State Logic"),
    "true:level": PluginInfo(name="true:level", category="Metering", vendor="sonible"),
    "FENNEK": PluginInfo(name="FENNEK", category="Metering", vendor="zplane"),
    "PPMulator XL": PluginInfo(name="PPMulator XL", category="Metering", vendor="zplane"),
}
