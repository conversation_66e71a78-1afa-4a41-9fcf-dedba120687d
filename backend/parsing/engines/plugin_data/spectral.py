# -*- coding: utf-8 -*-
"""
List of plugins in the spectral category.
"""
from .base import PluginInfo

SPECTRAL = {
    "Gullfoss": PluginInfo(name="Gullfoss", category="Spectral", vendor="Soundtheory"),
    "Smooth Operator Pro": PluginInfo(name="Smooth Operator Pro", category="Spectral", vendor="Baby Audio"),
    "TEOTE": PluginInfo(name="TEOTE", category="Spectral", vendor="Voxengo"),
    "SpectralBalance": PluginInfo(name="SpectralBalance", category="Spectral", vendor="Accentize"),
    "TDR Arbiter": PluginInfo(name="TDR Arbiter", category="Spectral", vendor="Tokyo Dawn Records"),
    "HoRNet BalancEQ": PluginInfo(name="HoRNet BalancEQ", category="Spectral", vendor="HoRNet Plugins"),
    "Soothe2": PluginInfo(name="Soothe2", category="Spectral", vendor="Oeksound"),
    "RESO": PluginInfo(name="RESO", category="Spectral", vendor="Mastering The Mix"),
    "M-Clarity 2": PluginInfo(name="M-Clarity 2", category="Spectral", vendor="Techivation"),
    "DSEQ3": PluginInfo(name="DSEQ3", category="Spectral", vendor="TBProAudio"),
    "TDR Nova": PluginInfo(name="TDR Nova", category="Spectral", vendor="Tokyo Dawn Records"),
    "F6 Floating-Band EQ": PluginInfo(name="F6 Floating-Band EQ", category="Spectral", vendor="Waves"),
    "Curves Equator": PluginInfo(name="Curves Equator", category="Spectral", vendor="Waves"),
    "Dove": PluginInfo(name="Dove", category="Spectral", vendor="Acustica Audio"),
}
