# -*- coding: utf-8 -*-
"""
List of plugins in the reverbs category.
"""
from .base import PluginInfo

REVERBS = {
    "Lair": PluginInfo(name="Lair", category="Reverb", vendor="Aberrant DSP"),
    "Acon Digital Verberate 2": PluginInfo(
        name="Acon Digital Verberate 2", category="Reverb", vendor="Acon Digital"
    ),
    "Verberate 2": PluginInfo(name="Verberate 2", category="Reverb", vendor="Acon Digital"),
    "Verberate 2 Upgrade from Verberate": PluginInfo(
        name="Verberate 2 Upgrade from Verberate",
        category="Reverb",
        vendor="Acon Digital",
    ),
    "Verberate Immersive 2": PluginInfo(name="Verberate Immersive 2", category="Reverb", vendor="Acon Digital"),
    "Verberate Immersive 2 Upgrade": PluginInfo(
        name="Verberate Immersive 2 Upgrade", category="Reverb", vendor="Acon Digital"
    ),
    "Ether": PluginInfo(name="Ether", category="Reverb", vendor="AIR Music Technology"),
    "MFX DigiVerbs": PluginInfo(name="MFX DigiVerbs", category="Reverb", vendor="ALM / Busy Circuits"),
    "MFX Physiverbs": PluginInfo(name="MFX Physiverbs", category="Reverb", vendor="ALM / Busy Circuits"),
    "Clearmountain's Spaces": PluginInfo(name="Clearmountain's Spaces", category="Reverb", vendor="Apogee"),
    "Rev INTENSITY": PluginInfo(name="Rev INTENSITY", category="Reverb", vendor="Arturia"),
    "Rev LX-24": PluginInfo(name="Rev LX-24", category="Reverb", vendor="Arturia"),
    "Rev PLATE-140": PluginInfo(name="Rev PLATE-140", category="Reverb", vendor="Arturia"),
    "Rev SPRING-636": PluginInfo(name="Rev SPRING-636", category="Reverb", vendor="Arturia"),
    "Linda IronVerb": PluginInfo(name="Linda IronVerb", category="Reverb", vendor="Audified"),
    "Toneknob Stargazer": PluginInfo(name="Toneknob Stargazer", category="Reverb", vendor="Audified"),
    "ADverb2": PluginInfo(name="ADverb2", category="Reverb", vendor="Audio Damage"),
    "Eos 2": PluginInfo(name="Eos 2", category="Reverb", vendor="Audio Damage"),
    "Ratshack Reverb 3": PluginInfo(name="Ratshack Reverb 3", category="Reverb", vendor="Audio Damage"),
    "GrainSpace": PluginInfo(name="GrainSpace", category="Reverb", vendor="Audiority"),
    "Polaris": PluginInfo(name="Polaris", category="Reverb", vendor="Audiority"),
    "Space Age 555": PluginInfo(name="Space Age 555", category="Reverb", vendor="Audiority"),
    "VertiVerb VRS23": PluginInfo(name="VertiVerb VRS23", category="Reverb", vendor="Audiority"),
    "XenoVerb": PluginInfo(name="XenoVerb", category="Reverb", vendor="Audiority"),
    "Fog Convolver 2": PluginInfo(name="Fog Convolver 2", category="Reverb", vendor="AudioThing"),
    "Fog Convolver 2 (Upgrade from Fog Convolver 1)": PluginInfo(
        name="Fog Convolver 2 (Upgrade from Fog Convolver 1)",
        category="Reverb",
        vendor="AudioThing",
    ),
    "miniVerb": PluginInfo(name="miniVerb", category="Reverb", vendor="AudioThing"),
    "Springs": PluginInfo(name="Springs", category="Reverb", vendor="AudioThing"),
    "Things – Texture": PluginInfo(name="Things – Texture", category="Reverb", vendor="AudioThing"),
    "Crystalline": PluginInfo(name="Crystalline", category="Reverb", vendor="Baby Audio"),
    "Spaced Out": PluginInfo(name="Spaced Out", category="Reverb", vendor="Baby Audio"),
    "RO-140": PluginInfo(name="RO-140", category="Reverb", vendor="Black Rooster Audio"),
    "RO-SPR": PluginInfo(name="RO-SPR", category="Reverb", vendor="Black Rooster Audio"),
    "BLEASS Reverb": PluginInfo(name="BLEASS Reverb", category="Reverb", vendor="BLEASS"),
    "BLEASS Shimmer": PluginInfo(name="BLEASS Shimmer", category="Reverb", vendor="BLEASS"),
    "Recoil": PluginInfo(name="Recoil", category="Reverb", vendor="Boz Digital Labs"),
    "Bettermaker BM60": PluginInfo(name="Bettermaker BM60", category="Reverb", vendor="Brainworx"),
    "bx_rooMS": PluginInfo(name="bx_rooMS", category="Reverb", vendor="Brainworx"),
    "Unfiltered Audio SILO": PluginInfo(name="Unfiltered Audio SILO", category="Reverb", vendor="Brainworx"),
    "Unfiltered Audio TAILS": PluginInfo(name="Unfiltered Audio TAILS", category="Reverb", vendor="Brainworx"),
    "ReverbShaper": PluginInfo(name="ReverbShaper", category="Reverb", vendor="Cableguys"),
    "Galactic Reverb": PluginInfo(name="Galactic Reverb", category="Reverb", vendor="Cherry Audio"),
    "Spacerek": PluginInfo(name="Spacerek", category="Reverb", vendor="D16 Group"),
    "Toraverb 2": PluginInfo(name="Toraverb 2", category="Reverb", vendor="D16 Group"),
    "Perfect Plate XL": PluginInfo(name="Perfect Plate XL", category="Reverb", vendor="Denise Audio"),
    "Perfect Room 2": PluginInfo(name="Perfect Room 2", category="Reverb", vendor="Denise Audio"),
    "Perfect Room 2 Upgrade from Perfect Room": PluginInfo(
        name="Perfect Room 2 Upgrade from Perfect Room",
        category="Reverb",
        vendor="Denise Audio",
    ),
    "EAReverb 2": PluginInfo(name="EAReverb 2", category="Reverb", vendor="eaReckon"),
    "EAReverb 2 Upgrade from EAReverb 1": PluginInfo(
        name="EAReverb 2 Upgrade from EAReverb 1", category="Reverb", vendor="eaReckon"
    ),
    "EAReverb 2 Upgrade from EAReverb SE": PluginInfo(
        name="EAReverb 2 Upgrade from EAReverb SE", category="Reverb", vendor="eaReckon"
    ),
    "EAReverb SE": PluginInfo(name="EAReverb SE", category="Reverb", vendor="eaReckon"),
    "Blackhole": PluginInfo(name="Blackhole", category="Reverb", vendor="Eventide"),
    "Blackhole Immersive": PluginInfo(name="Blackhole Immersive", category="Reverb", vendor="Eventide"),
    "MangledVerb": PluginInfo(name="MangledVerb", category="Reverb", vendor="Eventide"),
    "ShimmerVerb": PluginInfo(name="ShimmerVerb", category="Reverb", vendor="Eventide"),
    "SP2016 Reverb": PluginInfo(name="SP2016 Reverb", category="Reverb", vendor="Eventide"),
    "Spring": PluginInfo(name="Spring", category="Reverb", vendor="Eventide"),
    "Tverb": PluginInfo(name="Tverb", category="Reverb", vendor="Eventide"),
    "UltraReverb": PluginInfo(name="UltraReverb", category="Reverb", vendor="Eventide"),
    "Excite Audio Motion: Dimension": PluginInfo(
        name="Excite Audio Motion: Dimension", category="Reverb", vendor="Excite Audio"
    ),
    "Lifeline Space": PluginInfo(name="Lifeline Space", category="Reverb", vendor="Excite Audio"),
    "Motion: Dimension": PluginInfo(name="Motion: Dimension", category="Reverb", vendor="Excite Audio"),
    "Motion: Dimension (Crossgrade from any Motion Product)": PluginInfo(
        name="Motion: Dimension (Crossgrade from any Motion Product)",
        category="Reverb",
        vendor="Excite Audio",
    ),
    "Motion: Dimension (Upgrade from Motion: Dimension Lite)": PluginInfo(
        name="Motion: Dimension (Upgrade from Motion: Dimension Lite)",
        category="Reverb",
        vendor="Excite Audio",
    ),
    "Motion: Dimension Lite": PluginInfo(name="Motion: Dimension Lite", category="Reverb", vendor="Excite Audio"),
    "FabFilter Pro-R 2": PluginInfo(name="FabFilter Pro-R 2", category="Reverb", vendor="FabFilter"),
    "FabFilter Pro-R 2 & Upgrade": PluginInfo(
        name="FabFilter Pro-R 2 & Upgrade", category="Reverb", vendor="FabFilter"
    ),
    "FabFilter Pro-R 2 Upgrade from Pro-R": PluginInfo(
        name="FabFilter Pro-R 2 Upgrade from Pro-R",
        category="Reverb",
        vendor="FabFilter",
    ),
    "VREV-140 Vintage Plate Reverb": PluginInfo(
        name="VREV-140 Vintage Plate Reverb",
        category="Reverb",
        vendor="Fuse Audio Labs",
    ),
    "VREV-305 Vintage Spring Reverb": PluginInfo(
        name="VREV-305 Vintage Spring Reverb",
        category="Reverb",
        vendor="Fuse Audio Labs",
    ),
    "VREV-63 Surf Spring Reverb": PluginInfo(
        name="VREV-63 Surf Spring Reverb", category="Reverb", vendor="Fuse Audio Labs"
    ),
    "MicroFX Shimmer": PluginInfo(name="MicroFX Shimmer", category="Reverb", vendor="Heavyocity"),
    "HA 240 Gold Foil Verb": PluginInfo(
        name="HA 240 Gold Foil Verb", category="Reverb", vendor="Heritage Audio"
    ),
    "CSR Hall Reverb": PluginInfo(name="CSR Hall Reverb", category="Reverb", vendor="IK Multimedia"),
    "CSR Inverse Reverb": PluginInfo(name="CSR Inverse Reverb", category="Reverb", vendor="IK Multimedia"),
    "CSR Plate Reverb": PluginInfo(name="CSR Plate Reverb", category="Reverb", vendor="IK Multimedia"),
    "CSR Room Reverb": PluginInfo(name="CSR Room Reverb", category="Reverb", vendor="IK Multimedia"),
    "FAME Studio Reverb": PluginInfo(name="FAME Studio Reverb", category="Reverb", vendor="IK Multimedia"),
    "Sunset Sound Studio Reverb": PluginInfo(
        name="Sunset Sound Studio Reverb", category="Reverb", vendor="IK Multimedia"
    ),
    "T-RackS Leslie": PluginInfo(name="T-RackS Leslie", category="Reverb", vendor="IK Multimedia"),
    "The Farm Stone Room Studio Reverb": PluginInfo(
        name="The Farm Stone Room Studio Reverb",
        category="Reverb",
        vendor="IK Multimedia",
    ),
    "AR1 Reverb": PluginInfo(name="AR1 Reverb", category="Reverb", vendor="Initial Audio"),
    "Aurora": PluginInfo(name="Aurora", category="Reverb", vendor="iZotope"),
    "Equinox": PluginInfo(name="Equinox", category="Reverb", vendor="iZotope"),
    "Equinox Crossgrade from any paid iZotope or Exponential Audio product": PluginInfo(
        name="Equinox Crossgrade from any paid iZotope or Exponential Audio product",
        category="Reverb",
        vendor="iZotope",
    ),
    "Equinox Crossgrade from Stratus 3D and Symphony 3D, 3D Reverb Bundle, Music Production Suite 5-7, Post Production Suite 4-8.5, or Everything Bundle": PluginInfo(
        name="Equinox Crossgrade from Stratus 3D and Symphony 3D, 3D Reverb Bundle, Music Production Suite 5-7, Post Production Suite 4-8.5, or Everything Bundle",
        category="Reverb",
        vendor="iZotope",
    ),
    "Equinox Crossgrade from Stratus 3D or Symphony 3D": PluginInfo(
        name="Equinox Crossgrade from Stratus 3D or Symphony 3D",
        category="Reverb",
        vendor="iZotope",
    ),
    "iZotope Equinox": PluginInfo(name="iZotope Equinox", category="Reverb", vendor="iZotope"),
    "iZotope Exponential Audio Plugins": PluginInfo(
        name="iZotope Exponential Audio Plugins", category="Reverb", vendor="iZotope"
    ),
    "iZotope Neoverb": PluginInfo(name="iZotope Neoverb", category="Reverb", vendor="iZotope"),
    "Neoverb": PluginInfo(name="Neoverb", category="Reverb", vendor="iZotope"),
    "Neoverb Crossgrade from Any Paid iZotope or Exponential Audio Product": PluginInfo(
        name="Neoverb Crossgrade from Any Paid iZotope or Exponential Audio Product",
        category="Reverb",
        vendor="iZotope",
    ),
    "Neoverb Crossgrade from MPS 1-3 or MPB 1-2": PluginInfo(
        name="Neoverb Crossgrade from MPS 1-3 or MPB 1-2",
        category="Reverb",
        vendor="iZotope",
    ),
    "Neoverb Educational Version": PluginInfo(name="Neoverb Educational Version", category="Reverb", vendor="iZotope"),
    "Poly Space": PluginInfo(name="Poly Space", category="Reverb", vendor="Karanyi Sounds"),
    "Space": PluginInfo(name="Space", category="Reverb", vendor="Karanyi Sounds"),
    "XL-305R Dual Reverb": PluginInfo(name="XL-305R Dual Reverb", category="Reverb", vendor="Kiive Audio"),
    "Convolver": PluginInfo(name="Convolver", category="Reverb", vendor="Kilohearts"),
    "Kleverb": PluginInfo(name="Kleverb", category="Reverb", vendor="Klevgrand"),
    "Revolv": PluginInfo(name="Revolv", category="Reverb", vendor="Klevgrand"),
    "Rum": PluginInfo(name="Rum", category="Reverb", vendor="Klevgrand"),
    "Røverb": PluginInfo(name="Røverb", category="Reverb", vendor="Klevgrand"),
    "EFEKTOR RV3604 Reverb": PluginInfo(name="EFEKTOR RV3604 Reverb", category="Reverb", vendor="Kuassa"),
    "Eigen": PluginInfo(name="Eigen", category="Reverb", vendor="Lese"),
    "Space": PluginInfo(name="Space", category="Reverb", vendor="Lunacy"),
    "Revolver Convolution Reverb Native v7": PluginInfo(
        name="Revolver Convolution Reverb Native v7", category="Reverb", vendor="McDSP"
    ),
    "MConvolutionMB": PluginInfo(name="MConvolutionMB", category="Reverb", vendor="MeldaProduction"),
    "Melda Production MTurboReverb": PluginInfo(
        name="Melda Production MTurboReverb",
        category="Reverb",
        vendor="MeldaProduction",
    ),
    "MReverb": PluginInfo(name="MReverb", category="Reverb", vendor="MeldaProduction"),
    "MReverbMB": PluginInfo(name="MReverbMB", category="Reverb", vendor="MeldaProduction"),
    "MTurboReverb": PluginInfo(name="MTurboReverb", category="Reverb", vendor="MeldaProduction"),
    "MTurboReverbLE": PluginInfo(name="MTurboReverbLE", category="Reverb", vendor="MeldaProduction"),
    "IR1A Convolver": PluginInfo(name="IR1A Convolver", category="Reverb", vendor="Mellowmuse"),
    "Swarm Reverb": PluginInfo(name="Swarm Reverb", category="Reverb", vendor="Minimal Audio"),
    "Airwalker": PluginInfo(name="Airwalker", category="Reverb", vendor="Minimal System Group"),
    "Dreamscape": PluginInfo(name="Dreamscape", category="Reverb", vendor="Minimal System Group"),
    "ModVerb": PluginInfo(name="ModVerb", category="Reverb", vendor="Minimal System Group"),
    "Nebula Space Reverb": PluginInfo(
        name="Nebula Space Reverb", category="Reverb", vendor="Minimal System Group"
    ),
    "BOREALIS": PluginInfo(name="BOREALIS", category="Reverb", vendor="MNTRA Instruments"),
    "Airspace": PluginInfo(name="Airspace", category="Reverb", vendor="ModeAudio"),
    "Paragon": PluginInfo(name="Paragon", category="Reverb", vendor="NUGEN Audio"),
    "Paragon ST": PluginInfo(name="Paragon ST", category="Reverb", vendor="NUGEN Audio"),
    "Paragon Upgrade from NUGEN Post Bundle": PluginInfo(
        name="Paragon Upgrade from NUGEN Post Bundle",
        category="Reverb",
        vendor="NUGEN Audio",
    ),
    "Comet": PluginInfo(name="Comet", category="Reverb", vendor="Polyverse"),
    "PSP 2445 EMT": PluginInfo(name="PSP 2445 EMT", category="Reverb", vendor="PSP Audioware"),
    "PSP EasyVerb 2": PluginInfo(name="PSP EasyVerb 2", category="Reverb", vendor="PSP Audioware"),
    "PSP PianoVerb2": PluginInfo(name="PSP PianoVerb2", category="Reverb", vendor="PSP Audioware"),
    "PSP SpringBox": PluginInfo(name="PSP SpringBox", category="Reverb", vendor="PSP Audioware"),
    "Primavera": PluginInfo(name="Primavera", category="Reverb", vendor="Pulsar Audio"),
    "LX480 Dual-Engine Reverb V4": PluginInfo(
        name="LX480 Dual-Engine Reverb V4",
        category="Reverb",
        vendor="Relab Development",
    ),
    "LX480 Dual-Engine Reverb V4 Upgrade from ANY Relab Product": PluginInfo(
        name="LX480 Dual-Engine Reverb V4 Upgrade from ANY Relab Product",
        category="Reverb",
        vendor="Relab Development",
    ),
    "LX480 Essentials": PluginInfo(name="LX480 Essentials", category="Reverb", vendor="Relab Development"),
    "QuantX Essentials": PluginInfo(name="QuantX Essentials", category="Reverb", vendor="Relab Development"),
    "REV6000 Essentials": PluginInfo(
        name="REV6000 Essentials", category="Reverb", vendor="Relab Development"
    ),
    "Sonsig Rev-A": PluginInfo(name="Sonsig Rev-A", category="Reverb", vendor="Relab Development"),
    "RevSane": PluginInfo(name="RevSane", category="Reverb", vendor="Rob Papen"),
    "Rob Papen RP-VERB 2": PluginInfo(name="Rob Papen RP-VERB 2", category="Reverb", vendor="Rob Papen"),
    "RP-VERB 2": PluginInfo(name="RP-VERB 2", category="Reverb", vendor="Rob Papen"),
    "RP-VERB 2 (Upgrade from RP-VERB 1)": PluginInfo(
        name="RP-VERB 2 (Upgrade from RP-VERB 1)", category="Reverb", vendor="Rob Papen"
    ),
    "Hollow": PluginInfo(name="Hollow", category="Reverb", vendor="Sinevibes"),
    "Luminance v2": PluginInfo(name="Luminance v2", category="Reverb", vendor="Sinevibes"),
    "Atlantis Dual Chambers": PluginInfo(name="Atlantis Dual Chambers", category="Reverb", vendor="Softube"),
    "Dimensions": PluginInfo(name="Dimensions", category="Reverb", vendor="Softube"),
    "Spring Reverb": PluginInfo(name="Spring Reverb", category="Reverb", vendor="Softube"),
    "TSAR-1 Reverb": PluginInfo(name="TSAR-1 Reverb", category="Reverb", vendor="Softube"),
    "TSAR-1R Reverb": PluginInfo(name="TSAR-1R Reverb", category="Reverb", vendor="Softube"),
    "Wasted Space": PluginInfo(name="Wasted Space", category="Reverb", vendor="Softube"),
    "GateVerb": PluginInfo(name="GateVerb", category="Reverb", vendor="Solid State Logic"),
    "SSL Native FlexVerb": PluginInfo(
        name="SSL Native FlexVerb", category="Reverb", vendor="Solid State Logic"
    ),
    "SSL PlateVerb": PluginInfo(name="SSL PlateVerb", category="Reverb", vendor="Solid State Logic"),
    "pure:verb": PluginInfo(name="pure:verb", category="Reverb", vendor="sonible"),
    "smart:reverb": PluginInfo(name="smart:reverb", category="Reverb", vendor="sonible"),
    "Oxford Reverb": PluginInfo(name="Oxford Reverb", category="Reverb", vendor="Sonnox"),
    "Oxford Reverb - HDX": PluginInfo(name="Oxford Reverb - HDX", category="Reverb", vendor="Sonnox"),
    "Little Plate": PluginInfo(name="Little Plate", category="Reverb", vendor="Soundtoys"),
    "SpaceBlender": PluginInfo(name="SpaceBlender", category="Reverb", vendor="Soundtoys"),
    "SuperPlate": PluginInfo(name="SuperPlate", category="Reverb", vendor="Soundtoys"),
    "EchoThief": PluginInfo(name="EchoThief", category="Reverb", vendor="Stagecraft"),
    "DVR 250 - Digital Vintage Reverb": PluginInfo(
        name="DVR 250 - Digital Vintage Reverb",
        category="Reverb",
        vendor="TC Electronic",
    ),
    "NONLIN2 - Twisted Effects Reverb": PluginInfo(
        name="NONLIN2 - Twisted Effects Reverb",
        category="Reverb",
        vendor="TC Electronic",
    ),
    "TC 8210 - Classic Mixing Reverb": PluginInfo(
        name="TC 8210 - Classic Mixing Reverb",
        category="Reverb",
        vendor="TC Electronic",
    ),
    "VSS3 - Space Simulation Reverb": PluginInfo(
        name="VSS3 - Space Simulation Reverb", category="Reverb", vendor="TC Electronic"
    ),
    "VSS4 HD - Authentic Space Reverb": PluginInfo(
        name="VSS4 HD - Authentic Space Reverb",
        category="Reverb",
        vendor="TC Electronic",
    ),
    "Tuned Reverb": PluginInfo(name="Tuned Reverb", category="Reverb", vendor="Tuned Plugins"),
    "Twangström": PluginInfo(name="Twangström", category="Reverb", vendor="u-he"),
    "UFX REVERB Version 2": PluginInfo(name="UFX REVERB Version 2", category="Reverb", vendor="UJAM"),
    "UFX REVERB Version 2 Loyalty Upgrade from any paid UJAM product": PluginInfo(
        name="UFX REVERB Version 2 Loyalty Upgrade from any paid UJAM product",
        category="Reverb",
        vendor="UJAM",
    ),
    "Biverb": PluginInfo(name="Biverb", category="Reverb", vendor="United Plugins"),
    "Hyperspace": PluginInfo(name="Hyperspace", category="Reverb", vendor="United Plugins"),
    "MorphVerb": PluginInfo(name="MorphVerb", category="Reverb", vendor="United Plugins"),
    "Verbum Entropic Hall": PluginInfo(name="Verbum Entropic Hall", category="Reverb", vendor="United Plugins"),
    "Hitsville Reverb Chambers": PluginInfo(
        name="Hitsville Reverb Chambers", category="Reverb", vendor="Universal Audio"
    ),
    "Lexicon 224 Digital Reverb": PluginInfo(
        name="Lexicon 224 Digital Reverb", category="Reverb", vendor="Universal Audio"
    ),
    "Pure Plate Reverb": PluginInfo(name="Pure Plate Reverb", category="Reverb", vendor="Universal Audio"),
    "KSHMR Reverb 1.2": PluginInfo(name="KSHMR Reverb 1.2", category="Reverb", vendor="W. A. Production"),
    "Mutant Reverb": PluginInfo(name="Mutant Reverb", category="Reverb", vendor="W. A. Production"),
    "Dawn": PluginInfo(name="Dawn", category="Reverb", vendor="Wave Alchemy"),
    "Glow": PluginInfo(name="Glow", category="Reverb", vendor="Wave Alchemy"),
    "Pulse": PluginInfo(name="Pulse", category="Reverb", vendor="Wave Alchemy"),
    "Radiance": PluginInfo(name="Radiance", category="Reverb", vendor="Wave Alchemy"),
    "Abbey Road Chambers": PluginInfo(name="Abbey Road Chambers", category="Reverb", vendor="Waves"),
    "Abbey Road Reverb Plates": PluginInfo(name="Abbey Road Reverb Plates", category="Reverb", vendor="Waves"),
    "CLA Epic": PluginInfo(name="CLA Epic", category="Reverb", vendor="Waves"),
    "H-Reverb Hybrid Reverb": PluginInfo(name="H-Reverb Hybrid Reverb", category="Reverb", vendor="Waves"),
    "IR-L Convolution Reverb": PluginInfo(name="IR-L Convolution Reverb", category="Reverb", vendor="Waves"),
    "IR-Live Convolution Reverb": PluginInfo(name="IR-Live Convolution Reverb", category="Reverb", vendor="Waves"),
    "IR1 Convolution Reverb": PluginInfo(name="IR1 Convolution Reverb", category="Reverb", vendor="Waves"),
    "IR360 Convolution Reverb": PluginInfo(name="IR360 Convolution Reverb", category="Reverb", vendor="Waves"),
    "Lofi Space": PluginInfo(name="Lofi Space", category="Reverb", vendor="Waves"),
    "Magma Springs": PluginInfo(name="Magma Springs", category="Reverb", vendor="Waves"),
    "Manny Marroquin Reverb": PluginInfo(name="Manny Marroquin Reverb", category="Reverb", vendor="Waves"),
    "OneKnob Wetter": PluginInfo(name="OneKnob Wetter", category="Reverb", vendor="Waves"),
    "Renaissance Reverb": PluginInfo(name="Renaissance Reverb", category="Reverb", vendor="Waves"),
    "TrueVerb": PluginInfo(name="TrueVerb", category="Reverb", vendor="Waves"),
    "Deep Waters": PluginInfo(name="Deep Waters", category="Reverb", vendor="ZAK Sound"),
    "ADAPTIVERB": PluginInfo(name="ADAPTIVERB", category="Reverb", vendor="Zynaptiq"),
    "ADAPTIVERB Educational Version": PluginInfo(
        name="ADAPTIVERB Educational Version", category="Reverb", vendor="Zynaptiq"
    ),
}
