from .limiters import <PERSON>IM<PERSON>ER<PERSON>
from .compressors import COMPRESSOR<PERSON>
from .eqs import EQS
from .saturation import SATURATION
from .mastering import MASTERING
from .clippers import C<PERSON>IPPERS
from .metering import METERING
from .referencing import REFERENCING
from .reverbs import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .delays import DELAY<PERSON>
from .stereo_imaging import <PERSON>ER<PERSON>O_IMAGING
from .spectral import SPECTR<PERSON>
from .patterns import VENDOR_MODEL_PATTERNS, KEYWORD_PATTERNS, KNOWN_METERING_PLUGINS
from .base import PluginInfo  # Export PluginInfo for external use

# Unified database combining all categories  
# All categories now in dictionary format for consistent lookup
PLUGIN_DATABASE = {}

# Add all converted categories (all now in dict format)
PLUGIN_DATABASE.update(LIMITERS)      # Already in dict format
PLUGIN_DATABASE.update(COMPRESSORS)   # Now in dict format
PLUGIN_DATABASE.update(EQS)           # Now in dict format  
PLUGIN_DATABASE.update(SATURATION)    # Now in dict format
PLUGIN_DATABASE.update(MASTERING)     # Now in dict format
PLUGIN_DATABASE.update(CLIPPERS)      # Now in dict format
PLUGIN_DATABASE.update(REFERENCING)   # Now in dict format
PLUGIN_DATABASE.update(DELAYS)        # Now in dict format
PLUGIN_DATABASE.update(SPECTRAL)      # Now in dict format

# Add newly converted categories
PLUGIN_DATABASE.update(METERING)      # Now in dict format
PLUGIN_DATABASE.update(REVERBS)       # Now in dict format
PLUGIN_DATABASE.update(STEREO_IMAGING) # Now in dict format

# All categories now converted to dict format! ✅

# Export everything the registry needs
__all__ = [
    "PLUGIN_DATABASE",
    "VENDOR_MODEL_PATTERNS",
    "KEYWORD_PATTERNS",
    "KNOWN_METERING_PLUGINS",
    "PluginInfo",  # Export PluginInfo for type hinting and external use
    "LIMITERS",  # Can be useful for category-specific access if needed
    "COMPRESSORS",
    "EQS",
    "SATURATION",
    "MASTERING",
    "CLIPPERS",
    "METERING",
    "REFERENCING",
    "REVERBS",
    "DELAYS",
    "STEREO_IMAGING",
    "SPECTRAL",
]
