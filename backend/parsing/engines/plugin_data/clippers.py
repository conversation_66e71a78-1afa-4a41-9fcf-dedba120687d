# -*- coding: utf-8 -*-
"""
List of plugins in the clippers category.
"""
from .base import PluginInfo

CLIPPERS = {
    "Soft Clipper": PluginInfo(name="Soft Clipper", category="Clipper", vendor="AIR Music Technology"),
    "BSAClipper": PluginInfo(name="BSAClipper", category="Clipper", vendor="Black Salt Audio"),
    "Big Clipper 2": PluginInfo(name="Big Clipper 2", category="Clipper", vendor="Boz Digital Labs"),
    "Boz Digital Big & Little Clipper 2": PluginInfo(
        name="Boz Digital Big & Little Clipper 2",
        category="Clipper",
        vendor="Boz Digital Labs",
    ),
    "Little Clipper 2": PluginInfo(name="Little Clipper 2", category="Clipper", vendor="Boz Digital Labs"),
    "OCELOT Clipper": PluginInfo(name="OCELOT Clipper", category="Clipper", vendor="Fuse Audio Labs"),
    "Classic T-RackS Clipper": PluginInfo(
        name="Classic T-RackS Clipper", category="Clipper", vendor="IK Multimedia"
    ),
    "DualClip": PluginInfo(name="DualClip", category="Clipper", vendor="Plugin Boutique"),
    "KNOCK Clipper": PluginInfo(name="KNOCK Clipper", category="Clipper", vendor="PLUGINS THAT KNOCK"),
    "SKYE Clipper": PluginInfo(name="SKYE Clipper", category="Clipper", vendor="Signum Audio"),
    "SKYE Clipper (Surround)": PluginInfo(
        name="SKYE Clipper (Surround)", category="Clipper", vendor="Signum Audio"
    ),
    "Clipper": PluginInfo(name="Clipper", category="Clipper", vendor="Softube"),
}
