from typing import Dict, <PERSON>, <PERSON><PERSON>, List

# Vendor + Model pattern combinations for flexible matching
VENDOR_MODEL_PATTERNS: Dict[<PERSON>ple[str, str], Dict[str, Any]] = {
    # FabFilter patterns
    ("fabfilter", "pro-l"): {
        "category": "Limiter",
        "vendor": "FabFilter",
        "confidence": 0.95,
    },
    ("ff", "pro-l"): {"category": "Limiter", "vendor": "FabFilter", "confidence": 0.95},
    ("fabfilter", "pro-l2"): {
        "category": "Limiter",
        "vendor": "FabFilter",
        "confidence": 0.95,
    },
    # Waves patterns
    ("waves", "l1"): {"category": "Limiter", "vendor": "Waves", "confidence": 0.95},
    ("waves", "l2"): {"category": "Limiter", "vendor": "Waves", "confidence": 0.95},
    ("waves", "l3"): {"category": "Limiter", "vendor": "Waves", "confidence": 0.95},
    ("waves", "ultramaximizer"): {
        "category": "Limiter",
        "vendor": "Waves",
        "confidence": 0.95,
    },
    ("waves", "c1"): {"category": "Limiter", "vendor": "Waves", "confidence": 0.90},
    ("waves", "renaissance"): {
        "category": "Limiter",
        "vendor": "Waves",
        "confidence": 0.85,
    },
    # iZotope patterns
    ("izotope", "maximizer"): {
        "category": "Limiter",
        "vendor": "iZotope",
        "confidence": 0.95,
    },
    ("ozone", "maximizer"): {
        "category": "Limiter",
        "vendor": "iZotope",
        "confidence": 0.95,
    },
    # PSP patterns
    ("psp", "xenon"): {"category": "Limiter", "vendor": "PSP", "confidence": 0.95},
    ("psp", "vintage"): {"category": "Limiter", "vendor": "PSP", "confidence": 0.90},
    ("psp", "warmer"): {"category": "Limiter", "vendor": "PSP", "confidence": 0.90},
    # Eventide patterns
    ("eventide", "elevate"): {
        "category": "Limiter",
        "vendor": "Eventide",
        "confidence": 0.95,
    },
    # Slate Digital patterns
    ("slate", "fg-x"): {
        "category": "Limiter",
        "vendor": "Slate Digital",
        "confidence": 0.95,
    },
    ("slate", "bomber"): {
        "category": "Limiter",
        "vendor": "Slate Digital",
        "confidence": 0.95,
    },
    # Universal Audio patterns
    ("ua", "precision"): {
        "category": "Limiter",
        "vendor": "Universal Audio",
        "confidence": 0.90,
    },
    ("uad", "precision"): {
        "category": "Limiter",
        "vendor": "Universal Audio",
        "confidence": 0.90,
    },
    ("ua", "1176"): {
        "category": "Limiter",
        "vendor": "Universal Audio",
        "confidence": 0.85,
    },
    ("uad", "1176"): {
        "category": "Limiter",
        "vendor": "Universal Audio",
        "confidence": 0.85,
    },
    # Sonnox patterns
    ("sonnox", "limiter"): {
        "category": "Limiter",
        "vendor": "Sonnox",
        "confidence": 0.95,
    },
    ("oxford", "limiter"): {
        "category": "Limiter",
        "vendor": "Sonnox",
        "confidence": 0.95,
    },
    ("sonnox", "inflator"): {
        "category": "Limiter",
        "vendor": "Sonnox",
        "confidence": 0.90,
    },
    ("oxford", "inflator"): {
        "category": "Limiter",
        "vendor": "Sonnox",
        "confidence": 0.90,
    },
    # DMG Audio patterns
    ("dmg", "limitless"): {
        "category": "Limiter",
        "vendor": "DMG Audio",
        "confidence": 0.95,
    },
    # Voxengo patterns
    ("voxengo", "elephant"): {
        "category": "Limiter",
        "vendor": "Voxengo",
        "confidence": 0.95,
    },
    # Tokyo Dawn Labs patterns
    ("tdr", "limiter"): {
        "category": "Limiter",
        "vendor": "Tokyo Dawn Labs",
        "confidence": 0.95,
    },
    ("tdr", "kotelnikov"): {
        "category": "Limiter",
        "vendor": "Tokyo Dawn Labs",
        "confidence": 0.90,
    },
}

# Enhanced keyword patterns with confidence scoring
KEYWORD_PATTERNS: Dict[str, Dict[str, Any]] = {
    # Strong limiter indicators
    "limiter": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "maximizer": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "brick wall": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "brickwall": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "peak eater": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "ultramaximizer": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "multimaximizer": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "elephant": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "elevate": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "limitless": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "fg-x": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    "xenon": {"confidence": 0.85, "type": "strong", "category": "Limiter"},
    # Medium limiter indicators
    "clipper": {"confidence": 0.70, "type": "medium", "category": "Limiter"},
    "vintage warmer": {"confidence": 0.75, "type": "medium", "category": "Limiter"},
    "warmer": {"confidence": 0.65, "type": "medium", "category": "Limiter"},
    "finalizer": {"confidence": 0.80, "type": "medium", "category": "Limiter"},
    "inflator": {"confidence": 0.70, "type": "medium", "category": "Limiter"},
    "precision": {"confidence": 0.70, "type": "medium", "category": "Limiter"},
    "adaptive": {"confidence": 0.60, "type": "medium", "category": "Limiter"},
    # Model number patterns (Waves L-series, etc.)
    "l1": {"confidence": 0.80, "type": "model", "category": "Limiter"},
    "l2": {"confidence": 0.80, "type": "model", "category": "Limiter"},
    "l3": {"confidence": 0.80, "type": "model", "category": "Limiter"},
    "l4": {"confidence": 0.80, "type": "model", "category": "Limiter"},
    "c1": {"confidence": 0.70, "type": "model", "category": "Limiter"},
    # Weaker indicators (need context)
    "ceiling": {"confidence": 0.50, "type": "weak", "category": "Limiter"},
    "peak": {"confidence": 0.40, "type": "weak", "category": "Limiter"},
    "master": {"confidence": 0.30, "type": "weak", "category": "Limiter"},
    "compressor": {
        "confidence": 0.25,
        "type": "weak",
        "category": "Compressor",
    },  # Can be limiter in some contexts
    # Demo/Trial indicators
    "demo": {"confidence": 0.95, "type": "blacklist", "category": "Demo"},
    "trial": {"confidence": 0.95, "type": "blacklist", "category": "Trial"},
    # General categories (non-limiter specific)
    "pro-q": {"category": "EQ", "vendor": "FabFilter", "confidence": 0.95},
    "pro-c": {"category": "Compressor", "vendor": "FabFilter", "confidence": 0.95},
    "pro-mb": {
        "category": "Multiband Compressor",
        "vendor": "FabFilter",
        "confidence": 0.95,
    },
    "pro-ds": {"category": "De-esser", "vendor": "FabFilter", "confidence": 0.95},
    "pro-g": {"category": "Gate", "vendor": "FabFilter", "confidence": 0.95},
    "pro-r": {"category": "Reverb", "vendor": "FabFilter", "confidence": 0.95},
    "ozone": {"category": "Mastering Suite", "vendor": "iZotope", "confidence": 0.90},
    "neutron": {"category": "Mixing Suite", "vendor": "iZotope", "confidence": 0.90},
    "insight": {"category": "Metering", "vendor": "iZotope", "confidence": 0.90},
    "gullfoss": {"category": "EQ", "vendor": "Soundtheory", "confidence": 0.90},
    "soothe": {"category": "Dynamic EQ", "vendor": "oeksound", "confidence": 0.90},
}

# Could also have lists for specific types, e.g., known metering plugins
KNOWN_METERING_PLUGINS = [
    "SPAN (Voxengo)",
    "Youlean Loudness Meter 2 (Youlean)",
    "Insight 2 (iZotope)",
]
