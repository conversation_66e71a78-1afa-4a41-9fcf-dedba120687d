# -*- coding: utf-8 -*-
"""
List of plugins in the clippers category.
"""
from .base import PluginInfo

CLIPPERS = [
    PluginInfo(name="Soft Clipper", category="Clipper", vendor="AIR Music Technology"),
    PluginInfo(name="BSAClipper", category="Clipper", vendor="Black Salt Audio"),
    PluginInfo(name="Big Clipper 2", category="Clipper", vendor="Boz Digital Labs"),
    PluginInfo(
        name="Boz Digital Big & Little Clipper 2",
        category="Clipper",
        vendor="Boz Digital Labs",
    ),
    PluginInfo(name="Little Clipper 2", category="Clipper", vendor="Boz Digital Labs"),
    PluginInfo(name="OCELOT Clipper", category="Clipper", vendor="Fuse Audio Labs"),
    PluginInfo(
        name="Classic T-RackS Clipper", category="Clipper", vendor="IK Multimedia"
    ),
    PluginInfo(name="DualClip", category="Clipper", vendor="Plugin Boutique"),
    PluginInfo(name="KNOCK Clipper", category="Clipper", vendor="PLUGINS THAT KNOCK"),
    PluginInfo(name="SKYE Clipper", category="Clipper", vendor="Signum Audio"),
    PluginInfo(
        name="SKYE Clipper (Surround)", category="Clipper", vendor="Signum Audio"
    ),
    PluginInfo(name="Clipper", category="Clipper", vendor="Softube"),
]
