# -*- coding: utf-8 -*-
"""
List of plugins in the mastering category.
"""
from .base import PluginInfo

MASTERING = {
    "Mix Monolith": PluginInfo(name="Mix Monolith", category="Mastering", vendor="AYAIC"),
    "bx_masterdesk": PluginInfo(name="bx_masterdesk", category="Mastering", vendor="Brainworx"),
    "bx_masterdesk True Peak": PluginInfo(
        name="bx_masterdesk True Peak", category="Mastering", vendor="Brainworx"
    ),
    "bx_XL V2": PluginInfo(name="bx_XL V2", category="Mastering", vendor="Brainworx"),
    "Lindell Audio 80 Series": PluginInfo(
        name="Lindell Audio 80 Series", category="Mastering", vendor="Brainworx"
    ),
    "Dolby Atmos Composer": PluginInfo(
        name="Dolby Atmos Composer", category="Mastering", vendor="Fiedler Audio"
    ),
    "Mastering Console": PluginInfo(name="Mastering Console", category="Mastering", vendor="Fiedler Audio"),
    "Spacelab Ignition": PluginInfo(name="Spacelab Ignition", category="Mastering", vendor="Fiedler Audio"),
    "Lurssen Mastering Console": PluginInfo(
        name="Lurssen Mastering Console", category="Mastering", vendor="IK Multimedia"
    ),
    "Master Match": PluginInfo(name="Master Match", category="Mastering", vendor="IK Multimedia"),
    "One": PluginInfo(name="One", category="Mastering", vendor="IK Multimedia"),
    "Master Suite": PluginInfo(name="Master Suite", category="Mastering", vendor="Initial Audio"),
    "iZotope Ozone 11": PluginInfo(name="iZotope Ozone 11", category="Mastering", vendor="iZotope"),
    "Ozone 11 Advanced": PluginInfo(name="Ozone 11 Advanced", category="Mastering", vendor="iZotope"),
    "Ozone 11 Advanced Crossgrade from any iZotope product (including Elements & Exponential Audio)": PluginInfo(
        name="Ozone 11 Advanced Crossgrade from any iZotope product (including Elements & Exponential Audio)",
        category="Mastering",
        vendor="iZotope",
    ),
    "Ozone 11 Advanced Educational Version": PluginInfo(
        name="Ozone 11 Advanced Educational Version",
        category="Mastering",
        vendor="iZotope",
    ),
    "Ozone 11 Advanced Upgrade from Music Production Suite 4 or 5 or Ozone 9 or 10 Advanced": PluginInfo(
        name="Ozone 11 Advanced Upgrade from Music Production Suite 4 or 5 or Ozone 9 or 10 Advanced",
        category="Mastering",
        vendor="iZotope",
    ),
    "Ozone 11 Advanced Upgrade from Ozone 11 Standard": PluginInfo(
        name="Ozone 11 Advanced Upgrade from Ozone 11 Standard",
        category="Mastering",
        vendor="iZotope",
    ),
    "Ozone 11 Advanced Upgrade from Ozone 9 or 10 Standard": PluginInfo(
        name="Ozone 11 Advanced Upgrade from Ozone 9 or 10 Standard",
        category="Mastering",
        vendor="iZotope",
    ),
    "Ozone 11 Elements": PluginInfo(name="Ozone 11 Elements", category="Mastering", vendor="iZotope"),
    "Ozone 11 Elements Educational Version": PluginInfo(
        name="Ozone 11 Elements Educational Version",
        category="Mastering",
        vendor="iZotope",
    ),
    "Ozone 11 Standard": PluginInfo(name="Ozone 11 Standard", category="Mastering", vendor="iZotope"),
    "Ozone 11 Standard Crossgrade from any iZotope product, including Elements, and Exponential Audio": PluginInfo(
        name="Ozone 11 Standard Crossgrade from any iZotope product, including Elements, and Exponential Audio",
        category="Mastering",
        vendor="iZotope",
    ),
    "Ozone 11 Standard Educational Version": PluginInfo(
        name="Ozone 11 Standard Educational Version",
        category="Mastering",
        vendor="iZotope",
    ),
    "Ozone 11 Standard Upgrade from Ozone 9 or 10 Standard": PluginInfo(
        name="Ozone 11 Standard Upgrade from Ozone 9 or 10 Standard",
        category="Mastering",
        vendor="iZotope",
    ),
    "Tonal Balance Control 2": PluginInfo(name="Tonal Balance Control 2", category="Mastering", vendor="iZotope"),
    "Tonal Balance Control 2 Educational Version": PluginInfo(
        name="Tonal Balance Control 2 Educational Version",
        category="Mastering",
        vendor="iZotope",
    ),
    "Grand Finale": PluginInfo(name="Grand Finale", category="Mastering", vendor="Klevgrand"),
    "LANDR Mastering Plugin PRO": PluginInfo(name="LANDR Mastering Plugin PRO", category="Mastering", vendor="LANDR"),
    "LANDR Mastering Plugin SE": PluginInfo(name="LANDR Mastering Plugin SE", category="Mastering", vendor="LANDR"),
    "2BC multiCORR": PluginInfo(name="2BC multiCORR", category="Mastering", vendor="MAAT"),
    "Plugin Name": PluginInfo(name="Plugin Name", category="Mastering", vendor="Manufacturer"),
    "BASSROOM": PluginInfo(name="BASSROOM", category="Mastering", vendor="Mastering The Mix"),
    "Mini Mastering Bundle": PluginInfo(
        name="Mini Mastering Bundle",
        category="Mastering",
        vendor="Minimal System Group",
    ),
    "Elevate Mastering Bundle": PluginInfo(
        name="Elevate Mastering Bundle", category="Mastering", vendor="Newfangled Audio"
    ),
    "Newfangled Audio Elevate": PluginInfo(
        name="Newfangled Audio Elevate", category="Mastering", vendor="Newfangled Audio"
    ),
    "LM-Correct 2": PluginInfo(name="LM-Correct 2", category="Mastering", vendor="NUGEN Audio"),
    "MasterCheck": PluginInfo(name="MasterCheck", category="Mastering", vendor="NUGEN Audio"),
    "Monofilter": PluginInfo(name="Monofilter", category="Mastering", vendor="NUGEN Audio"),
    "Monofilter Upgrade from Monofilter Elements": PluginInfo(
        name="Monofilter Upgrade from Monofilter Elements",
        category="Mastering",
        vendor="NUGEN Audio",
    ),
    "PSP auralControl": PluginInfo(name="PSP auralControl", category="Mastering", vendor="PSP Audioware"),
    "PSP MasterQ2": PluginInfo(name="PSP MasterQ2", category="Mastering", vendor="PSP Audioware"),
    "MasterMagic": PluginInfo(name="MasterMagic", category="Mastering", vendor="Rob Papen"),
    "BUTE Loudness Normaliser": PluginInfo(
        name="BUTE Loudness Normaliser", category="Mastering", vendor="Signum Audio"
    ),
    "BUTE Loudness Normaliser (Surround)": PluginInfo(
        name="BUTE Loudness Normaliser (Surround)",
        category="Mastering",
        vendor="Signum Audio",
    ),
    "BUTE Loudness Suite 2": PluginInfo(
        name="BUTE Loudness Suite 2", category="Mastering", vendor="Signum Audio"
    ),
    "BUTE Loudness Suite 2 (Surround)": PluginInfo(
        name="BUTE Loudness Suite 2 (Surround)",
        category="Mastering",
        vendor="Signum Audio",
    ),
    "Softube Weiss Series": PluginInfo(name="Softube Weiss Series", category="Mastering", vendor="Softube"),
    "Weiss DS1-MK3": PluginInfo(name="Weiss DS1-MK3", category="Mastering", vendor="Softube"),
    "Weiss DS1-MK3 (Upgrade from Weiss Compressor/Limiter and Weiss MM-1)": PluginInfo(
        name="Weiss DS1-MK3 (Upgrade from Weiss Compressor/Limiter and Weiss MM-1)",
        category="Mastering",
        vendor="Softube",
    ),
    "Weiss DS1-MK3 (Upgrade from Weiss Compressor/Limiter)": PluginInfo(
        name="Weiss DS1-MK3 (Upgrade from Weiss Compressor/Limiter)",
        category="Mastering",
        vendor="Softube",
    ),
    "Weiss DS1-MK3 (Upgrade from Weiss Deess and Weiss MM-1)": PluginInfo(
        name="Weiss DS1-MK3 (Upgrade from Weiss Deess and Weiss MM-1)",
        category="Mastering",
        vendor="Softube",
    ),
    "Weiss DS1-MK3 (Upgrade from Weiss Deess)": PluginInfo(
        name="Weiss DS1-MK3 (Upgrade from Weiss Deess)",
        category="Mastering",
        vendor="Softube",
    ),
    "Weiss DS1-MK3 (Upgrade from Weiss MM-1)": PluginInfo(
        name="Weiss DS1-MK3 (Upgrade from Weiss MM-1)",
        category="Mastering",
        vendor="Softube",
    ),
    "Weiss MM-1": PluginInfo(name="Weiss MM-1", category="Mastering", vendor="Softube"),
    "Oxford Dynamics": PluginInfo(name="Oxford Dynamics", category="Mastering", vendor="Sonnox"),
    "Oxford Dynamics - HDX": PluginInfo(name="Oxford Dynamics - HDX", category="Mastering", vendor="Sonnox"),
    "WaveLab": PluginInfo(name="WaveLab", category="Mastering", vendor="Steinberg"),
    "WaveLab Elements 12": PluginInfo(name="WaveLab Elements 12", category="Mastering", vendor="Steinberg"),
    "WaveLab Elements 12 Educational Edition": PluginInfo(
        name="WaveLab Elements 12 Educational Edition",
        category="Mastering",
        vendor="Steinberg",
    ),
    "WaveLab Pro 12": PluginInfo(name="WaveLab Pro 12", category="Mastering", vendor="Steinberg"),
    "WaveLab Pro 12 Educational Edition": PluginInfo(
        name="WaveLab Pro 12 Educational Edition",
        category="Mastering",
        vendor="Steinberg",
    ),
    "TDR SimuLathe CUT": PluginInfo(
        name="TDR SimuLathe CUT", category="Mastering", vendor="Tokyo Dawn Labs"
    ),
    "TDR SimuLathe REF": PluginInfo(
        name="TDR SimuLathe REF", category="Mastering", vendor="Tokyo Dawn Labs"
    ),
    "FireMaximizer": PluginInfo(name="FireMaximizer", category="Mastering", vendor="United Plugins"),
    "MasterMind": PluginInfo(name="MasterMind", category="Mastering", vendor="United Plugins"),
    "Abbey Road TG Mastering Chain": PluginInfo(
        name="Abbey Road TG Mastering Chain", category="Mastering", vendor="Waves"
    ),
    "MASTER Bundle": PluginInfo(name="MASTER Bundle", category="Mastering", vendor="Zynaptiq"),
    "MASTER Bundle Educational Version": PluginInfo(
        name="MASTER Bundle Educational Version",
        category="Mastering",
        vendor="Zynaptiq",
    ),
}
