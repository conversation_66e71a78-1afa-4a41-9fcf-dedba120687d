from .base import PluginInfo

COMPRESSORS = {
    "SSL Native X-ValveComp": PluginInfo(
        name="SSL Native X-ValveComp",
        category="Compressor",
        vendor="Solid State Logic",
    ),
    "SSL 4K B": PluginInfo(name="SSL 4K B", category="Compressor", vendor="Solid State Logic"),
    "Empirical Labs EL8 Distressor Compressor": PluginInfo(
        name="Empirical Labs EL8 Distressor Compressor",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "LA-2A Tube Compressor": PluginInfo(
        name="LA-2A Tube Compressor",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "FabFilter Pro-C 2": PluginInfo(name="FabFilter Pro-C 2", category="Compressor", vendor="FabFilter"),
    "Shadow Hills Mastering Compressor Class A": PluginInfo(
        name="Shadow Hills Mastering Compressor Class A",
        category="Compressor",
        vendor="Brainworx",
    ),
    "Unfiltered Audio Zip": PluginInfo(name="Unfiltered Audio Zip", category="Compressor", vendor="Brainworx"),
    "PSP VintageWarmer2": PluginInfo(
        name="PSP VintageWarmer2", category="Compressor", vendor="PSP Audioware"
    ),
    "dbx 160 Compressor/Limiter": PluginInfo(
        name="dbx 160 Compressor/Limiter",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "Parallel Aggressor": PluginInfo(name="Parallel Aggressor", category="Compressor", vendor="Baby Audio"),
    "bx_townhouse Buss Compressor": PluginInfo(
        name="bx_townhouse Buss Compressor",
        category="Compressor",
        vendor="Brainworx",
    ),
    "UAD 1176 Classic FET Compressor": PluginInfo(
        name="UAD 1176 Classic FET Compressor",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "API 2500 Bus Compressor": PluginInfo(
        name="API 2500 Bus Compressor",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "Fuse Compressor": PluginInfo(name="Fuse Compressor", category="Compressor", vendor="Minimal Audio"),
    "Teletronix LA-3A Audio Leveler": PluginInfo(
        name="Teletronix LA-3A Audio Leveler",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "Shadow Hills Mastering Compressor": PluginInfo(
        name="Shadow Hills Mastering Compressor",
        category="Compressor",
        vendor="Brainworx",
    ),
    "Black 76 Limiting Amplifier": PluginInfo(
        name="Black 76 Limiting Amplifier",
        category="Compressor",
        vendor="IK Multimedia",
    ),
    "SPL IRON": PluginInfo(name="SPL IRON", category="Compressor", vendor="Brainworx"),
    "Pulsar 1178": PluginInfo(name="Pulsar 1178", category="Compressor", vendor="Pulsar Audio"),
    "Capitol Mastering Compressor": PluginInfo(
        name="Capitol Mastering Compressor",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "PSP DRC": PluginInfo(name="PSP DRC", category="Compressor", vendor="PSP Audioware"),
    "Lindell Audio 254E": PluginInfo(name="Lindell Audio 254E", category="Compressor", vendor="Brainworx"),
    "NEOLD U2A": PluginInfo(name="NEOLD U2A", category="Compressor", vendor="Brainworx"),
    "White 2A Leveling Amplifier": PluginInfo(
        name="White 2A Leveling Amplifier",
        category="Compressor",
        vendor="IK Multimedia",
    ),
    "Vertigo VSC-2": PluginInfo(name="Vertigo VSC-2", category="Compressor", vendor="Brainworx"),
    "Smasher": PluginInfo(name="Smasher", category="Compressor", vendor="Pulsar Audio"),
    "Bettermaker Bus Compressor": PluginInfo(
        name="Bettermaker Bus Compressor",
        category="Compressor",
        vendor="Brainworx",
    ),
    "IDX Intelligent Dynamics": PluginInfo(name="IDX Intelligent Dynamics", category="Compressor", vendor="Waves"),
    "Drawmer S73 Intelligent Master Processor": PluginInfo(
        name="Drawmer S73 Intelligent Master Processor",
        category="Compressor",
        vendor="Softube",
    ),
    "bx_opto": PluginInfo(name="bx_opto", category="Compressor", vendor="Brainworx"),
    "Weiss Compressor/Limiter": PluginInfo(
        name="Weiss Compressor/Limiter", category="Compressor", vendor="Softube"
    ),
    "Three-Body Technology Cenozoix Compressor": PluginInfo(
        name="Three-Body Technology Cenozoix Compressor",
        category="Compressor",
        vendor="Brainworx",
    ),
    "VPRE-2C Vintage Tube Booster": PluginInfo(
        name="VPRE-2C Vintage Tube Booster",
        category="Compressor",
        vendor="Fuse Audio Labs",
    ),
    "VCL-4 Vintage Opto Leveller": PluginInfo(
        name="VCL-4 Vintage Opto Leveller",
        category="Compressor",
        vendor="Fuse Audio Labs",
    ),
    "VCL-373 Vintage Compressor/Limiter": PluginInfo(
        name="VCL-373 Vintage Compressor/Limiter",
        category="Compressor",
        vendor="Fuse Audio Labs",
    ),
    "CLA-2A Compressor / Limiter": PluginInfo(
        name="CLA-2A Compressor / Limiter", category="Compressor", vendor="Waves"
    ),
    "smart:comp 2": PluginInfo(name="smart:comp 2", category="Compressor", vendor="sonible"),
    "ShapeShifter": PluginInfo(name="ShapeShifter", category="Compressor", vendor="Aberrant DSP"),
    "Comp FET-76": PluginInfo(name="Comp FET-76", category="Compressor", vendor="Arturia"),
    "FabFilter Pro-MB": PluginInfo(name="FabFilter Pro-MB", category="Compressor", vendor="FabFilter"),
    "Lindell Audio 354E": PluginInfo(name="Lindell Audio 354E", category="Compressor", vendor="Brainworx"),
    "Lindell Audio 7X-500": PluginInfo(name="Lindell Audio 7X-500", category="Compressor", vendor="Brainworx"),
    "Lindell Audio SBC": PluginInfo(name="Lindell Audio SBC", category="Compressor", vendor="Brainworx"),
    "VCL-25A Vintage Vari-Mu Leveller": PluginInfo(
        name="VCL-25A Vintage Vari-Mu Leveller",
        category="Compressor",
        vendor="Fuse Audio Labs",
    ),
    "Renaissance Compressor": PluginInfo(name="Renaissance Compressor", category="Compressor", vendor="Waves"),
    "C4 Multiband Compressor": PluginInfo(name="C4 Multiband Compressor", category="Compressor", vendor="Waves"),
    "CLA-76 Compressor / Limiter": PluginInfo(
        name="CLA-76 Compressor / Limiter", category="Compressor", vendor="Waves"
    ),
    "SSL G-Master Buss Compressor": PluginInfo(
        name="SSL G-Master Buss Compressor", category="Compressor", vendor="Waves"
    ),
    "Dirty Dyna": PluginInfo(name="Dirty Dyna", category="Compressor", vendor="BeatSkillz"),
    "bx_clipper": PluginInfo(name="bx_clipper", category="Compressor", vendor="Brainworx"),
    "Tube-Tech SMC 2B": PluginInfo(name="Tube-Tech SMC 2B", category="Compressor", vendor="Softube"),
    "ACME Audio Opticom XLA-3": PluginInfo(
        name="ACME Audio Opticom XLA-3", category="Compressor", vendor="Brainworx"
    ),
    "Millennia TCL-2": PluginInfo(name="Millennia TCL-2", category="Compressor", vendor="Brainworx"),
    "AMEK Mastering Compressor": PluginInfo(
        name="AMEK Mastering Compressor", category="Compressor", vendor="Brainworx"
    ),
    "Elysia Mpressor": PluginInfo(name="Elysia Mpressor", category="Compressor", vendor="Brainworx"),
    "Elysia Alpha Compressor": PluginInfo(
        name="Elysia Alpha Compressor", category="Compressor", vendor="Brainworx"
    ),
    "Drawmer 1973 Multi-Band Compressor": PluginInfo(
        name="Drawmer 1973 Multi-Band Compressor",
        category="Compressor",
        vendor="Softube",
    ),
    "AutoTune Vocal Compressor": PluginInfo(
        name="AutoTune Vocal Compressor", category="Compressor", vendor="Antares"
    ),
    "Teletronix LA-2A Leveler Collection": PluginInfo(
        name="Teletronix LA-2A Leveler Collection",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "IHNY-2": PluginInfo(name="IHNY-2", category="Compressor", vendor="Baby Audio"),
    "6030 Ultimate Compressor Native v7": PluginInfo(
        name="6030 Ultimate Compressor Native v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "MC2000 Multi-Band Compressor Native v7": PluginInfo(
        name="MC2000 Multi-Band Compressor Native v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "Korvpressor": PluginInfo(name="Korvpressor", category="Compressor", vendor="Klevgrand"),
    "XTT by VINAI": PluginInfo(name="XTT by VINAI", category="Compressor", vendor="W. A. Production"),
    "PolyComp": PluginInfo(name="PolyComp", category="Compressor", vendor="Audiority"),
    "The King 2": PluginInfo(name="The King 2", category="Compressor", vendor="W. A. Production"),
    "Multiband Compressor": PluginInfo(
        name="Multiband Compressor",
        category="Compressor",
        vendor="Vengeance Sound",
    ),
    "MD3 - Legendary Multiband Dynamics": PluginInfo(
        name="MD3 - Legendary Multiband Dynamics",
        category="Compressor",
        vendor="TC Electronic",
    ),
    "MD4 HD - Hi-Res Multiband Dynamics": PluginInfo(
        name="MD4 HD - Hi-Res Multiband Dynamics",
        category="Compressor",
        vendor="TC Electronic",
    ),
    "DYN 3000 - Midas Channel Dynamics": PluginInfo(
        name="DYN 3000 - Midas Channel Dynamics",
        category="Compressor",
        vendor="TC Electronic",
    ),
    "MASTER X HD - Easy Multiband Dynamics": PluginInfo(
        name="MASTER X HD - Easy Multiband Dynamics",
        category="Compressor",
        vendor="TC Electronic",
    ),
    "E-LUX": PluginInfo(name="E-LUX", category="Compressor", vendor="Tone Empire"),
    "NEOLD U17": PluginInfo(name="NEOLD U17", category="Compressor", vendor="Brainworx"),
    "SUCCESSOR": PluginInfo(name="SUCCESSOR", category="Compressor", vendor="Heritage Audio"),
    "MK-609": PluginInfo(name="MK-609", category="Compressor", vendor="Kiive Audio"),
    "Lindell Audio MBC": PluginInfo(name="Lindell Audio MBC", category="Compressor", vendor="Brainworx"),
    "Lindell Audio MU-66": PluginInfo(name="Lindell Audio MU-66", category="Compressor", vendor="Brainworx"),
    "Bettermaker C502V": PluginInfo(name="Bettermaker C502V", category="Compressor", vendor="Brainworx"),
    "ToneKnob Compressor": PluginInfo(name="ToneKnob Compressor", category="Compressor", vendor="Audified"),
    "C1 Compressor": PluginInfo(name="C1 Compressor", category="Compressor", vendor="Waves"),
    "Manny Marroquin Tone Shaper": PluginInfo(
        name="Manny Marroquin Tone Shaper", category="Compressor", vendor="Waves"
    ),
    "CLA MixDown": PluginInfo(name="CLA MixDown", category="Compressor", vendor="Waves"),
    "Renaissance Axx": PluginInfo(name="Renaissance Axx", category="Compressor", vendor="Waves"),
    "MV2": PluginInfo(name="MV2", category="Compressor", vendor="Waves"),
    "Bass Rider": PluginInfo(name="Bass Rider", category="Compressor", vendor="Waves"),
    "Renaissance Vox": PluginInfo(name="Renaissance Vox", category="Compressor", vendor="Waves"),
    "Scheps 73": PluginInfo(name="Scheps 73", category="Compressor", vendor="Waves"),
    "PuigChild Compressor": PluginInfo(name="PuigChild Compressor", category="Compressor", vendor="Waves"),
    "dbx® 160 Compressor / Limiter": PluginInfo(
        name="dbx® 160 Compressor / Limiter", category="Compressor", vendor="Waves"
    ),
    "Abbey Road RS124 Compressor": PluginInfo(
        name="Abbey Road RS124 Compressor", category="Compressor", vendor="Waves"
    ),
    "V-Comp": PluginInfo(name="V-Comp", category="Compressor", vendor="Waves"),
    "CLA-3A Compressor / Limiter": PluginInfo(
        name="CLA-3A Compressor / Limiter", category="Compressor", vendor="Waves"
    ),
    "API 2500": PluginInfo(name="API 2500", category="Compressor", vendor="Waves"),
    "Linear Phase Multiband Compressor": PluginInfo(
        name="Linear Phase Multiband Compressor",
        category="Compressor",
        vendor="Waves",
    ),
    "H-Comp Hybrid Compressor": PluginInfo(name="H-Comp Hybrid Compressor", category="Compressor", vendor="Waves"),
    "C6 Multiband Compressor": PluginInfo(name="C6 Multiband Compressor", category="Compressor", vendor="Waves"),
    "OneKnob Louder": PluginInfo(name="OneKnob Louder", category="Compressor", vendor="Waves"),
    "OneKnob Pressure": PluginInfo(name="OneKnob Pressure", category="Compressor", vendor="Waves"),
    "OneKnob Pumper": PluginInfo(name="OneKnob Pumper", category="Compressor", vendor="Waves"),
    "Kramer PIE Compressor": PluginInfo(name="Kramer PIE Compressor", category="Compressor", vendor="Waves"),
    "BSS DPR-402": PluginInfo(name="BSS DPR-402", category="Compressor", vendor="Waves"),
    "MaxxVolume": PluginInfo(name="MaxxVolume", category="Compressor", vendor="Waves"),
    "Spherix Immersive Compressor & Limiter": PluginInfo(
        name="Spherix Immersive Compressor & Limiter",
        category="Compressor",
        vendor="Waves",
    ),
    "eMo D5 Dynamics": PluginInfo(name="eMo D5 Dynamics", category="Compressor", vendor="Waves"),
    "bx_glue": PluginInfo(name="bx_glue", category="Compressor", vendor="Brainworx"),
    "Precision Comp/Limiter": PluginInfo(
        name="Precision Comp/Limiter",
        category="Compressor",
        vendor="IK Multimedia",
    ),
    "Comprexxor": PluginInfo(name="Comprexxor", category="Compressor", vendor="IK Multimedia"),
    "Dyna-Mu": PluginInfo(name="Dyna-Mu", category="Compressor", vendor="IK Multimedia"),
    "Vintage Tube Compressor/Limiter Model 670": PluginInfo(
        name="Vintage Tube Compressor/Limiter Model 670",
        category="Compressor",
        vendor="IK Multimedia",
    ),
    "Quad Comp": PluginInfo(name="Quad Comp", category="Compressor", vendor="IK Multimedia"),
    "Opto Compressor": PluginInfo(name="Opto Compressor", category="Compressor", vendor="IK Multimedia"),
    "Pythor": PluginInfo(name="Pythor", category="Compressor", vendor="Tone Empire"),
    "LT2 Comp": PluginInfo(name="LT2 Comp", category="Compressor", vendor="Tone Empire"),
    "Bus Compressor": PluginInfo(name="Bus Compressor", category="Compressor", vendor="IK Multimedia"),
    "Lifeline Comp": PluginInfo(name="Lifeline Comp", category="Compressor", vendor="Excite Audio"),
    "KC-1 Tube Compressor": PluginInfo(
        name="KC-1 Tube Compressor", category="Compressor", vendor="Kiive Audio"
    ),
    "V-Comp": PluginInfo(name="V-Comp", category="Compressor", vendor="Kiive Audio"),
    "LA Crème": PluginInfo(name="LA Crème", category="Compressor", vendor="Tone Empire"),
    "gravitas MDS": PluginInfo(name="gravitas MDS", category="Compressor", vendor="Fiedler Audio"),
    "StaGate": PluginInfo(name="StaGate", category="Compressor", vendor="Tone Empire"),
    "PSP Flare": PluginInfo(name="PSP Flare", category="Compressor", vendor="PSP Audioware"),
    "BLEASS Multiband Compressor": PluginInfo(
        name="BLEASS Multiband Compressor", category="Compressor", vendor="BLEASS"
    ),
    "NFuse": PluginInfo(name="NFuse", category="Compressor", vendor="Kiive Audio"),
    "MPC Compressor": PluginInfo(
        name="MPC Compressor", category="Compressor", vendor="Harrison Consoles"
    ),
    "Lens": PluginInfo(name="Lens", category="Compressor", vendor="Auburn Sounds"),
    "MPC Spectral Compressor": PluginInfo(
        name="MPC Spectral Compressor",
        category="Compressor",
        vendor="Harrison Consoles",
    ),
    "Things – Voice": PluginInfo(name="Things – Voice", category="Compressor", vendor="AudioThing"),
    "Softube Tube-Tech Series": PluginInfo(
        name="Softube Tube-Tech Series", category="Compressor", vendor="Softube"
    ),
    "Tube-Tech CL 1B Mk II": PluginInfo(name="Tube-Tech CL 1B Mk II", category="Compressor", vendor="Softube"),
    "OmniTec-436C": PluginInfo(
        name="OmniTec-436C", category="Compressor", vendor="Black Rooster Audio"
    ),
    "VLA-3A": PluginInfo(name="VLA-3A", category="Compressor", vendor="Black Rooster Audio"),
    "BlueAsh SC-5": PluginInfo(
        name="BlueAsh SC-5", category="Compressor", vendor="Black Rooster Audio"
    ),
    "BlackAsh SC-5": PluginInfo(
        name="BlackAsh SC-5", category="Compressor", vendor="Black Rooster Audio"
    ),
    "VLA-FET": PluginInfo(name="VLA-FET", category="Compressor", vendor="Black Rooster Audio"),
    "KH-COMP1": PluginInfo(name="KH-COMP1", category="Compressor", vendor="Black Rooster Audio"),
    "VLA-2A Mark II": PluginInfo(
        name="VLA-2A Mark II", category="Compressor", vendor="Black Rooster Audio"
    ),
    "VTC-2": PluginInfo(name="VTC-2", category="Compressor", vendor="Black Rooster Audio"),
    "Soul Squash": PluginInfo(name="Soul Squash", category="Compressor", vendor="Tone Empire"),
    "Supercharger GT": PluginInfo(
        name="Supercharger GT", category="Compressor", vendor="Native Instruments"
    ),
    "SSL G3 MultiBusComp": PluginInfo(
        name="SSL G3 MultiBusComp",
        category="Compressor",
        vendor="Solid State Logic",
    ),
    "175B & 176 Tube Compressor Collection": PluginInfo(
        name="175B & 176 Tube Compressor Collection",
        category="Compressor",
        vendor="Universal Audio",
    ),
    "LVL-01B": PluginInfo(name="LVL-01B", category="Compressor", vendor="Tone Empire"),
    "COMP.TWO": PluginInfo(name="COMP.TWO", category="Compressor", vendor="PROCESS.AUDIO"),
    "PSP Impressor": PluginInfo(name="PSP Impressor", category="Compressor", vendor="PSP Audioware"),
    "PSP auralComp": PluginInfo(name="PSP auralComp", category="Compressor", vendor="PSP Audioware"),
    "Multiband X6": PluginInfo(name="Multiband X6", category="Compressor", vendor="Devious Machines"),
    "Richter": PluginInfo(name="Richter", category="Compressor", vendor="Klevgrand"),
    "Bus Processor": PluginInfo(name="Bus Processor", category="Compressor", vendor="Softube"),
    "SSL Blitzer": PluginInfo(name="SSL Blitzer", category="Compressor", vendor="Solid State Logic"),
    "pure:comp": PluginInfo(name="pure:comp", category="Compressor", vendor="sonible"),
    "Empirical Labs Mike-E Comp": PluginInfo(
        name="Empirical Labs Mike-E Comp", category="Compressor", vendor="Softube"
    ),
    "Chandler Limited® Germanium Compressor": PluginInfo(
        name="Chandler Limited® Germanium Compressor",
        category="Compressor",
        vendor="Softube",
    ),
    "FET Compressor MK II": PluginInfo(name="FET Compressor MK II", category="Compressor", vendor="Softube"),
    "VCA Compressor": PluginInfo(name="VCA Compressor", category="Compressor", vendor="Softube"),
    "OPTO Compressor": PluginInfo(name="OPTO Compressor", category="Compressor", vendor="Softube"),
    "LVL-01": PluginInfo(name="LVL-01", category="Compressor", vendor="Tone Empire"),
    "Presser by Aiden Kenway": PluginInfo(
        name="Presser by Aiden Kenway",
        category="Compressor",
        vendor="W. A. Production",
    ),
    "+10db Compressor V2": PluginInfo(
        name="+10db Compressor V2",
        category="Compressor",
        vendor="Boz Digital Labs",
    ),
    "+10db Compressor V2 Upgrade from V1": PluginInfo(
        name="+10db Compressor V2 Upgrade from V1",
        category="Compressor",
        vendor="Boz Digital Labs",
    ),
    "Invigorate": PluginInfo(name="Invigorate", category="Compressor", vendor="Newfangled Audio"),
    "ADC1 Compressor": PluginInfo(name="ADC1 Compressor", category="Compressor", vendor="Kiive Audio"),
    "SKYE Clipper": PluginInfo(name="SKYE Clipper", category="Compressor", vendor="Signum Audio"),
    "SKYE Clipper (Surround)": PluginInfo(
        name="SKYE Clipper (Surround)",
        category="Compressor",
        vendor="Signum Audio",
    ),
    "IHNY-2 Upgrade from IHNY-1": PluginInfo(
        name="IHNY-2 Upgrade from IHNY-1",
        category="Compressor",
        vendor="Baby Audio",
    ),
    "sonible smart:comp 2": PluginInfo(name="sonible smart:comp 2", category="Compressor", vendor="sonible"),
    "smart:comp 2 Upgrade from smart:comp 1": PluginInfo(
        name="smart:comp 2 Upgrade from smart:comp 1",
        category="Compressor",
        vendor="sonible",
    ),
    "BLEASS Compressor": PluginInfo(name="BLEASS Compressor", category="Compressor", vendor="BLEASS"),
    "Low Control": PluginInfo(name="Low Control", category="Compressor", vendor="Black Salt Audio"),
    "MC2000 Multi-Band Compressor HD v7": PluginInfo(
        name="MC2000 Multi-Band Compressor HD v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "McDSP CompressorBank v7": PluginInfo(name="McDSP CompressorBank v7", category="Compressor", vendor="McDSP"),
    "McDSP 4030 Retro Compressor v7": PluginInfo(
        name="McDSP 4030 Retro Compressor v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "McDSP 6030 Ultimate Compressor v7": PluginInfo(
        name="McDSP 6030 Ultimate Compressor v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "McDSP 6034 Ultimate Multi-band v7": PluginInfo(
        name="McDSP 6034 Ultimate Multi-band v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "McDSP MC2000 Multi-Band Compressor v7": PluginInfo(
        name="McDSP MC2000 Multi-Band Compressor v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "McDSP SPC2000 Serial/Parallel Compressor v7": PluginInfo(
        name="McDSP SPC2000 Serial/Parallel Compressor v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "4030 Retro Compressor Native v7": PluginInfo(
        name="4030 Retro Compressor Native v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "6034 Ultimate Multi-band Native v7": PluginInfo(
        name="6034 Ultimate Multi-band Native v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "SPC2000 Serial/Parallel Compressor HD v7": PluginInfo(
        name="SPC2000 Serial/Parallel Compressor HD v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "CompressorBank Native v7": PluginInfo(name="CompressorBank Native v7", category="Compressor", vendor="McDSP"),
    "CompressorBank HD v7": PluginInfo(name="CompressorBank HD v7", category="Compressor", vendor="McDSP"),
    "SPC2000 Serial/Parallel Compressor Native v7": PluginInfo(
        name="SPC2000 Serial/Parallel Compressor Native v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "4030 Retro Compressor HD v7": PluginInfo(
        name="4030 Retro Compressor HD v7", category="Compressor", vendor="McDSP"
    ),
    "6030 Ultimate Compressor HD v7": PluginInfo(
        name="6030 Ultimate Compressor HD v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "6034 Ultimate Multi-band HD v7": PluginInfo(
        name="6034 Ultimate Multi-band HD v7",
        category="Compressor",
        vendor="McDSP",
    ),
    "Combustor": PluginInfo(name="Combustor", category="Compressor", vendor="W. A. Production"),
    "Opto-3A": PluginInfo(name="Opto-3A", category="Compressor", vendor="Apogee"),
    "ModComp": PluginInfo(name="ModComp", category="Compressor", vendor="Apogee"),
    "SSL Native Bus Compressor 2": PluginInfo(
        name="SSL Native Bus Compressor 2",
        category="Compressor",
        vendor="Solid State Logic",
    ),
    "SSL Native X-Comp": PluginInfo(
        name="SSL Native X-Comp", category="Compressor", vendor="Solid State Logic"
    ),
    "SSL LMC+": PluginInfo(name="SSL LMC+", category="Compressor", vendor="Solid State Logic"),
    "SSL Fusion HF Compressor": PluginInfo(
        name="SSL Fusion HF Compressor",
        category="Compressor",
        vendor="Solid State Logic",
    ),
    "SKYE Dynamics": PluginInfo(name="SKYE Dynamics", category="Compressor", vendor="Signum Audio"),
    "SKYE Dynamics (Surround)": PluginInfo(
        name="SKYE Dynamics (Surround)",
        category="Compressor",
        vendor="Signum Audio",
    ),
    "FireChild": PluginInfo(name="FireChild", category="Compressor", vendor="Tone Empire"),
    "Comp DIODE-609": PluginInfo(name="Comp DIODE-609", category="Compressor", vendor="Arturia"),
    "Smoov": PluginInfo(name="Smoov", category="Compressor", vendor="Caelum Audio"),
    "Schlap": PluginInfo(name="Schlap", category="Compressor", vendor="Caelum Audio"),
    "AVA Multiband Compressor": PluginInfo(
        name="AVA Multiband Compressor",
        category="Compressor",
        vendor="Harrison Consoles",
    ),
    "Dragon Fire": PluginInfo(name="Dragon Fire", category="Compressor", vendor="Denise Audio"),
    "BIGGIFIER by Aden": PluginInfo(
        name="BIGGIFIER by Aden", category="Compressor", vendor="W. A. Production"
    ),
    "MTurboCompLE": PluginInfo(name="MTurboCompLE", category="Compressor", vendor="MeldaProduction"),
    "Melda Production MTurboComp": PluginInfo(
        name="Melda Production MTurboComp",
        category="Compressor",
        vendor="MeldaProduction",
    ),
    "TDR Molot GE": PluginInfo(name="TDR Molot GE", category="Compressor", vendor="Tokyo Dawn Labs"),
    "Model 5000": PluginInfo(name="Model 5000", category="Compressor", vendor="Tone Empire"),
    "OptoRED": PluginInfo(name="OptoRED", category="Compressor", vendor="Tone Empire"),
    "CP1A Compressor": PluginInfo(name="CP1A Compressor", category="Compressor", vendor="Mellowmuse"),
    "CP2V Compressor": PluginInfo(name="CP2V Compressor", category="Compressor", vendor="Mellowmuse"),
    "CP3V Compressor": PluginInfo(name="CP3V Compressor", category="Compressor", vendor="Mellowmuse"),
    "IA-LA1": PluginInfo(name="IA-LA1", category="Compressor", vendor="Initial Audio"),
    "FirePresser": PluginInfo(name="FirePresser", category="Compressor", vendor="United Plugins"),
    "Royal Compressor": PluginInfo(name="Royal Compressor", category="Compressor", vendor="United Plugins"),
    "PSP oldTimerMB": PluginInfo(name="PSP oldTimerMB", category="Compressor", vendor="PSP Audioware"),
    "Comp TUBE-STA": PluginInfo(name="Comp TUBE-STA", category="Compressor", vendor="Arturia"),
    "Comp VCA-65": PluginInfo(name="Comp VCA-65", category="Compressor", vendor="Arturia"),
    "Compressor": PluginInfo(name="Compressor", category="Compressor", vendor="Stagecraft"),
    "LDC2 Compander": PluginInfo(name="LDC2 Compander", category="Compressor", vendor="Audiority"),
    "Puncher 2": PluginInfo(name="Puncher 2", category="Compressor", vendor="W. A. Production"),
    "SphereComp": PluginInfo(name="SphereComp", category="Compressor", vendor="W. A. Production"),
    "EFEKTOR CP3603 Compressor": PluginInfo(
        name="EFEKTOR CP3603 Compressor", category="Compressor", vendor="Kuassa"
    ),
    "PSP FETpressor": PluginInfo(name="PSP FETpressor", category="Compressor", vendor="PSP Audioware"),
    "U73b Compressor": PluginInfo(name="U73b Compressor", category="Compressor", vendor="Audified"),
    "Summit Audio TLA-100A Compressor": PluginInfo(
        name="Summit Audio TLA-100A Compressor",
        category="Compressor",
        vendor="Softube",
    ),
    "MTurboComp": PluginInfo(name="MTurboComp", category="Compressor", vendor="MeldaProduction"),
    "Multiband Sidechain3": PluginInfo(
        name="Multiband Sidechain3",
        category="Compressor",
        vendor="Vengeance Sound",
    ),
    "Manic Compressor": PluginInfo(
        name="Manic Compressor", category="Compressor", vendor="Boz Digital Labs"
    ),
    "CFA Sound GRIP Valve Drive Compressor": PluginInfo(
        name="CFA Sound GRIP Valve Drive Compressor",
        category="Compressor",
        vendor="Resonance Sound",
    ),
    "Pro Series Compressor": PluginInfo(
        name="Pro Series Compressor", category="Compressor", vendor="Positive Grid"
    ),
    "Blue Cat's Dynamics": PluginInfo(
        name="Blue Cat's Dynamics", category="Compressor", vendor="Blue Cat Audio"
    ),
    "PSP oldTimerME": PluginInfo(name="PSP oldTimerME", category="Compressor", vendor="PSP Audioware"),
    "PSP MasterComp": PluginInfo(name="PSP MasterComp", category="Compressor", vendor="PSP Audioware"),
    "MasterMix Buss Compressor": PluginInfo(
        name="MasterMix Buss Compressor",
        category="Compressor",
        vendor="Minimal System Group",
    ),
    "MDynamics": PluginInfo(name="MDynamics", category="Compressor", vendor="MeldaProduction"),
    "PSP BussPressor": PluginInfo(name="PSP BussPressor", category="Compressor", vendor="PSP Audioware"),
    "Punch Evolved": PluginInfo(
        name="Punch Evolved", category="Compressor", vendor="Minimal System Group"
    ),
    "Tri-Comp Multiband Compressor": PluginInfo(
        name="Tri-Comp Multiband Compressor",
        category="Compressor",
        vendor="Minimal System Group",
    ),
    "Blue Cat's MB-5 Dynamix": PluginInfo(
        name="Blue Cat's MB-5 Dynamix",
        category="Compressor",
        vendor="Blue Cat Audio",
    ),
    "Master Compressor": PluginInfo(
        name="Master Compressor",
        category="Compressor",
        vendor="Minimal System Group",
    ),
    "Punch Compressor": PluginInfo(
        name="Punch Compressor",
        category="Compressor",
        vendor="Minimal System Group",
    ),
    "MDynamicsMB": PluginInfo(name="MDynamicsMB", category="Compressor", vendor="MeldaProduction"),
    "MModernCompressor": PluginInfo(
        name="MModernCompressor", category="Compressor", vendor="MeldaProduction"
    ),
    "SSi Pro Compressor": PluginInfo(
        name="SSi Pro Compressor",
        category="Compressor",
        vendor="Minimal System Group",
    ),
}
