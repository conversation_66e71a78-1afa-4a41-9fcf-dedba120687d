"""
Fuzzy plugin matching system for robust plugin detection.

This module provides sophisticated string matching algorithms to handle
plugin name variations, vendor differences, and version mismatches.
"""

import re
from typing import Dict, List, Tuple, Optional, Set, Any
from difflib import SequenceMatcher


class FuzzyPluginMatcher:
    def __init__(self, plugin_database: Dict[str, Any]):
        self.plugin_database = plugin_database
        self._build_search_indices()
        
    def _build_search_indices(self):
        """Build optimized search indices for faster fuzzy matching."""
        self.normalized_db = {}
        self.token_index = {}
        self.vendor_index = {}
        
        for plugin_name, plugin_info in self.plugin_database.items():
            # Normalize and index plugin names
            normalized = self._normalize_plugin_name(plugin_name)
            self.normalized_db[normalized] = (plugin_name, plugin_info)
            
            # Build token index for fast token-based lookup
            tokens = self._extract_key_tokens(normalized)
            for token in tokens:
                if token not in self.token_index:
                    self.token_index[token] = []
                self.token_index[token].append((plugin_name, plugin_info))
            
            # Build vendor index
            vendor = plugin_info.vendor.lower().replace(' ', '')
            if vendor not in self.vendor_index:
                self.vendor_index[vendor] = []
            self.vendor_index[vendor].append((plugin_name, plugin_info))

    def _normalize_plugin_name(self, name: str) -> str:
        """
        Normalize plugin names for better matching.
        
        Handles:
        - Version numbers and suffixes
        - Vendor prefixes and variations  
        - Common formatting differences
        - Case normalization
        """
        normalized = name.lower()
        
        # Remove common prefixes
        prefixes = ['vst3:', 'vst:', 'au:', 'aax:', 'dx:', 'jsfx:', 'js:']
        for prefix in prefixes:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix):].strip()
                break
        
        # Remove vendor info in parentheses (handle nested parentheses)
        normalized = self._remove_parenthetical_info(normalized)
        
        # Normalize vendor prefixes
        vendor_patterns = [
            (r'^fabfilter\s+', ''),  # "FabFilter Pro-Q" -> "Pro-Q"
            (r'^waves\s+', ''),      # "Waves SSL G" -> "SSL G"
            (r'^uadx?\s+', ''),      # "UADx SSL G" -> "SSL G"
            (r'^ssl\s+native\s+', 'ssl '),  # "SSL Native X-Comp" -> "SSL X-Comp"
            (r'^plugin\s+alliance\s+', ''),
            (r'^brainworx\s+', ''),
            (r'^soundtoys\s+', ''),
            (r'^eventide\s+', ''),
            (r'^izotope\s+', ''),
        ]
        
        for pattern, replacement in vendor_patterns:
            normalized = re.sub(pattern, replacement, normalized)
        
        # Normalize version numbers and suffixes
        version_patterns = [
            r'\s+v\d+(\.\d+)*$',     # " v2", " v1.5"
            r'\s+mk\s*i{1,3}$',      # " MK II", " MKIII"
            r'\s+(2|3|4|5|6|7|8|9|10|11)$',  # " 2", " 11"
            r'\s+\d+$',              # " 4", " 2"
        ]
        
        for pattern in version_patterns:
            normalized = re.sub(pattern, '', normalized)
            
        # Handle common plugin name variations
        name_variations = [
            (r'\s+master$', ' compressor'),  # "alpha master" -> "alpha compressor"
            (r'\s+master\s', ' compressor '), # "alpha master eq" -> "alpha compressor eq"
        ]
        
        for pattern, replacement in name_variations:
            normalized = re.sub(pattern, replacement, normalized)
        
        # Remove common suffixes that don't affect core identity
        suffixes = [
            r'\s+plugin$',
            r'\s+vst$',
            r'\s+au$',
            r'\s+collection$',
            r'\s+series$',
            r'\s+bundle$',
            r'\s+\(.*?\)$',  # Remove any remaining parentheses
        ]
        
        for suffix in suffixes:
            normalized = re.sub(suffix, '', normalized)
        
        # Normalize spacing and special characters
        normalized = re.sub(r'[^\w\s\-]', ' ', normalized)  # Replace special chars with spaces
        normalized = re.sub(r'\s+', ' ', normalized)        # Collapse multiple spaces
        normalized = normalized.strip()
        
        return normalized

    def _remove_parenthetical_info(self, text: str) -> str:
        """Remove parenthetical information, handling nested parentheses."""
        result = text
        while '(' in result and ')' in result:
            # Find the last opening parenthesis and its matching closing parenthesis
            start = result.rfind('(')
            if start == -1:
                break
            end = result.find(')', start)
            if end == -1:
                break
            result = result[:start] + result[end+1:]
        return result.strip()

    def _extract_key_tokens(self, name: str) -> Set[str]:
        """Extract key tokens from a plugin name for token-based matching."""
        # Split on common delimiters
        tokens = re.split(r'[\s\-_]+', name.lower())
        
        # Filter out noise words
        noise_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'vs', 'v', 'edition', 'version'
        }
        
        key_tokens = set()
        for token in tokens:
            token = token.strip()
            if len(token) >= 2 and token not in noise_words and not token.isdigit():
                key_tokens.add(token)
        
        return key_tokens

    def _calculate_similarity_score(self, name1: str, name2: str) -> float:
        """Calculate similarity score between two plugin names."""
        norm1 = self._normalize_plugin_name(name1)
        norm2 = self._normalize_plugin_name(name2)
        
        # Exact match after normalization
        if norm1 == norm2:
            return 1.0
        
        # Sequence similarity (overall string similarity)
        sequence_similarity = SequenceMatcher(None, norm1, norm2).ratio()
        
        # Token-based similarity (how many key tokens match)
        tokens1 = self._extract_key_tokens(norm1)
        tokens2 = self._extract_key_tokens(norm2)
        
        if not tokens1 or not tokens2:
            token_similarity = 0.0
        else:
            intersection = tokens1.intersection(tokens2)
            union = tokens1.union(tokens2)
            token_similarity = len(intersection) / len(union) if union else 0.0
        
        # Weighted combination (token matching is more important for plugins)
        combined_score = (token_similarity * 0.7) + (sequence_similarity * 0.3)
        
        return combined_score

    def find_best_match(self, plugin_name: str, min_confidence: float = 0.6) -> Optional[Tuple[str, Any, float]]:
        """
        Find the best matching plugin in the database.
        
        Returns:
            Tuple of (matched_plugin_name, plugin_info, confidence_score) or None
        """
        best_match = None
        best_score = 0.0
        best_plugin_info = None
        
        # Step 1: Try exact match after normalization
        normalized_input = self._normalize_plugin_name(plugin_name)
        if normalized_input in self.normalized_db:
            original_name, plugin_info = self.normalized_db[normalized_input]
            return (original_name, plugin_info, 1.0)
        
        # Step 2: Token-based pre-filtering for performance
        input_tokens = self._extract_key_tokens(normalized_input)
        candidate_plugins = {}
        
        for token in input_tokens:
            if token in self.token_index:
                for plugin_name_db, plugin_info in self.token_index[token]:
                    candidate_plugins[plugin_name_db] = plugin_info
        
        # If no token matches, fall back to full database search (expensive)
        if not candidate_plugins:
            candidate_plugins = self.plugin_database
        
        # Step 3: Calculate similarity scores for candidates
        for plugin_name_db, plugin_info in candidate_plugins.items():
            score = self._calculate_similarity_score(plugin_name, plugin_name_db)
            
            if score > best_score and score >= min_confidence:
                best_score = score
                best_match = plugin_name_db
                best_plugin_info = plugin_info
        
        if best_match:
            return (best_match, best_plugin_info, best_score)
        
        return None

    def find_vendor_matches(self, plugin_name: str, vendor_hint: str = None) -> List[Tuple[str, Any, float]]:
        """
        Find plugins matching by vendor, useful when plugin name is unclear.
        
        Args:
            plugin_name: The plugin name to match
            vendor_hint: Optional vendor name hint from plugin metadata
        """
        matches = []
        
        if vendor_hint:
            vendor_normalized = vendor_hint.lower().replace(' ', '')
            if vendor_normalized in self.vendor_index:
                for plugin_name_db, plugin_info in self.vendor_index[vendor_normalized]:
                    score = self._calculate_similarity_score(plugin_name, plugin_name_db)
                    if score >= 0.4:  # Lower threshold for vendor-based matching
                        matches.append((plugin_name_db, plugin_info, score))
        
        # Sort by score, descending
        matches.sort(key=lambda x: x[2], reverse=True)
        return matches[:5]  # Return top 5 matches

    def get_match_info(self, plugin_name: str, vendor_hint: str = None) -> Dict[str, Any]:
        """
        Get comprehensive matching information for a plugin.
        
        This is the main public interface for the fuzzy matching system.
        """
        # Try fuzzy matching first
        fuzzy_result = self.find_best_match(plugin_name)
        
        if fuzzy_result:
            matched_name, plugin_info, confidence = fuzzy_result
            return {
                "category": plugin_info.category,
                "vendor": plugin_info.vendor,
                "confidence": confidence,
                "decoder": getattr(plugin_info, 'decoder', None),
                "matched_name": matched_name,
                "detection_method": "fuzzy_match"
            }
        
        # Try vendor-based matching if available
        if vendor_hint:
            vendor_matches = self.find_vendor_matches(plugin_name, vendor_hint)
            if vendor_matches:
                matched_name, plugin_info, confidence = vendor_matches[0]
                return {
                    "category": plugin_info.category,
                    "vendor": plugin_info.vendor,
                    "confidence": confidence * 0.8,  # Reduce confidence for vendor-only matches
                    "decoder": getattr(plugin_info, 'decoder', None),
                    "matched_name": matched_name,
                    "detection_method": "vendor_match"
                }
        
        # No match found
        return {
            "category": "Unknown",
            "confidence": 0.0,
            "detection_method": "no_match"
        }