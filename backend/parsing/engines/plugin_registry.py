from typing import Dict, Any, Optional, List, Tuple
from functools import lru_cache

from .plugin_data import (
    PLUGIN_DATABASE,
    VENDOR_MODEL_PATTERNS,
    KEYWORD_PATTERNS,
    KNOWN_METERING_PLUGINS,
    PluginInfo,
)
from .fuzzy_plugin_matcher import FuzzyPluginMatcher


class PluginRegistry:
    def __init__(self):
        self.plugin_database = PLUGIN_DATABASE
        self.vendor_model_patterns = VENDOR_MODEL_PATTERNS
        self.keyword_patterns = KEYWORD_PATTERNS
        self.metering_plugins = KNOWN_METERING_PLUGINS
        # Initialize fuzzy matcher for robust plugin detection
        self.fuzzy_matcher = FuzzyPluginMatcher(PLUGIN_DATABASE)
        # Initialize result cache with LRU for performance
        self._get_plugin_info_cached = lru_cache(maxsize=1000)(self._get_plugin_info_impl)

    def _clean_plugin_name(self, plugin_name: str) -> str:
        """
        Clean plugin name by removing prefixes and vendor info for better matching.
        """
        # Remove common prefixes
        clean_name = plugin_name
        prefixes_to_remove = ["AU:", "VST:", "VST3:", "VST2:", "JSFX:", "JS:", "DX:", "AAX:"]
        for prefix in prefixes_to_remove:
            if clean_name.startswith(prefix):
                clean_name = clean_name[len(prefix):].strip()
                break
        
        # Remove vendor info in parentheses at the end
        if "(" in clean_name and clean_name.endswith(")"):
            clean_name = clean_name[:clean_name.rfind("(")].strip()
            
        return clean_name

    def get_plugin_info(self, plugin_name: str, vendor_hint: str = None) -> Dict[str, Any]:
        """
        Retrieves category, vendor, confidence, and decoder info for a given plugin name.
        Uses fuzzy matching as primary method with fallback to legacy pattern matching.
        Results are cached for performance.
        
        Args:
            plugin_name: The plugin name to analyze
            vendor_hint: Optional vendor information from plugin metadata
        """
        # Use cached implementation - vendor_hint can be None so we convert to empty string for cache key
        return self._get_plugin_info_cached(plugin_name, vendor_hint or "")
    
    def _get_plugin_info_impl(self, plugin_name: str, vendor_hint: str) -> Dict[str, Any]:
        """
        Internal implementation of get_plugin_info that will be cached.
        """
        # Convert empty string back to None for vendor_hint
        vendor_hint = vendor_hint if vendor_hint else None
        
        # Primary: Try fuzzy matching system (most robust)
        fuzzy_result = self.fuzzy_matcher.get_match_info(plugin_name, vendor_hint)
        if fuzzy_result["confidence"] >= 0.6:
            return fuzzy_result
        
        # Fallback: Legacy exact matching and pattern-based detection
        return self._legacy_get_plugin_info(plugin_name)
    
    def _legacy_get_plugin_info(self, plugin_name: str) -> Dict[str, Any]:
        """
        Legacy plugin detection method - kept as fallback for edge cases.
        Uses exact match, vendor+model patterns, then keyword patterns.
        """
        plugin_name_lower = plugin_name.lower()
        clean_name = self._clean_plugin_name(plugin_name)
        clean_name_lower = clean_name.lower()

        # Tier 1: Exact match in PLUGIN_DATABASE (try both original and cleaned names)
        for name_to_try in [plugin_name, clean_name]:
            if name_to_try in self.plugin_database:
                plugin_info = self.plugin_database[name_to_try]
                return {
                    "category": plugin_info.category,
                    "vendor": plugin_info.vendor,
                    "confidence": plugin_info.confidence,
                    "decoder": getattr(plugin_info, 'decoder', None),
                    "detection_method": "exact_match"
                }
        
        # Tier 1.5: Try case-insensitive matching for cleaned name
        for db_key, plugin_info in self.plugin_database.items():
            if db_key.lower() == clean_name_lower:
                return {
                    "category": plugin_info.category,
                    "vendor": plugin_info.vendor, 
                    "confidence": plugin_info.confidence,
                    "decoder": getattr(plugin_info, 'decoder', None),
                    "detection_method": "case_insensitive_match"
                }

        # Tier 2: Vendor + Model pattern matching
        for (vendor_pattern, model_pattern), info in self.vendor_model_patterns.items():
            if (
                vendor_pattern in plugin_name_lower
                and model_pattern in plugin_name_lower
            ):
                return {**info, "detected_via": "vendor_model_pattern"}

        # Tier 3: Keyword pattern matching
        total_keyword_confidence = 0.0
        matched_keywords = []
        for keyword, info in self.keyword_patterns.items():
            if keyword in plugin_name_lower:
                total_keyword_confidence += info["confidence"]
                matched_keywords.append(keyword)

        if len(matched_keywords) > 1:
            total_keyword_confidence = min(total_keyword_confidence * 1.2, 0.95)

        if total_keyword_confidence > 0.6:
            # Use the category from the highest confidence keyword match, or default to "Unknown"
            best_category = "Unknown"
            max_confidence_keyword = 0.0
            for keyword, info in self.keyword_patterns.items():
                if (
                    keyword in plugin_name_lower
                    and info["confidence"] > max_confidence_keyword
                ):
                    best_category = info.get("category", "Unknown")
                    max_confidence_keyword = info["confidence"]

            return {
                "category": best_category,
                "confidence": total_keyword_confidence,
                "matched_keywords": matched_keywords,
                "detected_via": "keyword_analysis",
            }

        return {"category": "Unknown", "confidence": 0.0, "detection_method": "no_match"}

    def get_category(self, plugin_name: str) -> str:
        """
        Gets the category of a plugin.
        """
        return self.get_plugin_info(plugin_name).get("category", "Unknown")

    def get_decoder_key(self, plugin_name: str) -> Optional[str]:
        """
        Gets the decoder key (e.g., 'ProL2Decoder') for a plugin, if defined.
        """
        return self.get_plugin_info(plugin_name).get("decoder")

    def get_confidence(self, plugin_name: str) -> float:
        """
        Gets the confidence score of a plugin's classification.
        """
        return self.get_plugin_info(plugin_name).get("confidence", 0.0)

    def is_metering_plugin(self, plugin_name: str) -> bool:
        """
        Checks if a plugin is a known metering-only plugin.
        """
        return plugin_name in self.metering_plugins
