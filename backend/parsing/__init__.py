"""
Parsing module for REAPER project files.
This package provides functionality to parse and extract data from RPP files.
"""

# Core parsing functionality
from .core import parse_rpp_content, parse_rpp_file, parse_rpp_fileobj

# Data models
from .models import Track, MasterTrack, RoutingLink, Metadata, ParsedSession

# Output generators
from .generators import generate_markdown, generate_rfxchain, generate_tracktemplate

# Make extractors available through the package
from .extractors.metadata_extractor import MetadataExtractor
from .extractors.master_track_extractor import MasterTrackExtractor

# Removed: from .extractors.track_extractor import extract_tracks
from .extractors.routing_extractor import extract_routing

# Note: Other extractors like fx, automation are used internally by core.py now

__all__ = [
    # Core parsing functions
    "parse_rpp_content",
    "parse_rpp_file",
    "parse_rpp_fileobj",
    # Data models
    "Track",
    "MasterTrack",
    "RoutingLink",
    "Metadata",
    "ParsedSession",
    # Extractors (Only exporting those potentially useful outside the core parsing flow)
    "MetadataExtractor",
    "MasterTrackExtractor",
    # Removed: "extract_tracks",
    "extract_routing",
    # Output generators
    "generate_markdown",
    "generate_rfxchain",
    "generate_tracktemplate",
]
