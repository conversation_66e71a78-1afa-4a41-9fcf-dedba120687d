"""
Track-level automation extraction functionality.
Refactored to use BaseExtractor and ParsingContext.
"""

from typing import List, Union, Optional, Tuple, Dict, Any
from rpp import Element
from ..models import AutomationLane
from ..infrastructure.base_extractor import BaseExtractor
from ..infrastructure.parsing_context import ParsingContext
from .automation_utils import (
    extract_automation_attributes,
    extract_envelope_guid,
    extract_automation_points,
    calculate_effective_points,
    # safe_find_item # Not directly used in the refactored top-level methods here
)


class TrackAutomationExtractor(BaseExtractor):
    """
    Extracts track-level automation envelopes, including direct track automation
    and send/receive automation.
    """

    ENVELOPE_TYPES = {
        "VOLENV": "Volume (Pre-FX)",
        "VOLENV2": "Volume (Post-FX)",
        "VOLENV3": "Volume (Trim)",
        "PANENV": "Pan (Pre-FX)",
        "PANENV2": "Pan (Post-FX)",
        "WIDTHENV": "Width (Pre-FX)",
        "WIDTHENV2": "Width (Post-FX)",
        "MUTEENV": "Mute",
        "DUALPANENVL": "Pan Left (Pre-FX)",
        "DUALPANENVL2": "Pan Left (Post-FX)",
        "DUALPANENV": "Pan Right (Pre-FX)",
        "DUALPANENV2": "Pan Right (Post-FX)",
    }

    SEND_ENVELOPE_TYPES = {
        "AUXVOLENV": "Volume",
        "AUXPANENV": "Pan",
        "AUXMUTEENV": "Mute",
    }

    def __init__(self, context: ParsingContext):
        super().__init__(context)
        self._track_name_map_by_idx: Dict[int, str] = (
            {}
        )  # For send automation display names

    def _initialize_track_name_map(self):
        if self._track_name_map_by_idx:
            return
        for idx, track_element in enumerate(self.context.cache.tracks):
            track_name = f"Track {idx+1}"
            # Find NAME in children lists
            for child in track_element.children:
                if isinstance(child, list) and len(child) > 1 and child[0] == "NAME":
                    track_name = child[1]
                    break
            self._track_name_map_by_idx[idx] = track_name

    def extract_direct_automation_for_track(
        self, track_element: Element
    ) -> Tuple[List[AutomationLane], bool, bool]:
        """
        Extracts direct track-level automation envelopes (Volume, Pan, Mute, Width).
        """
        automation_lanes: List[AutomationLane] = []
        volume_automated = False
        pan_automated = False

        for env_type_key, display_name_suffix in self.ENVELOPE_TYPES.items():
            # RPP stores envelopes as direct children, e.g., <VOLENV ...> or list ['VOLENV', ...]
            # self._find_element should handle finding these if they are structured as Elements.
            # If they are list-children, direct iteration might be needed or _find_element adapted.
            # For now, assuming _find_element works for RPP's structure or direct iteration is used.

            # Let's iterate children and check tag, as RPP structure is often flat list of lists/elements
            env_element_data = None
            for child in track_element:  # track_element.children if it's an Element
                child_tag_name = None
                if isinstance(child, Element) and hasattr(child, "tag"):
                    child_tag_name = child.tag
                elif isinstance(child, list) and child:
                    child_tag_name = child[0]

                if child_tag_name == env_type_key:
                    env_element_data = child
                    break  # Found it

            if env_element_data is not None:
                guid = extract_envelope_guid(env_element_data)
                lane = AutomationLane(display_name=display_name_suffix, guid=guid)
                extract_automation_attributes(env_element_data, lane)
                lane.points = extract_automation_points(env_element_data)
                lane.effective_points = calculate_effective_points(lane.points)

                if lane.effective_points > 0:
                    if "Volume" in display_name_suffix:
                        volume_automated = True
                    elif "Pan" in display_name_suffix:
                        pan_automated = True
                automation_lanes.append(lane)

        return automation_lanes, volume_automated, pan_automated

    def _get_source_track_info_from_auxrecv(
        self, auxrecv_data: list
    ) -> Tuple[Optional[str], Optional[int]]:
        """
        Extracts source track reference (index or GUID) from an AUXRECV list.
        Returns (track_ref_str, track_idx_if_numeric).
        """
        if len(auxrecv_data) > 1:
            track_ref_str = str(auxrecv_data[1])
            try:
                return track_ref_str, int(track_ref_str)  # If it's a numeric index
            except ValueError:
                return track_ref_str, None  # It's likely a GUID string
        return None, None

    def extract_all_send_automation(
        self,
    ) -> Dict[int, List[Tuple[str, AutomationLane]]]:
        """
        Extracts all send/receive automation envelopes from the project.
        Maps them to the *sending* track's index.
        The tuple contains (receiving_track_name, AutomationLane_for_the_send_on_sender).
        """
        self._initialize_track_name_map()
        send_automation_mappings: Dict[int, List[Tuple[str, AutomationLane]]] = {}

        for sender_idx, track_element in enumerate(self.context.cache.tracks):
            last_auxrecv_source_idx: Optional[int] = (
                None  # Index of the track that is SENDING to current track
            )
            last_auxrecv_dest_track_name: str = (
                ""  # Name of the track that is RECEIVING (current track_element)
            )
            last_auxrecv_dest_channel_idx: int = (
                0  # Dest channel on the current track_element for the receive
            )

            # Iterate through children of the current track_element (which is the destination for AUXRECVs)
            for (
                child_data
            ) in track_element:  # Assuming track_element.children if it's an Element
                child_tag_name: Optional[str] = None
                current_data_list: Optional[list] = None

                if isinstance(child_data, Element) and hasattr(child_data, "tag"):
                    child_tag_name = child_data.tag
                    current_data_list = (
                        child_data  # Pass element to utils, they handle list/element
                    )
                elif isinstance(child_data, list) and child_data:
                    child_tag_name = child_data[0]
                    current_data_list = child_data
                else:
                    continue  # Skip malformed children

                if child_tag_name == "AUXRECV" and isinstance(current_data_list, list):
                    # This AUXRECV is on `track_element` (the destination).
                    # It tells us which track (`source_track_ref_from_auxrecv`) is sending to it.
                    source_track_ref_from_auxrecv, src_idx_if_numeric = (
                        self._get_source_track_info_from_auxrecv(current_data_list)
                    )

                    if src_idx_if_numeric is not None:
                        last_auxrecv_source_idx = src_idx_if_numeric
                    elif source_track_ref_from_auxrecv:
                        # If it's a GUID, need to map GUID to index. Cache should provide this.
                        # For now, if not numeric, we can't easily map to sender_idx without full GUID map.
                        # This part of RPP structure is tricky: AUXRECV's first param is sender's index/GUID.
                        # The automation (AUXVOLENV) appears *after* AUXRECV on the *receiving* track.
                        # We need to attribute this automation to the *sending* track.
                        # Let's assume for now `source_track_ref_from_auxrecv` is an index string if not GUID.
                        try:
                            last_auxrecv_source_idx = int(source_track_ref_from_auxrecv)
                        except ValueError:
                            self.context.add_warning(
                                f"AUXRECV on track {sender_idx} has non-numeric source ref '{source_track_ref_from_auxrecv}', cannot map send automation without GUID-to-index map."
                            )
                            last_auxrecv_source_idx = (
                                None  # Cannot process further without index
                            )

                    last_auxrecv_dest_track_name = self._track_name_map_by_idx.get(
                        sender_idx, f"Track {sender_idx+1}"
                    )

                    # AUXRECV parameters: [7] is dest_channel_pair_start_index (0 for 1-2, 2 for 3-4)
                    dest_channel_val = self._get_typed_value_from_list(
                        current_data_list, 7, int, default=0
                    )
                    last_auxrecv_dest_channel_idx = (
                        dest_channel_val if dest_channel_val is not None else 0
                    )

                elif (
                    child_tag_name in self.SEND_ENVELOPE_TYPES
                    and last_auxrecv_source_idx is not None
                    and current_data_list is not None
                ):
                    env_type_key = child_tag_name
                    display_suffix = self.SEND_ENVELOPE_TYPES[env_type_key]

                    guid = extract_envelope_guid(current_data_list)
                    channel_str = f"{last_auxrecv_dest_channel_idx*2+1}-{last_auxrecv_dest_channel_idx*2+2}"
                    display_name = f"Send to {last_auxrecv_dest_track_name} (Dest Ch {channel_str}): {display_suffix}"

                    lane = AutomationLane(display_name=display_name, guid=guid)
                    lane.dest_channel = last_auxrecv_dest_channel_idx  # Store the parsed dest channel index

                    extract_automation_attributes(current_data_list, lane)
                    lane.points = extract_automation_points(current_data_list)
                    lane.effective_points = calculate_effective_points(lane.points)

                    if last_auxrecv_source_idx not in send_automation_mappings:
                        send_automation_mappings[last_auxrecv_source_idx] = []
                    send_automation_mappings[last_auxrecv_source_idx].append(
                        (last_auxrecv_dest_track_name, lane)
                    )

        return send_automation_mappings

    def extract(self) -> Any:
        """
        BaseExtractor abstract method.
        This extractor provides specific methods for direct track and send automation.
        Orchestrator (core.py) should call those methods as needed.
        """
        self.context.add_warning(
            "TrackAutomationExtractor.extract() called. Use specific methods "
            "like extract_direct_automation_for_track() or extract_all_send_automation()."
        )
        return {
            "direct_track_automation_per_track": "Use extract_direct_automation_for_track(track_element)",
            "send_automation_mappings_by_sender_idx": "Use extract_all_send_automation()",
        }


# Standalone functions for backward compatibility with core.py
def extract_track_automation(
    track_element: Element,
) -> Tuple[List[AutomationLane], bool, bool]:
    """Standalone function for backward compatibility."""
    from ..infrastructure.parsing_context import ParsingContext
    from ..infrastructure.rpp_tree_cache import RPPTreeCache

    # Create a minimal context for the extractor
    # Note: For single track extraction, we create a minimal project tree
    from xml.etree.ElementTree import Element as ET

    minimal_tree = ET("PROJECT")
    context = ParsingContext(project_tree=minimal_tree, file_path="unknown.rpp")

    extractor = TrackAutomationExtractor(context)
    return extractor.extract_direct_automation_for_track(track_element)


def extract_send_automation_mappings(
    project_tree: Element,
) -> Dict[int, List[Tuple[str, AutomationLane]]]:
    """Standalone function for backward compatibility."""
    from ..infrastructure.parsing_context import ParsingContext
    from ..infrastructure.rpp_tree_cache import RPPTreeCache

    # Create a minimal context for the extractor
    context = ParsingContext(project_tree=project_tree, file_path="unknown.rpp")
    # The cache is automatically created by ParsingContext with the project_tree

    extractor = TrackAutomationExtractor(context)
    return extractor.extract_all_send_automation()
