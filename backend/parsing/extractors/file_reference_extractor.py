import os
from typing import List, Dict, Any, Optional
from urllib.parse import unquote

from ..models import Item, Metadata  # Track is not used in the current logic

# from rpp import Element # Element is not used directly in this analyzer's logic
from ..infrastructure.base_extractor import BaseExtractor  # For context access
from ..infrastructure.parsing_context import ParsingContext


# Common system/temp directory patterns to flag
SYSTEM_TEMP_PATTERNS = [
    "/tmp/",
    "/temp/",
    "/var/folders/",
    "/private/var/",
    "C:\\Users\\<USER>\\Windows\\Temp\\",
    # "/Users/", # Can be too broad on macOS/Linux if project is in user's home.
    # More specific user-related temp/cache paths might be better.
]


class FileReferenceAnalyzer(BaseExtractor):
    """
    Analyzes file references within extracted Item data for consistency and portability.
    Focuses on path patterns and metadata mismatches, not actual file existence.
    """

    def __init__(self, context: ParsingContext):
        super().__init__(context)
        # If specific config for this analyzer is needed, load from context or constants
        self.system_temp_patterns = SYSTEM_TEMP_PATTERNS

    def analyze(self, items: List[Item], metadata: Metadata) -> Dict[str, Any]:
        """
        Analyzes file references within the provided items and metadata.

        Args:
            items: List of Item objects extracted from the session.
            metadata: Metadata object for the session.

        Returns:
            A dictionary containing file reference analysis results.
        """
        analysis_results: Dict[str, Any] = {
            "absolute_paths_detected": False,
            "system_temp_paths_detected": False,
            "external_paths_detected": False,
            "sample_rate_mismatches_detected": False,
            "bit_depth_mismatches_detected": False,
            "problematic_file_references": [],
            "summary_messages": [],
        }

        project_sample_rate = metadata.sample_rate
        project_bit_depth = metadata.project_bit_depth

        problematic_references: List[Dict[str, Any]] = []
        absolute_paths_count = 0
        system_temp_paths_count = 0
        external_paths_count = 0  # Relative paths outside common project media folders
        sample_rate_mismatches_count = 0
        bit_depth_mismatches_count = 0

        for item in items:
            if not item.takes:  # Ensure item has takes
                continue
            for take in item.takes:
                if (
                    not take.source or not take.source.file_path
                ):  # Check if source and file_path exist
                    continue

                # Decode URL-encoded characters (e.g., %20 for space)
                try:
                    file_path = unquote(take.source.file_path)
                except Exception as e:
                    self.context.add_warning(
                        f"Item {item.guid}, Take {take.guid}: Could not unquote file path '{take.source.file_path}': {e}"
                    )
                    file_path = take.source.file_path  # Use raw path if unquote fails

                issues: List[str] = []
                is_absolute = os.path.isabs(file_path)

                # 1. Absolute Path Detection
                if is_absolute:
                    issues.append("Absolute path detected (may break portability)")
                    absolute_paths_count += 1
                    analysis_results["absolute_paths_detected"] = True

                # 2. System/Temp Path Detection (applies to absolute paths primarily)
                # For relative paths, this check is less meaningful unless they resolve to such locations.
                if is_absolute and any(
                    pattern.lower() in file_path.lower()
                    for pattern in self.system_temp_patterns
                ):
                    issues.append("References system/temp directory (non-portable)")
                    system_temp_paths_count += 1
                    analysis_results["system_temp_paths_detected"] = True

                # 3. External Path Detection (heuristic for relative paths)
                # Checks if a relative path points outside typical project subfolders.
                if not is_absolute and not file_path.startswith(
                    ("../", ".\\", "./")
                ):  # Simple check if it's not trying to go up
                    # More robust: check against a list of expected project media subfolders
                    common_media_folders = (
                        "Media/",
                        "Audio/",
                        "Stems/",
                        "Rendered/",
                        "Samples/",
                    )
                    if not any(
                        file_path.startswith(folder) for folder in common_media_folders
                    ):
                        issues.append(
                            "Relative path does not start with common media folder (check project organization)"
                        )
                        external_paths_count += 1
                        analysis_results["external_paths_detected"] = True
                elif not is_absolute and (
                    file_path.startswith(("../", ".\\"))
                ):  # Indicates path traversal
                    issues.append(
                        "Relative path uses parent directory traversal ('../' or '.\\'), ensure it stays within project."
                    )
                    external_paths_count += 1  # Count these as potentially external too
                    analysis_results["external_paths_detected"] = True

                # 4. Sample Rate Mismatch
                if (
                    take.source.sample_rate is not None
                    and project_sample_rate is not None
                ):
                    if take.source.sample_rate != project_sample_rate:
                        issues.append(
                            f"Sample rate mismatch: {take.source.sample_rate}Hz (item) vs {project_sample_rate}Hz (project)"
                        )
                        sample_rate_mismatches_count += 1
                        analysis_results["sample_rate_mismatches_detected"] = True

                # 5. Bit Depth Mismatch
                if take.source.bit_depth is not None and project_bit_depth is not None:
                    if take.source.bit_depth != project_bit_depth:
                        issues.append(
                            f"Bit depth mismatch: {take.source.bit_depth}bit (item) vs {project_bit_depth}bit (project)"
                        )
                        bit_depth_mismatches_count += 1
                        analysis_results["bit_depth_mismatches_detected"] = True

                if issues:
                    problematic_references.append(
                        {
                            "item_id": item.guid,
                            "take_id": take.guid,
                            "file_path": file_path,
                            "issues": issues,
                        }
                    )

        analysis_results["problematic_file_references"] = problematic_references
        self._generate_summary_messages(
            analysis_results,
            absolute_paths_count,
            system_temp_paths_count,
            external_paths_count,
            sample_rate_mismatches_count,
            bit_depth_mismatches_count,
            len(problematic_references),
        )

        return analysis_results

    def _generate_summary_messages(
        self,
        results_dict: Dict[str, Any],
        abs_count: int,
        sys_count: int,
        ext_count: int,
        sr_mismatch: int,
        bd_mismatch: int,
        total_problematic_refs: int,
    ):
        """Helper to generate summary messages."""
        if abs_count > 0:
            results_dict["summary_messages"].append(
                f"⚠️ {abs_count} absolute file path(s) detected. These may break if the project is moved."
            )
        if sys_count > 0:
            results_dict["summary_messages"].append(
                f"⚠️ {sys_count} file reference(s) point to system/temp directories. This is non-portable."
            )
        if ext_count > 0:  # This now includes '..' traversals
            results_dict["summary_messages"].append(
                f"⚠️ {ext_count} relative file path(s) may point outside the main project media structure. Review project organization."
            )
        if sr_mismatch > 0:
            results_dict["summary_messages"].append(
                f"⚠️ {sr_mismatch} sample rate mismatch(es) detected between items and project settings."
            )
        if bd_mismatch > 0:
            results_dict["summary_messages"].append(
                f"⚠️ {bd_mismatch} bit depth mismatch(es) detected between items and project settings."
            )

        if total_problematic_refs == 0:
            results_dict["summary_messages"].append(
                "✅ No problematic file references detected based on path patterns and metadata."
            )

    def extract(self) -> Any:
        """
        BaseExtractor abstract method.
        This analyzer is typically called with specific data (items, metadata).
        If called directly, it implies context should hold necessary data or it returns empty.
        """
        # Option 1: Assume items and metadata are in context (e.g., self.context.project_items, self.context.project_metadata)
        # items = getattr(self.context, 'project_items', [])
        # metadata = getattr(self.context, 'project_metadata', None)
        # if not items or not metadata:
        #     self.context.add_warning("FileReferenceAnalyzer.extract called without items/metadata in context.")
        #     return { "summary_messages": ["Analysis skipped: Items or Metadata not found in context."]}
        # return self.analyze(items, metadata)

        # Option 2: This extractor is meant to be called with analyze() method directly.
        self.context.add_warning(
            "FileReferenceAnalyzer.extract() called directly. Use analyze(items, metadata) method for analysis."
        )
        return {
            "absolute_paths_detected": False,
            "system_temp_paths_detected": False,
            "external_paths_detected": False,
            "sample_rate_mismatches_detected": False,
            "bit_depth_mismatches_detected": False,
            "problematic_file_references": [],
            "summary_messages": [
                "Analysis not run via direct extract() call. Use analyze(items, metadata)."
            ],
        }


# Standalone function for backward compatibility with core.py
def analyze_file_references(
    project_tree,  # Element, not used directly in current implementation
    items: List[Item],
    metadata: Metadata,
    tracks,  # List[Track], not used in current implementation
) -> Dict[str, Any]:
    """Standalone function for backward compatibility."""
    from ..infrastructure.parsing_context import ParsingContext

    # Create a minimal context for the analyzer
    context = ParsingContext(project_tree=project_tree, file_path="unknown.rpp")

    analyzer = FileReferenceAnalyzer(context)
    return analyzer.analyze(items, metadata)
