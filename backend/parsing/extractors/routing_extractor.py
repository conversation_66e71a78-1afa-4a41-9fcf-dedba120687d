"""
Routing extraction functionality for RPP projects.
Refactored to use BaseExtractor and ParsingContext.
"""

from typing import Dict, List, Tuple, Optional
from rpp import Element
from ..models import (
    RoutingLink,
    Track,
    AutomationLane,
)  # Track, AutomationLane for update_routing_automation_flags
from ..infrastructure.base_extractor import BaseExtractor
from ..infrastructure.parsing_context import ParsingContext


class RoutingExtractor(BaseExtractor):
    """
    Extracts routing information (sends/receives) between tracks.
    """

    def __init__(self, context: ParsingContext):
        super().__init__(context)
        self._track_name_map: Dict[str, str] = {}  # Maps GUID/index_str to Name
        self._guid_map: Dict[str, str] = {}  # Maps index_str to GUID

    def _initialize_mappings(self):
        """
        Initializes track name and GUID mappings using the cached track elements.
        The cache should be populated before this extractor runs.
        """
        if self._track_name_map and self._guid_map:  # Avoid re-initialization
            return

        cached_track_elements = self.context.cache.tracks
        if not cached_track_elements:
            self.context.add_warning(
                "RoutingExtractor: Track cache is empty. Cannot create mappings."
            )
            return

        for idx, track_element in enumerate(cached_track_elements):
            track_guid = self._get_typed_value_from_list(
                track_element.attrib, 0, str, default=f"Track_{idx+1}"
            )

            # Find NAME in children lists
            track_name = f"Track {idx+1}"  # Default name
            for child in track_element.children:
                if isinstance(child, list) and len(child) > 1 and child[0] == "NAME":
                    track_name = child[1]
                    break

            self._track_name_map[track_guid] = track_name
            self._track_name_map[str(idx)] = track_name  # Map by index string
            self._guid_map[str(idx)] = track_guid

    def extract(self) -> List[RoutingLink]:
        """
        Extracts all routing links from the project.
        """
        self._initialize_mappings()
        routing_data: List[RoutingLink] = []

        cached_track_elements = self.context.cache.tracks
        if not cached_track_elements:
            self.context.add_warning(
                "RoutingExtractor: Track cache is empty. Skipping routing extraction."
            )
            return routing_data

        for idx, track_element in enumerate(cached_track_elements):
            # Source GUID from element attribute or default from index
            source_guid = self._get_typed_value_from_list(
                track_element.attrib,
                0,
                str,
                default=self._guid_map.get(str(idx), f"Track_{idx+1}_GUID_Fallback"),
            )
            source_name = self._track_name_map.get(
                source_guid, self._track_name_map.get(str(idx), f"Track {idx+1}")
            )

            self._extract_receives(
                track_element, source_name, source_guid, routing_data
            )
            self._extract_sends(track_element, source_name, source_guid, routing_data)

        return routing_data

    def _extract_receives(
        self,
        track_element: Element,
        destination_name: str,
        destination_guid: str,
        routing_data: List[RoutingLink],
    ) -> None:
        auxrecv_tags = self._find_all_elements(track_element, "AUXRECV")
        for recv_tag_list in auxrecv_tags:
            if not (
                recv_tag_list
                and isinstance(recv_tag_list, list)
                and len(recv_tag_list) > 1
            ):
                self.context.add_warning(
                    f"Malformed AUXRECV tag found in track {destination_guid}: {recv_tag_list}"
                )
                continue

            src_track_ref = self._get_typed_value_from_list(recv_tag_list, 1, str)
            if src_track_ref is None:
                self.context.add_warning(
                    f"Missing source track reference in AUXRECV tag for track {destination_guid}: {recv_tag_list}"
                )
                continue

            src_name = self._track_name_map.get(
                src_track_ref, f"Unknown Track ({src_track_ref})"
            )
            src_guid = self._guid_map.get(
                src_track_ref, src_track_ref
            )  # Fallback to ref if GUID not mapped (e.g. master)

            volume, pan, mute, src_channel, dest_channel, channels = (
                self._extract_routing_properties(
                    recv_tag_list, "AUXRECV", destination_guid
                )
            )

            routing_data.append(
                RoutingLink(
                    source=src_name,
                    destination=destination_name,
                    type="Receive",
                    source_guid=src_guid,
                    destination_guid=destination_guid,
                    volume=volume,
                    pan=pan,
                    muted=mute,
                    src_channel=src_channel,
                    dest_channel=dest_channel,
                    channels=channels,
                )
            )

    def _extract_sends(
        self,
        track_element: Element,
        source_name: str,
        source_guid: str,
        routing_data: List[RoutingLink],
    ) -> None:
        send_tags = self._find_all_elements(track_element, "SEND")
        for send_tag_list in send_tags:
            if not (
                send_tag_list
                and isinstance(send_tag_list, list)
                and len(send_tag_list) > 1
            ):
                self.context.add_warning(
                    f"Malformed SEND tag found in track {source_guid}: {send_tag_list}"
                )
                continue

            dest_track_ref = self._get_typed_value_from_list(send_tag_list, 1, str)
            if dest_track_ref is None:
                self.context.add_warning(
                    f"Missing destination track reference in SEND tag for track {source_guid}: {send_tag_list}"
                )
                continue

            dest_name = self._track_name_map.get(
                dest_track_ref, f"Unknown Track ({dest_track_ref})"
            )
            dest_guid = self._guid_map.get(dest_track_ref, dest_track_ref)

            volume, pan, mute, src_channel, dest_channel, channels = (
                self._extract_routing_properties(send_tag_list, "SEND", source_guid)
            )

            routing_data.append(
                RoutingLink(
                    source=source_name,
                    destination=dest_name,
                    type="Send",
                    source_guid=source_guid,
                    destination_guid=dest_guid,
                    volume=volume,
                    pan=pan,
                    muted=mute,
                    src_channel=src_channel,
                    dest_channel=dest_channel,
                    channels=channels,
                )
            )

    def _extract_routing_properties(
        self, routing_tag_list: list, tag_type: str, track_context_guid: str
    ) -> Tuple[float, float, bool, int, int, int]:
        volume = 1.0
        pan = 0.0  # Pan is not typically in SEND/AUXRECV main params, often automated separately or part of complex routing.
        mute = False
        src_channel = 0  # Default to first channel pair (1-2)
        dest_channel = 0  # Default to first channel pair (1-2)
        channels = 2  # Default to stereo

        # RPP SEND/AUXRECV format: [TYPE, TargetTrackIdxOrGUID, RoutingFlags, Volume, Mute, Mono, Phase, Pan?, SrcChanIdx, DstChanIdx, ...]
        # Indices based on common observations:
        # 3: Volume (linear)
        # 4: Mute (0 or 1)
        # 7: Source Channel Index (0 for 1-2, 2 for 3-4, etc.)
        # 9: Destination Channel Index (0 for 1-2, 2 for 3-4, etc.)

        vol_val = self._get_typed_value_from_list(routing_tag_list, 3, float)
        if vol_val is not None:
            volume = vol_val
        else:
            self.context.add_warning(
                f"Track {track_context_guid} {tag_type}: Could not parse volume from {routing_tag_list}"
            )

        mute_str = self._get_typed_value_from_list(routing_tag_list, 4, str)
        if mute_str is not None:
            mute = mute_str == "1"
        else:
            self.context.add_warning(
                f"Track {track_context_guid} {tag_type}: Could not parse mute status from {routing_tag_list}"
            )

        src_chan_val = self._get_typed_value_from_list(
            routing_tag_list, 7, int
        )  # Source audio channel start index
        if src_chan_val is not None:
            src_channel = src_chan_val

        # Pan value is often at index 8 if present, but its presence is not guaranteed.
        # For simplicity, pan extraction from this tag is omitted unless a clear pattern is established.
        # It's often controlled by automation on the send envelope.

        dest_chan_val = self._get_typed_value_from_list(
            routing_tag_list, 9, int
        )  # Destination audio channel start index
        if dest_chan_val is not None:
            dest_channel = dest_chan_val

        # Number of channels (e.g., 2 for stereo, 1 for mono) might be inferred or explicitly stated.
        # For now, defaulting to 2 (stereo). RPP might have this at index 6 (e.g. 0x1000000 for mono).
        # This needs more detailed RPP spec knowledge if precise channel count is required beyond src/dest indices.

        return volume, pan, mute, src_channel, dest_channel, channels

    def update_routing_automation_flags(
        self, routing_links: List[RoutingLink], tracks: List[Track]
    ) -> None:
        """
        Updates routing links with information about whether send parameters are automated.
        This is a post-processing step.
        """
        track_by_guid: Dict[str, Track] = {
            track.guid: track for track in tracks if track.guid
        }

        for link in routing_links:
            if not link.source_guid or link.source_guid not in track_by_guid:
                continue

            source_track = track_by_guid[link.source_guid]
            if not source_track.automation_lanes:
                continue

            for lane in source_track.automation_lanes:
                # Automation lane display_name often includes target track name/index and parameter type
                # Example: "Send to TrackName (Dest Ch 1-2): Volume"
                # Example: "Send to TrackIdx (Dest Ch 1-2): Volume"
                # We need to match link.destination (name) or link.destination_guid with info in lane.display_name

                # This matching logic can be complex and brittle.
                # A more robust way would be if automation lanes had structured data about their target.
                # For now, a simplified string check:
                lane_name_lower = lane.display_name.lower()
                link_dest_lower = link.destination.lower()

                # Check if the lane name contains the destination track name and matches the send/receive type
                # This is a heuristic.
                is_relevant_send_lane = (
                    "send to " + link_dest_lower
                ) in lane_name_lower or (
                    link_dest_lower + " send"
                ) in lane_name_lower  # Common variations

                # Further check for channel if possible, e.g. "(ch " + str(link.dest_channel_pair_start_idx)
                # For now, a simpler match on destination name and parameter type.
                # dest_channel_pair_start_idx = link.dest_channel // 2 (0 for 1-2, 1 for 3-4)
                # This requires dest_channel to be reliably parsed as start of pair.

                if is_relevant_send_lane:
                    if "volume" in lane_name_lower:
                        link.volume_automated = True
                    elif (
                        "pan" in lane_name_lower
                    ):  # RPP sends often have separate pan automation
                        link.pan_automated = True
                    elif "mute" in lane_name_lower:
                        link.mute_automated = True

                # Note: Receive automation is less common directly on AUXRECV, usually controlled by send side.


# Standalone functions for backward compatibility with core.py
def extract_routing(project_tree: Element) -> List[RoutingLink]:
    """Standalone function for backward compatibility."""
    from ..infrastructure.parsing_context import ParsingContext
    from ..infrastructure.rpp_tree_cache import RPPTreeCache

    # Create a minimal context for the extractor
    context = ParsingContext(project_tree=project_tree, file_path="unknown.rpp")
    # The cache is automatically created by ParsingContext with the project_tree

    extractor = RoutingExtractor(context)
    return extractor.extract()


def create_track_mapping(project_tree: Element) -> Dict[str, str]:
    """Creates a mapping from track index/GUID to track name."""
    track_name_map: Dict[str, str] = {}
    track_elements = project_tree.findall("TRACK")

    for idx, track_element in enumerate(track_elements):
        # Get GUID from track element attributes
        track_guid = (
            track_element.attrib[0] if track_element.attrib else f"Track_{idx+1}"
        )

        # Get track name from NAME element
        track_name = f"Track {idx+1}"  # Default name
        for child in track_element.children:
            if isinstance(child, list) and len(child) > 1 and child[0] == "NAME":
                track_name = child[1]
                break

        track_name_map[track_guid] = track_name
        track_name_map[str(idx)] = track_name  # Map by index string

    return track_name_map


def create_guid_mapping(project_tree: Element) -> Dict[str, str]:
    """Creates a mapping from track index to GUID."""
    guid_map: Dict[str, str] = {}
    track_elements = project_tree.findall("TRACK")

    for idx, track_element in enumerate(track_elements):
        track_guid = (
            track_element.attrib[0] if track_element.attrib else f"Track_{idx+1}"
        )
        guid_map[str(idx)] = track_guid

    return guid_map


def update_routing_automation_flags(
    routing_links: List[RoutingLink], tracks: List[Track]
) -> None:
    """Updates routing links with automation information."""
    # Create a dummy context for the extractor
    from ..infrastructure.parsing_context import ParsingContext

    context = ParsingContext(project_tree=None, file_path="unknown.rpp")

    extractor = RoutingExtractor(context)
    extractor.update_routing_automation_flags(routing_links, tracks)
