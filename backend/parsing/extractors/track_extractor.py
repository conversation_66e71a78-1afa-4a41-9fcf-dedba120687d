"""
Track extraction functionality for RPP projects.
Refactored to use BaseExtractor and ParsingContext.

This module focuses on extracting basic track properties like name, GUID,
volume, pan, mute, solo, color, and folder status.
"""

from typing import List, Tuple, Optional
from rpp import Element
from ..models import Track
from ..infrastructure.base_extractor import BaseExtractor
from ..infrastructure.parsing_context import ParsingContext

# from .track_analysis import determine_track_type # Keep for potential future use


class TrackExtractor(BaseExtractor):
    """
    Extracts basic information for all tracks in an RPP project.
    """

    def __init__(self, context: ParsingContext):
        super().__init__(context)

    def extract(self) -> List[Track]:
        """
        Extracts all tracks from the project tree stored in the parsing context.

        Returns:
            A list of Track objects.
        """
        all_tracks: List[Track] = []
        # Track elements are expected to be cached in ParsingContext
        track_elements = self.context.cache.tracks

        for i, track_element in enumerate(track_elements):
            # Use a default name in case GUID/Name extraction fails partially
            # The Track model itself has defaults, but good to be explicit if needed
            default_guid = f"{{UNKNOWN-GUID-{i}}}"
            default_name = f"Track {i + 1}"

            track = self._parse_single_track(track_element, default_guid, default_name)
            if track:  # Ensure a track object was successfully created
                all_tracks.append(track)
        return all_tracks

    def _parse_single_track(
        self, track_element: Element, default_guid: str, default_name: str
    ) -> Track:
        """
        Extracts data for a single track from its Element.
        """
        # Get track GUID from attrib list (typically the first element)
        # RPP format: <TRACK {GUID} ... >
        guid = self._get_typed_value_from_list(
            track_element.attrib, 0, str, default=default_guid
        )
        if (
            guid == default_guid and track_element.attrib
        ):  # Log if default was used but attrib existed
            self.context.add_warning(
                f"Could not parse GUID from track attrib: {track_element.attrib}, used default: {default_guid}"
            )
        elif not track_element.attrib:
            self.context.add_warning(
                f"Track element has no attributes, cannot parse GUID. Used default: {default_guid}"
            )

        # Get track name from NAME child list in RPP library structure
        name = default_name
        for child in track_element.children:
            if isinstance(child, list) and len(child) > 1 and child[0] == "NAME":
                name = child[1]
                break

        track = Track(guid=guid, name=name)

        self._extract_folder_status(track_element, track)
        self._extract_track_color(track_element, track)
        self._extract_volume_pan_settings(track_element, track)
        self._extract_record_arm_status(
            track_element, track
        )  # Added for foundational enhancements

        # track.type = determine_track_type(track_element, track.is_folder) # To be handled in core.py or analysis phase

        return track

    def _extract_folder_status(self, track_element: Element, track: Track) -> None:
        # Find ISBUS in children lists
        for child in track_element.children:
            if isinstance(child, list) and len(child) > 1 and child[0] == "ISBUS":
                isbus_value = self._get_typed_value_from_list(child, 1, int)
                if isbus_value is not None:
                    track.is_folder = isbus_value == 1
                break

        # Find FOLDERDEPTH in children lists
        for child in track_element.children:
            if isinstance(child, list) and len(child) > 1 and child[0] == "FOLDERDEPTH":
                depth = self._get_typed_value_from_list(child, 1, int)
                if depth is not None:
                    track.folder_depth = depth
                break

    def _extract_track_color(self, track_element: Element, track: Track) -> None:
        # Find PEAKCOL in children lists
        for child in track_element.children:
            if isinstance(child, list) and len(child) > 1 and child[0] == "PEAKCOL":
                color_int = self._get_typed_value_from_list(child, 1, int)
                if color_int is not None:
                    try:
                        # Convert Reaper's BGR format to RGB hex
                        b = (color_int >> 16) & 0xFF
                        g = (color_int >> 8) & 0xFF
                        r = color_int & 0xFF
                        track.color = f"{r:02X}{g:02X}{b:02X}"
                    except Exception as e:
                        self.context.add_warning(
                            f"Track {track.guid}: Error converting PEAKCOL value {color_int} to hex color: {e}"
                        )
                break

    def _extract_volume_pan_settings(
        self, track_element: Element, track: Track
    ) -> None:
        # Find VOLPAN in children lists
        for child in track_element.children:
            if isinstance(child, list) and len(child) > 1 and child[0] == "VOLPAN":
                volume = self._get_typed_value_from_list(child, 1, float)
                if volume is not None:
                    track.volume = volume

                # Pan is often the second value (index 2 of the RPP line, index 2 of list_tag)
                pan = self._get_typed_value_from_list(child, 2, float)
                if pan is not None:
                    track.pan = pan
                break

        # Find MUTESOLO in children lists
        for child in track_element.children:
            if isinstance(child, list) and len(child) > 1 and child[0] == "MUTESOLO":
                mute_str = self._get_typed_value_from_list(child, 1, str)
                if mute_str is not None:
                    track.muted = mute_str == "1"

                solo_str = self._get_typed_value_from_list(child, 2, str)
                if solo_str is not None:
                    track.soloed = solo_str == "1"
                break

    def _extract_record_arm_status(self, track_element: Element, track: Track) -> None:
        """
        Extracts the record arm status for the track.
        RPP format: RECMODE <mode> <recarm_flag> ...
        recarm_flag: 0 = not armed, 1 = armed
        """
        for child in track_element.children:
            if isinstance(child, list) and len(child) > 1 and child[0] == "RECMODE":
                # The record arm flag is typically the second value (index 1)
                recarm_flag_str = self._get_typed_value_from_list(child, 1, str)
                if recarm_flag_str is not None:
                    track.is_record_armed = recarm_flag_str == "1"
                else:
                    # If RECMODE exists but the flag is not parsable as string, log or default
                    self.context.add_warning(
                        f"Track {track.guid}: Could not parse RECMODE arm flag from {child}"
                    )
                break


# Standalone function for backward compatibility with core.py
def extract_basic_track_info(track_element: Element) -> Track:
    """Standalone function for backward compatibility."""
    from ..infrastructure.parsing_context import ParsingContext
    from ..infrastructure.rpp_tree_cache import RPPTreeCache

    # Create a minimal context for the extractor
    # Note: For single track extraction, we create a minimal project tree
    from xml.etree.ElementTree import Element as ET

    minimal_tree = ET("PROJECT")
    context = ParsingContext(project_tree=minimal_tree, file_path="unknown.rpp")

    extractor = TrackExtractor(context)
    tracks = extractor.extract()
    return tracks[0] if tracks else Track(guid="UNKNOWN", name="Unknown Track")
