import re
from typing import List, Dict, Any, Set, Optional
from dataclasses import dataclass, field
from ..models import Track, Metadata
from rpp import Element
from ..infrastructure.base_extractor import BaseExtractor
from ..infrastructure.parsing_context import ParsingContext

GENERIC_TRACK_NAMES_SET = (
    {  # Renamed to avoid conflict with a potential class attribute
        "track",
        "audio",
        "untitled",
        "midi",
        "inst",
        "aux",
        "input",
        "output",
        "bus",
        "group",
        "send",
        "return",
    }
)

GENERIC_PATTERNS_LIST = [  # Renamed
    r"^track\s*\d*$",
    r"^audio\s*\d*$",
    r"^untitled\s*\d*$",
    r"^midi\s*\d*$",
    r"^inst\s*\d*$",
    r"^aux\s*\d*$",
    r"^bus\s*\d*$",
    r"^group\s*\d*$",
    r"^send\s*\d*$",
    r"^return\s*\d*$",
]


@dataclass
class HygieneConfig:
    check_generic_names: bool = True
    check_folder_organization: bool = True
    check_duplicate_names: bool = True
    check_missing_elements: bool = True
    strict_generic_patterns: bool = True
    custom_generic_names: Set[str] = field(default_factory=set)


class SessionHygieneExtractor(BaseExtractor):
    """
    Analyzes session hygiene aspects from a REAPER project.
    Flags issues like generic track names, unorganized tracks,
    and missing session elements (markers, regions, notes).
    """

    def __init__(self, context: ParsingContext, config: Optional[HygieneConfig] = None):
        super().__init__(context)
        self.config = config or HygieneConfig()
        # Potentially load genre_rules from context if available and needed by config
        # self.genre_rules = self.context.genre_rules # Assuming context has this property

    def analyze(
        self,
        tracks: List[Track],
        metadata: Metadata,
        genre_rules: Optional[Dict[str, Any]] = None,  # Can be fetched from context too
    ) -> None:
        """
        Performs a comprehensive hygiene analysis on the session and updates
        the Track and Metadata models with relevant flags.
        Uses project_tree from self.context.
        """
        project_tree = self.context.project_tree
        # If genre_rules are consistently in context, use that:
        # active_genre_rules = genre_rules or self.genre_rules or {}
        active_genre_rules = genre_rules or {}

        if self.config.check_generic_names:
            self._check_generic_track_names(tracks, active_genre_rules)
        if self.config.check_folder_organization:
            self._check_folder_organization(tracks, active_genre_rules)
        if self.config.check_duplicate_names:
            self._check_duplicate_track_names(tracks)

        # Empty items check is still a placeholder.
        # This would require items to be passed or fetched from context.
        # self._check_empty_items(project_tree, tracks, items)
        self.context.add_warning(
            "Empty items check in SessionHygieneAnalyzer is still a placeholder."
        )

        if self.config.check_missing_elements:
            self._check_missing_session_elements(project_tree, metadata)

        self._generate_hygiene_summary(tracks, metadata)

    def analyze_session_hygiene(
        self,
        project_tree: Element,
        tracks: List[Track],
        metadata: Metadata,
        genre_rules: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Method expected by core.py for backward compatibility."""
        # Update the context's project_tree if needed
        if self.context.project_tree is None:
            self.context.project_tree = project_tree

        # Call the main analyze method
        self.analyze(tracks, metadata, genre_rules)

    def _is_generic_pattern(self, name: str) -> bool:
        normalized = name.strip().lower()
        return any(re.match(pattern, normalized) for pattern in GENERIC_PATTERNS_LIST)

    def _check_generic_track_names(
        self, tracks: List[Track], genre_rules: Dict[str, Any]
    ) -> None:
        for track in tracks:
            if genre_rules.get("allow_generic_names_for_demos", False):
                continue

            is_generic_by_set = track.name.lower() in GENERIC_TRACK_NAMES_SET
            is_generic_by_pattern = self._is_generic_pattern(track.name)
            is_generic_by_custom = (
                track.name.lower() in self.config.custom_generic_names
            )

            is_generic = False
            if self.config.strict_generic_patterns:
                is_generic = is_generic_by_pattern or is_generic_by_custom
            else:
                is_generic = (
                    is_generic_by_set or is_generic_by_pattern or is_generic_by_custom
                )

            if is_generic:
                track.has_generic_name = True
                track.is_hygiene_issue = True
                track.hygiene_details["generic_name"] = (
                    f"Track name '{track.name}' is generic."
                )

    def _is_special_track(self, track: Track) -> bool:
        special_guids = {"MASTER_TRACK"}  # Master track GUID is fixed in our model
        special_types = {"MASTER"}
        # More robust: check against actual master track object if available
        # For now, rely on name/type/guid heuristics
        special_names = {"master", "mix", "mixdown", "stereo out", "main out"}

        return (
            track.guid in special_guids
            or (
                track.type and track.type in special_types
            )  # Ensure track.type is not None
            or track.name.lower() in special_names
        )

    def _check_folder_organization(
        self, tracks: List[Track], genre_rules: Dict[str, Any]
    ) -> None:
        has_folders = any(track.is_folder for track in tracks)
        min_tracks_for_folders = genre_rules.get("min_tracks_for_folders", 0)

        if not has_folders or len(tracks) < min_tracks_for_folders:
            return

        for track in tracks:
            if self._is_special_track(track):
                continue
            if (
                not track.is_folder and not track.is_in_folder
            ):  # is_in_folder should be set by core logic
                track.not_in_folder_structure = True
                track.is_hygiene_issue = True
                track.hygiene_details["folder_organization"] = (
                    "Track is not in any folder, but folders exist in the session."
                )

    def _check_duplicate_track_names(self, tracks: List[Track]) -> None:
        name_counts: Dict[str, int] = {}
        for track in tracks:
            normalized_name = track.name.strip().lower()
            if not normalized_name:  # Skip empty names for duplicate check
                continue
            name_counts[normalized_name] = name_counts.get(normalized_name, 0) + 1

        for track in tracks:
            normalized_name = track.name.strip().lower()
            if not normalized_name:
                continue
            if name_counts[normalized_name] > 1:
                track.has_duplicate_name = True
                track.is_hygiene_issue = True
                track.hygiene_details["duplicate_name"] = (
                    f"Track name '{track.name}' is duplicated."
                )

    # def _check_empty_items(self, project_tree: Element, tracks: List[Track], items: List[Item]) -> None:
    #     """ Placeholder: Requires items to be passed or available in context. """
    #     # Actual logic would iterate items associated with each track.
    #     pass

    def _check_missing_session_elements(
        self, project_tree: Element, metadata: Metadata
    ) -> None:
        markers_exist = False
        regions_exist = False

        # RPP structure: <MARKER_LIST ...> <MARKER ...> </MARKER_LIST> or just <MARKER ...>
        # Using _find_element which looks for the first occurrence.
        # Need to check if any MARKER or REGION tag exists at the root level or within specific parent tags.
        # The original code iterated project.children and checked child.name.
        # This implies MARKER/REGION are direct children of project_tree if they are lists.
        # If they are Elements, then self._find_element(project_tree, "MARKER") would work.
        # Let's assume they can be found as direct children elements for now.

        # Check for markers and regions in project_tree children
        for child_element_or_list in project_tree.children:
            tag_name = None
            if isinstance(child_element_or_list, Element) and hasattr(
                child_element_or_list, "tag"
            ):
                tag_name = child_element_or_list.tag
            elif isinstance(child_element_or_list, list) and child_element_or_list:
                tag_name = child_element_or_list[0]  # Assuming ['TAGNAME', ...] format

            if tag_name == "MARKER":
                markers_exist = True
            elif tag_name in ["REGION", "REGION_LIST"]:
                regions_exist = True
            if markers_exist and regions_exist:
                break

        metadata.has_missing_markers = not markers_exist
        metadata.has_missing_regions = not regions_exist
        metadata.has_missing_notes = not (metadata.notes and metadata.notes.strip())

    def _generate_hygiene_summary(
        self, tracks: List[Track], metadata: Metadata
    ) -> None:
        summary = {
            "total_tracks": len(tracks),
            "tracks_with_generic_names": sum(1 for t in tracks if t.has_generic_name),
            "tracks_with_duplicate_names": sum(
                1 for t in tracks if t.has_duplicate_name
            ),
            "tracks_not_in_folders": sum(
                1 for t in tracks if t.not_in_folder_structure
            ),
            "tracks_with_empty_items": sum(
                1 for t in tracks if getattr(t, "has_empty_items", False)
            ),  # Safely access
            "missing_markers": metadata.has_missing_markers,
            "missing_regions": metadata.has_missing_regions,
            "missing_notes": metadata.has_missing_notes,
        }
        metadata.hygiene_summary = summary

    def extract(self) -> Any:
        """
        BaseExtractor abstract method. This analyzer is typically called with analyze().
        """
        self.context.add_warning(
            "SessionHygieneAnalyzer.extract() called directly. Use analyze(tracks, metadata) method."
        )
        # Return a default or empty analysis structure
        return {"hygiene_summary": "Analysis not run via direct extract() call."}
