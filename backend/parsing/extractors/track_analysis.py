"""
Track type analysis functionality.

This module provides functions to analyze and determine track types based on content.
"""

from typing import List, Optional, Tuple
from rpp import Element


def determine_track_type(track_element: Element, is_folder: bool) -> str:
    """
    Determine a track's type based on its contents.
    
    Args:
        track_element: The track Element to analyze
        is_folder: Whether the track is a folder
        
    Returns:
        String identifying the track type (AUDIO, MIDI, BUS, AUX)
    """
    # Folder tracks in REAPER (often called "buses") are track groups
    if is_folder:
        return "BUS"
    
    # Check items to determine if the track contains audio or MIDI
    items = track_element.findall("ITEM")
    has_audio_items = analyze_items_for_audio(items)
    
    # If audio was found, return AUDIO type (audio takes precedence)
    if has_audio_items:
        return "AUDIO"
    
    # Check if any of the items have MIDI content
    has_midi_items = analyze_items_for_midi(items)
    if has_midi_items:
        return "MIDI"
    
    # Check if this is a send/aux track (no items but has routing)
    if len(items) == 0 and (
            track_element.find("AUXRECV") is not None or track_element.find("SEND") is not None
    ):
        return "AUX"
    
    # Fallback for tracks with MIDI connections but no items
    if track_element.find("MIDIIN") is not None or track_element.find("MIDIOUT") is not None:
        return "MIDI"
    
    # Default fallback - audio is more common than MIDI
    return "AUDIO"


def analyze_items_for_audio(items: List) -> bool:
    """
    Analyze track items to check if any contain audio content.
    
    Args:
        items: List of ITEM elements to analyze
        
    Returns:
        True if any items contain audio, False otherwise
    """
    for item in items:
        # Method 1: Look for SOURCE WAVE as direct children of ITEM
        if check_direct_source_wave(item):
            return True
        
        # Method 2: Look for SOURCE elements with WAVE children
        if check_source_elements_for_wave(item):
            return True
        
        # Method 3: Look inside SOURCE SECTION
        if check_source_section_for_wave(item):
            return True
        
        # Method 4: Check for audio file references
        if check_for_audio_file_references(item):
            return True
    
    return False


def check_direct_source_wave(item: Element) -> bool:
    """
    Check if an item has direct SOURCE WAVE children.
    
    Args:
        item: ITEM element to check
        
    Returns:
        True if a direct SOURCE WAVE tag is found
    """
    for child in item.children:
        if isinstance(child, list) and len(child) > 0:
            if child[0] == "SOURCE" and len(child) > 1 and child[1] == "WAVE":
                return True
    return False


def check_source_elements_for_wave(item: Element) -> bool:
    """
    Check if an item has SOURCE elements with WAVE children.
    
    Args:
        item: ITEM element to check
        
    Returns:
        True if a SOURCE with WAVE is found
    """
    for child in item.children:
        if isinstance(child, Element) and child.tag == "SOURCE":
            # Check for WAVE child
            for wave_child in child.children:
                if isinstance(wave_child, Element) and wave_child.tag == "WAVE":
                    return True
                elif isinstance(wave_child, list) and len(wave_child) > 0 and wave_child[0] == "WAVE":
                    return True
    return False


def check_source_section_for_wave(item: Element) -> bool:
    """
    Check if an item has SOURCE SECTION containing WAVE data.
    
    Args:
        item: ITEM element to check
        
    Returns:
        True if a SOURCE SECTION with WAVE is found
    """
    section = None
    for child in item.children:
        if isinstance(child, Element) and child.tag == "SOURCE SECTION":
            section = child
            break
        elif isinstance(child, list) and len(child) > 0 and child[0] == "SOURCE SECTION":
            section = child
            break
    
    if not section or not isinstance(section, Element):
        return False
    
    # Check SOURCE elements inside the section
    for src in section.children:
        if not isinstance(src, Element) or src.tag != "SOURCE":
            continue
            
        for wave_child in src.children:
            if isinstance(wave_child, Element) and wave_child.tag == "WAVE":
                return True
            elif isinstance(wave_child, list) and len(wave_child) > 0 and wave_child[0] == "WAVE":
                return True
    
    return False


def check_for_audio_file_references(item: Element) -> bool:
    """
    Check if an item has FILE references to audio files.
    
    Args:
        item: ITEM element to check
        
    Returns:
        True if a reference to an audio file is found
    """
    has_audio_file = [False]  # Using a list to allow modification in the nested function
    
    def find_audio_files(element, depth=0):
        if has_audio_file[0]:
            return  # Already found, no need to continue
        
        # Check if this is a list with a FILE reference
        if isinstance(element, list) and len(element) > 0:
            if element[0] == "FILE" and len(element) > 1:
                file_path = element[1]
                # Check if it's an audio file based on extension
                if isinstance(file_path, str) and file_path.lower().endswith(
                        (".wav", ".mp3", ".aif", ".aiff", ".flac", ".ogg", ".m4a", ".wma")
                ):
                    has_audio_file[0] = True
                    return
        
        # Check children if it's an Element
        if isinstance(element, Element):
            for child in element.children:
                find_audio_files(child, depth + 1)
    
    # Start the recursive search
    find_audio_files(item)
    
    return has_audio_file[0]


def analyze_items_for_midi(items: List) -> bool:
    """
    Analyze track items to check if any contain MIDI content.
    
    Args:
        items: List of ITEM elements to analyze
        
    Returns:
        True if any items contain MIDI, False otherwise
    """
    for item in items:
        # Check for direct SOURCE MIDI
        for child in item.children:
            if isinstance(child, list) and len(child) > 0:
                if child[0] == "SOURCE" and len(child) > 1 and child[1] == "MIDI":
                    return True
        
        # Check SOURCE elements for MIDI
        for child in item.children:
            if isinstance(child, Element) and child.tag == "SOURCE":
                for midi_child in child.children:
                    if isinstance(midi_child, Element) and midi_child.tag == "MIDI":
                        return True
                    elif isinstance(midi_child, list) and len(midi_child) > 0 and midi_child[0] == "MIDI":
                        return True
        
        # Check SOURCE SECTION for MIDI
        section = None
        for child in item.children:
            if isinstance(child, Element) and child.tag == "SOURCE SECTION":
                section = child
                break
            elif isinstance(child, list) and len(child) > 0 and child[0] == "SOURCE SECTION":
                section = child
                break
        
        if section and isinstance(section, Element):
            for src in section.children:
                if isinstance(src, Element) and src.tag == "SOURCE":
                    for midi_child in src.children:
                        if isinstance(midi_child, Element) and midi_child.tag == "MIDI":
                            return True
                        elif isinstance(midi_child, list) and len(midi_child) > 0 and midi_child[0] == "MIDI":
                            return True
    
    return False
