"""
Master track extraction functionality for RPP projects.
Refactored to use BaseExtractor and ParsingContext.
"""

from typing import List, Optional
from rpp import Element
from ..models import (
    MasterTrack,
)  # FX model not directly used here, engine returns dicts
from ..infrastructure.base_extractor import BaseExtractor
from ..infrastructure.parsing_context import ParsingContext
from ..engines.plugin_analysis_engine import PluginAnalysisEngine  # Corrected


class MasterTrackExtractor(BaseExtractor):
    """
    Extracts Master track information from root-level elements of the RPP file.
    """

    def __init__(self, context: ParsingContext):
        super().__init__(context)

    def extract(
        self, plugin_engine: PluginAnalysisEngine
    ) -> MasterTrack:  # Added plugin_engine parameter
        """
        Extracts Master track information using the parsing context and plugin engine.
        In REAPER, the Master track is not a regular TRACK element but is represented
        by dedicated MASTER_* elements at the root level of the project_tree.

        Returns:
            MasterTrack object with extracted properties.
        """
        project_tree = self.context.project_tree
        master_track = MasterTrack(
            guid="MASTER_TRACK", name="Master"
        )  # Name is set by default in model too

        self._extract_master_properties(project_tree, master_track)

        # Find MASTERFXLIST in children lists
        masterfx_element = None
        for child in project_tree.children:
            if (
                isinstance(child, Element)
                and hasattr(child, "tag")
                and child.tag == "MASTERFXLIST"
            ):
                masterfx_element = child
                break

        if masterfx_element:
            master_track.fx = plugin_engine.analyze_fx_chain(
                fxchain_parent_element=masterfx_element,  # MASTERFXLIST is the parent of FX tags
                track_guid=master_track.guid,
                track_name=master_track.name,
                is_master_bus=True,
            )

            # Check for chain-level oversampling for the master track
            # This is <FX_OVERSAMPLE> directly under <MASTERFXLIST>
            master_track.chain_oversampling_rate = 0  # Default

            # Find FX_OVERSAMPLE in masterfx_element children
            for child in masterfx_element.children:
                if (
                    isinstance(child, list)
                    and len(child) > 1
                    and child[0] == "FX_OVERSAMPLE"
                ):
                    rate_val = self._get_typed_value_from_list(child, 1, int)
                    if rate_val is not None:
                        master_track.chain_oversampling_rate = rate_val
                    break
                elif (
                    isinstance(child, Element)
                    and hasattr(child, "tag")
                    and child.tag == "FX_OVERSAMPLE"
                    and child.text
                ):
                    try:
                        master_track.chain_oversampling_rate = int(
                            child.text.split()[0]
                        )
                    except (ValueError, IndexError, AttributeError):
                        self.context.add_warning(
                            f"Could not parse FX_OVERSAMPLE value for master track from '{child.text}'"
                        )
                    break
        else:
            master_track.fx = []  # Ensure fx is initialized

        return master_track

    def _extract_master_properties(
        self, project_tree: Element, master_track: MasterTrack
    ) -> None:
        """
        Extracts basic properties for the master track (volume, pan, mute, solo, color).
        These are typically root-level elements in the RPP project_tree.
        """
        # Find MASTER_VOLUME in children lists
        for child in project_tree.children:
            if (
                isinstance(child, list)
                and len(child) > 1
                and child[0] == "MASTER_VOLUME"
            ):
                volume = self._get_typed_value_from_list(child, 1, float)
                if volume is not None:
                    master_track.volume = volume
                break

        # Find MASTERMUTESOLO in children lists
        for child in project_tree.children:
            if (
                isinstance(child, list)
                and len(child) > 1
                and child[0] == "MASTERMUTESOLO"
            ):
                mute_str = self._get_typed_value_from_list(child, 1, str)
                if mute_str is not None:
                    master_track.muted = mute_str == "1"

                solo_str = self._get_typed_value_from_list(child, 2, str)
                if solo_str is not None:
                    master_track.soloed = solo_str == "1"
                break

        # Find MASTERPEAKCOL in children lists
        for child in project_tree.children:
            if (
                isinstance(child, list)
                and len(child) > 1
                and child[0] == "MASTERPEAKCOL"
            ):
                color_int = self._get_typed_value_from_list(child, 1, int)
                if color_int is not None:
                    try:
                        b = (color_int >> 16) & 0xFF
                        g = (color_int >> 8) & 0xFF
                        r = color_int & 0xFF
                        master_track.color = f"{r:02X}{g:02X}{b:02X}"
                    except Exception as e:
                        self.context.add_warning(
                            f"Error converting MASTERPEAKCOL value {color_int} to hex color: {e}"
                        )
                break
