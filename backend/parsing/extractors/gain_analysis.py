from typing import List, Dict, Any
import math
from ..models import Track, Item, Take


def analyze_track_gain_staging(track: Track, genre_rules: Dict[str, Any]) -> None:
    """Analyze gain staging for a complete track including all items."""
    if not genre_rules:
        return

    # Get thresholds from genre rules
    max_item_gain_db = genre_rules.get("gain_staging_item_max_db", 6.0)
    min_item_gain_db = genre_rules.get("gain_staging_item_min_db", -20.0)
    max_track_peak_db = genre_rules.get("gain_staging_track_max_peak_db", -6.0)

    # Analyze each item in the track
    for item in track.items:
        analyze_item_gain_staging(item, genre_rules)

    # Analyze track-level gain staging
    track_volume_db = (
        20 * math.log10(track.volume) if track.volume > 0 else -float("inf")
    )

    # Check if track volume is too hot
    if track_volume_db > max_track_peak_db:
        track.gain_staging_warning = f"Track volume too hot: {track_volume_db:.1f}dB (max: {max_track_peak_db}dB)"
    elif track_volume_db < min_item_gain_db:
        track.gain_staging_warning = f"Track volume very low: {track_volume_db:.1f}dB"


def analyze_item_gain_staging(item: Item, genre_rules: Dict[str, Any]) -> None:
    """Analyze gain staging for an item based on genre rules."""
    if not genre_rules:
        return

    max_item_gain_db = genre_rules.get("gain_staging_item_max_db", 6.0)
    min_item_gain_db = genre_rules.get("gain_staging_item_min_db", -20.0)

    # Convert item volume to dB
    item_volume_db = 20 * math.log10(item.volume) if item.volume > 0 else -float("inf")

    # Check if item volume is too hot
    if item_volume_db > max_item_gain_db:
        item.gain_staging_warning = (
            f"Item too hot: {item_volume_db:.1f}dB (max: {max_item_gain_db}dB)"
        )
    elif item_volume_db < min_item_gain_db:
        item.gain_staging_warning = f"Item very low: {item_volume_db:.1f}dB"

    # Analyze takes within the item
    for take in item.takes:
        take_volume_db = (
            20 * math.log10(take.volume) if take.volume > 0 else -float("inf")
        )
        take.volume_db = take_volume_db

        if take_volume_db > max_item_gain_db:
            take.gain_staging_flag = "too_hot"
        elif take_volume_db < min_item_gain_db:
            take.gain_staging_flag = "too_low"
        else:
            take.gain_staging_flag = "ok"


def calculate_cumulative_gain(track: Track) -> float:
    """Calculate cumulative gain through track volume + item volumes."""
    track_gain_db = 20 * math.log10(track.volume) if track.volume > 0 else -float("inf")

    # Find the loudest item
    max_item_gain_db = -float("inf")
    for item in track.items:
        item_gain_db = (
            20 * math.log10(item.volume) if item.volume > 0 else -float("inf")
        )
        max_item_gain_db = max(max_item_gain_db, item_gain_db)

    # Return cumulative gain (track + loudest item)
    if max_item_gain_db == -float("inf"):
        return track_gain_db
    else:
        return track_gain_db + max_item_gain_db


def generate_gain_recommendations(track: Track, genre: str) -> List[str]:
    """Generate actionable gain staging recommendations."""
    recommendations = []

    if track.gain_staging_warning:
        recommendations.append(f"Track '{track.name}': {track.gain_staging_warning}")

    for item in track.items:
        if item.gain_staging_warning:
            recommendations.append(f"Item '{item.name}': {item.gain_staging_warning}")

    # Add genre-specific recommendations
    if genre == "acoustic" or genre == "jazz_classical":
        recommendations.append(
            f"For {genre} music, consider maintaining more dynamic range"
        )
    elif genre == "electronic":
        recommendations.append(
            f"For {genre} music, moderate compression and limiting is acceptable"
        )

    return recommendations
