"""
Package for RPP structure extraction components.

This package breaks down the extraction logic into smaller, more focused modules.
"""

from .metadata_extractor import MetadataExtractor
from .master_track_extractor import MasterTrackExtractor

# Removed: from .track_extractor import extract_tracks
from .routing_extractor import extract_routing
from .track_automation_extractor import extract_track_automation

# Removed: from .plugin_automation_extractor import extract_plugin_automation

__all__ = [
    "MetadataExtractor",
    "MasterTrackExtractor",
    # Removed: "extract_tracks",
    "extract_routing",
    "extract_track_automation",
    # Removed: "extract_plugin_automation"
]
