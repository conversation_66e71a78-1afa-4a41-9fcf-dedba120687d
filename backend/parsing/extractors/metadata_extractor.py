"""
Metadata extraction functionality for RPP projects.
Refactored to use BaseExtractor and ParsingContext.
"""

from typing import Optional
from rpp import Element
from ..models import Metadata
from ..infrastructure.base_extractor import BaseExtractor
from ..infrastructure.parsing_context import ParsingContext


class MetadataExtractor(BaseExtractor):
    """
    Extracts project-level metadata from the RPP project tree.
    """

    def __init__(self, context: ParsingContext):
        super().__init__(context)

    def extract(self) -> Metadata:
        """
        Extracts project-level metadata using the parsing context.

        Returns:
            Metadata object containing extracted project information.
        """
        project_tree = self.context.project_tree
        metadata = Metadata(filename=self.context.file_path)

        # Extract Reaper version and timestamp from project_tree.attrib (which is a list of strings)
        # Example: RPR_PROJECT "" 6.80 "" "" 1678886400
        # attrib[0] is often empty or project specific, version is often at index 1 or 2, timestamp later
        # The original code checked len > 1 for version and len > 2 for timestamp.
        # Let's assume attrib is like: [project_specific_tag, reaper_version_str, save_timestamp_str, ...]
        # Or it might be just [reaper_version_str, save_timestamp_str] if the first element is the tag itself.
        # The original code implies project_tree.tag is "RPR_PROJECT" and attrib is its parameters.

        # Based on original logic: project_tree.attrib[1] for version, project_tree.attrib[2] for timestamp
        # This needs to be robust if attrib structure varies.
        # For now, replicating original logic with safer access.
        if project_tree.attrib:
            metadata.reaper_version = self._get_typed_value_from_list(
                project_tree.attrib, 1, str, default="Unknown"
            )
            metadata.save_timestamp = self._get_typed_value_from_list(
                project_tree.attrib,
                2,
                str,
                default="Unknown",  # Timestamps are often large numbers (seconds since epoch)
            )
            # If timestamp is numeric, it might be better to parse as int/float
            # For now, keeping as string as per original model and logic.

        # Extract title, notes, sample rate, etc., from children
        # Children are expected to be lists like ['KEY', 'VALUE1', 'VALUE2', ...]
        for child_list in project_tree.children:
            if not isinstance(child_list, list) or not child_list:
                continue

            key = child_list[0]

            if key == "TITLE":
                metadata.title = self._get_typed_value_from_list(child_list, 1, str)
            elif key == "NOTES":
                metadata.notes = self._get_typed_value_from_list(child_list, 1, str)
            elif key == "SAMPLERATE":
                metadata.sample_rate = self._get_typed_value_from_list(
                    child_list, 1, int, default=metadata.sample_rate
                )
            elif key == "BITDEPTH":
                metadata.project_bit_depth = self._get_typed_value_from_list(
                    child_list, 1, int, default=metadata.project_bit_depth
                )
            elif key == "TEMPO":
                tempo_val = self._get_typed_value_from_list(child_list, 1, float)
                ts_num_val = self._get_typed_value_from_list(
                    child_list, 2, str
                )  # Keep as str for f-string
                ts_den_val = self._get_typed_value_from_list(
                    child_list, 3, str
                )  # Keep as str for f-string

                if tempo_val is not None:
                    metadata.tempo = tempo_val
                if ts_num_val is not None and ts_den_val is not None:
                    metadata.time_signature = f"{ts_num_val}/{ts_den_val}"
                elif tempo_val is not None and (
                    ts_num_val is None or ts_den_val is None
                ):
                    self.context.add_warning(
                        f"TEMPO entry found but time signature parts are incomplete: {child_list}"
                    )

        # REAPER projects always have a master track
        metadata.has_master_track = True

        return metadata


# Example usage (for testing, not part of the module's final code):
# if __name__ == '__main__':
#     from rpp.rpp import loads
#     # Create a dummy RPP content string
#     dummy_rpp_content = """<REAPER_PROJECT "" 6.80 "" 1700000000
#   SAMPLERATE 48000 0 0
#   BITDEPTH 24
#   TITLE "My Awesome Song"
#   TEMPO 120.0 4 4
#   NOTES "This is a test project."
# >
# """
#     # The rpp library expects the root tag to be enclosed in < >
#     # and children to be indented. The loads function might handle this.
#     # The actual RPP file has a more complex structure.
#     # This is a simplified representation for the metadata part.
#
#     # A more realistic top-level structure for project_tree.attrib:
#     # project_tree = loads(dummy_rpp_content) # This is how project_tree is obtained
#     # project_tree.attrib would be ["", "6.80", "", "1700000000"] if the tag is REAPER_PROJECT
#     # For the example above, if loads() makes REAPER_PROJECT the root tag, then its attrib is ["", "6.80", "", "1700000000"]
#     # And its children are lists like ['SAMPLERATE', '48000', '0', '0']
#
#     # Simplified mock Element for testing the extractor logic directly
#     class MockElement:
#         def __init__(self, tag, attrib=None, children=None, text=None):
#             self.tag = tag
#             self.attrib = attrib if attrib is not None else []
#             self.children = children if children is not None else []
#             self.text = text
#         def find(self, tag_name): # Not used by this extractor directly on project_tree
#             return None
#         def findall(self, tag_name): # Not used by this extractor directly on project_tree
#             return []
#
#     mock_project_tree = MockElement(
#         tag="REAPER_PROJECT",
#         attrib=["", "6.80", "1700000000"], # Simplified: version, timestamp
#         children=[
#             ["SAMPLERATE", "48000", "0", "0"],
#             ["BITDEPTH", "24"],
#             ["TITLE", "My Test Song"],
#             ["TEMPO", "125.0", "3", "4"],
#             ["NOTES", "Some notes here."]
#         ]
#     )
#
#     mock_context = ParsingContext(project_tree=mock_project_tree, file_path="test.RPP")
#     extractor = MetadataExtractor(mock_context)
#     extracted_meta = extractor.extract()
#
#     print(f"Filename: {extracted_meta.filename}")
#     print(f"Reaper Version: {extracted_meta.reaper_version}")
#     print(f"Save Timestamp: {extracted_meta.save_timestamp}")
#     print(f"Title: {extracted_meta.title}")
#     print(f"Notes: {extracted_meta.notes}")
#     print(f"Sample Rate: {extracted_meta.sample_rate}")
#     print(f"Bit Depth: {extracted_meta.project_bit_depth}")
#     print(f"Tempo: {extracted_meta.tempo}")
#     print(f"Time Signature: {extracted_meta.time_signature}")
#     print(f"Has Master Track: {extracted_meta.has_master_track}")
#     print(f"Warnings: {mock_context.warnings}")
#     print(f"Errors: {mock_context.errors}")
