"""
Common utilities for automation lane and point extraction.

These functions are designed to handle potential inconsistencies in the RPP
data structure, where elements might be represented as rpp.Element objects
or as simple lists.
"""

from typing import List, Optional, Union, Any, Type, TypeVar
from uuid import uuid4
from rpp import Element
from ..models import AutomationLane, AutomationPoint

T = TypeVar('T') # Generic type variable for safe conversion

# --- Helper Functions ---

def safe_find_item(element: Union[Element, list], tag_name: str) -> Optional[list]:
    """Safely find the first item matching tag_name, handling Element or list."""
    if isinstance(element, Element):
        found = element.find(tag_name)
        # Ensure found item is a list-like structure for consistency
        return found if isinstance(found, list) else None
    elif isinstance(element, list):
        for item in element:
            if isinstance(item, list) and len(item) > 0 and item[0] == tag_name:
                return item
    return None

def safe_findall_items(element: Union[Element, list], tag_name: str) -> List[list]:
    """Safely find all items matching tag_name, handling Element or list."""
    items_found = []
    if isinstance(element, Element):
        found = element.findall(tag_name)
        # Ensure all found items are list-like
        items_found.extend(item for item in found if isinstance(item, list))
    elif isinstance(element, list):
        for item in element:
            if isinstance(item, list) and len(item) > 0 and item[0] == tag_name:
                items_found.append(item)
    return items_found

def safe_get_value(item_list: Optional[list], index: int, expected_type: Type[T], default: Optional[T] = None) -> Optional[T]:
    """Safely get and convert value from list index, handling errors."""
    if item_list is None or not isinstance(item_list, list) or index >= len(item_list):
        return default
    try:
        return expected_type(item_list[index])
    except (ValueError, TypeError, IndexError):
        return default

# --- Public Utility Functions ---

def extract_automation_attributes(env_element: Union[Element, list], lane: AutomationLane) -> None:
    """
    Extract common automation lane attributes from an envelope element.
    
    Args:
        env_element: The envelope Element to process
        lane: The AutomationLane object to update
    """
    # Use helper functions to safely extract attributes
    act_item = safe_find_item(env_element, "ACT")
    lane.is_active = safe_get_value(act_item, 1, str) == "1"

    vis_item = safe_find_item(env_element, "VIS")
    lane.is_visible = safe_get_value(vis_item, 1, str) == "1"

    arm_item = safe_find_item(env_element, "ARM")
    lane.is_armed = safe_get_value(arm_item, 1, str) == "1"

    lane_height_item = safe_find_item(env_element, "LANEHEIGHT")
    lane.lane_height = safe_get_value(lane_height_item, 1, int, default=lane.lane_height) # Keep default if extraction fails

    defshape_item = safe_find_item(env_element, "DEFSHAPE")
    lane.default_shape = safe_get_value(defshape_item, 1, int, default=lane.default_shape) # Keep default


def extract_envelope_guid(env_element: Union[Element, list]) -> str:
    """
    Extract GUID from an envelope element or generate one if not present.
    Handles both Element and list representations.
    
    Args:
        env_element: The envelope Element or list to process
        
    Returns:
        The extracted or generated GUID
    """
    guid_item = safe_find_item(env_element, "EGUID")
    guid = safe_get_value(guid_item, 1, str)
    return guid if guid else str(uuid4())


def extract_automation_points(env_element: Union[Element, list]) -> List[AutomationPoint]:
    """
    Extract automation points from an envelope element.
    Handles both Element and list representations.
    
    Args:
        env_element: The envelope Element or list to process
        
    Returns:
        List of AutomationPoint objects
    """
    points = []
    
    # Use helper to find all point items ("PT")
    pt_items = safe_findall_items(env_element, "PT")

    for pt_item in pt_items:
        # Use helper to safely extract values
        time = safe_get_value(pt_item, 1, float)
        value = safe_get_value(pt_item, 2, float)
        
        # Skip if essential time/value are missing
        if time is None or value is None:
            continue
            
        shape = safe_get_value(pt_item, 3, int, default=0)
        # Tension is typically at index 7 based on RPP format observation
        tension = safe_get_value(pt_item, 7, float) 

        point = AutomationPoint(time=time, value=value, shape=shape, tension=tension)
        points.append(point)
            
    return points


def calculate_effective_points(points: List[AutomationPoint]) -> int:
    """
    Calculate the number of effective points in an automation lane.
    The first point is often a default point at time=0, so effective points are any additional points.
    
    Args:
        points: List of AutomationPoint objects
        
    Returns:
        Number of effective points
    """
    return max(0, len(points) - 1)
