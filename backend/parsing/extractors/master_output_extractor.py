"""
Master output analysis extractor for RPP projects.
Extracts master volume, mute state, and output configuration for mastering readiness analysis.
"""

from typing import Optional, Dict, Any
from rpp import Element
from ..models import MasterOutputInfo
from ..infrastructure.base_extractor import BaseExtractor
from ..infrastructure.parsing_context import ParsingContext


class MasterOutputExtractor(BaseExtractor):
    """
    Extracts master output configuration and validates it for mastering readiness.
    """

    def __init__(self, context: ParsingContext):
        super().__init__(context)

    def extract(self) -> MasterOutputInfo:
        """
        Extracts master output information from the project.
        
        Returns:
            MasterOutputInfo object containing master output configuration.
        """
        project_tree = self.context.project_tree
        
        # Initialize with safe defaults
        master_output = MasterOutputInfo(
            master_volume=0.0,  # 0dB default
            master_mute=False,
            master_solo=False,
            has_excessive_gain=False,
            has_insufficient_headroom=False,
            volume_automation_present=False
        )
        
        # Look for master track configuration
        master_track = self._find_master_track()
        if master_track:
            master_output = self._extract_master_track_info(master_track, master_output)
        
        # Look for project-level master settings
        self._extract_project_master_settings(project_tree, master_output)
        
        # Validate master output for mastering readiness
        self._validate_master_output(master_output)
        
        return master_output

    def _find_master_track(self) -> Optional[Element]:
        """Find the master track element in the project."""
        tracks = self.context.cache.tracks
        
        for track in tracks:
            # Master track is typically the first track or has specific attributes
            if track.attrib and len(track.attrib) > 0:
                # Check if this is a master track (usually has no GUID or specific flags)
                track_children = list(track.children) if hasattr(track, 'children') else list(track)
                
                for child in track_children:
                    if isinstance(child, list) and child:
                        if child[0] == "MAINSEND" and len(child) > 1 and child[1] == "1":
                            # This indicates it's the master track
                            return track
                        elif child[0] == "TRACKID" and len(child) > 1 and child[1] == "{00000000-0000-0000-0000-000000000000}":
                            # Alternative master track identification
                            return track
        
        # If no explicit master track found, assume first track is master
        if tracks:
            return tracks[0]
        
        return None

    def _extract_master_track_info(self, master_track: Element, master_output: MasterOutputInfo) -> MasterOutputInfo:
        """Extract master track specific information."""
        track_children = list(master_track.children) if hasattr(master_track, 'children') else list(master_track)
        
        for child in track_children:
            if not isinstance(child, list) or not child:
                continue
                
            key = child[0]
            
            if key == "VOLPAN":
                # Extract volume and pan information
                volume = self._get_typed_value_from_list(child, 1, float)
                if volume is not None:
                    master_output.master_volume = volume
                    
            elif key == "MUTESOLO":
                # Extract mute/solo states
                mute_flag = self._get_typed_value_from_list(child, 1, int)
                solo_flag = self._get_typed_value_from_list(child, 2, int)
                if mute_flag is not None:
                    master_output.master_mute = bool(mute_flag)
                if solo_flag is not None:
                    master_output.master_solo = bool(solo_flag)
                    
            elif key == "VOLENV":
                # Volume automation envelope present
                master_output.volume_automation_present = True
                
        return master_output

    def _extract_project_master_settings(self, project_tree: Element, master_output: MasterOutputInfo) -> None:
        """Extract project-level master settings."""
        project_children = list(project_tree.children) if hasattr(project_tree, 'children') else list(project_tree)
        
        for child in project_children:
            if not isinstance(child, list) or not child:
                continue
                
            key = child[0]
            
            if key == "MASTER_VOLUME":
                volume = self._get_typed_value_from_list(child, 1, float)
                if volume is not None:
                    master_output.master_volume = volume
                    
            elif key == "MASTER_MUTE":
                mute_flag = self._get_typed_value_from_list(child, 1, int)
                if mute_flag is not None:
                    master_output.master_mute = bool(mute_flag)
                    
            elif key == "MASTER_SOLO":
                solo_flag = self._get_typed_value_from_list(child, 1, int)
                if solo_flag is not None:
                    master_output.master_solo = bool(solo_flag)

    def _validate_master_output(self, master_output: MasterOutputInfo) -> None:
        """Validate master output configuration for mastering readiness."""
        # Check for excessive gain (> +3dB is potentially problematic)
        if master_output.master_volume > 3.0:
            master_output.has_excessive_gain = True
            self.context.add_warning(
                f"Master volume is set to +{master_output.master_volume:.1f}dB, which may cause clipping"
            )
        
        # Check for insufficient headroom (negative values can be acceptable, but very negative might indicate issues)
        if master_output.master_volume < -12.0:
            master_output.has_insufficient_headroom = True
            self.context.add_warning(
                f"Master volume is set to {master_output.master_volume:.1f}dB, which may indicate gain staging issues"
            )
        
        # Check for muted master (critical issue)
        if master_output.master_mute:
            self.context.add_error("Master track is muted - this will result in no audio output")
        
        # Check for volume automation (informational)
        if master_output.volume_automation_present:
            self.context.add_warning("Master track has volume automation - ensure this is intentional for mastering")