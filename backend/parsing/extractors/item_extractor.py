from typing import List, Optional, Dict, Any
from rpp import Element
from ..models import Item, Take, Source
from ..infrastructure.base_extractor import BaseExtractor
from ..infrastructure.parsing_context import ParsingContext


class ItemExtractor(BaseExtractor):
    """
    Extracts items and their takes from track elements.
    """

    def __init__(self, context: ParsingContext):
        super().__init__(context)

    def extract(self) -> List[Item]:
        """Extract all items from all tracks in the project."""
        all_items: List[Item] = []

        # Get all tracks from the cache
        track_elements = self.context.cache.tracks

        for i, track_element in enumerate(track_elements):
            track_guid = (
                track_element.attrib[0] if track_element.attrib else f"Track_{i+1}"
            )
            track_items = self.extract_items_for_track(track_element, track_guid)
            all_items.extend(track_items)

        return all_items

    def extract_items_for_track(
        self, track_element: Element, track_guid: str
    ) -> List[Item]:
        """
        Extract all items from a given track element.
        `track_guid` is used for more informative warnings/errors.
        """
        items: List[Item] = []
        item_elements = self._find_all_elements(track_element, "ITEM")

        for i, item_element in enumerate(item_elements):
            default_item_guid = f"{{DEFAULT-ITEM-GUID-{track_guid}-{i}}}"
            item_guid = default_item_guid
            item_name = "Unnamed Item"
            position = 0.0
            length = 0.0
            volume = 1.0  # Default volume (0dB)
            pan = 0.0  # Default pan (center)

            # Item properties are often direct children lists like ['POSITION', '0.5']
            for child_list in item_element:  # Iterate through children of <ITEM>
                if not isinstance(child_list, list) or len(child_list) < 1:
                    continue

                key = child_list[0]

                if key == "GUID":
                    item_guid = self._get_typed_value_from_list(
                        child_list, 1, str, default=item_guid
                    )
                elif key == "NAME":
                    item_name = self._get_typed_value_from_list(
                        child_list, 1, str, default=item_name
                    )
                elif key == "POSITION":
                    pos_val = self._get_typed_value_from_list(child_list, 1, float)
                    if pos_val is not None:
                        position = pos_val
                elif key == "LENGTH":
                    len_val = self._get_typed_value_from_list(child_list, 1, float)
                    if len_val is not None:
                        length = len_val
                elif key == "VOLPAN":
                    vol_val = self._get_typed_value_from_list(child_list, 1, float)
                    if vol_val is not None:
                        volume = vol_val
                    pan_val = self._get_typed_value_from_list(
                        child_list, 2, float
                    )  # Pan is often index 2
                    if pan_val is not None:
                        pan = pan_val
                elif key == "FADEIN":
                    fade_in_shape = self._get_typed_value_from_list(child_list, 1, int)
                    fade_in_length = self._get_typed_value_from_list(
                        child_list, 2, float
                    )
                elif key == "FADEOUT":
                    fade_out_shape = self._get_typed_value_from_list(child_list, 1, int)
                    fade_out_length = self._get_typed_value_from_list(
                        child_list, 2, float
                    )
                elif key == "PLAYRATE":
                    playrate_val = self._get_typed_value_from_list(child_list, 1, float)
                    # preserve_pitch_flag = self._get_typed_value_from_list(child_list, 2, int) # Not currently stored
                elif key == "REVERSE":
                    reverse_flag_str = self._get_typed_value_from_list(
                        child_list, 1, str
                    )

            item = Item(
                guid=item_guid,
                name=item_name,
                position=position,
                length=length,
                volume=volume,
                pan=pan,
            )

            # Assign parsed fade, playrate, reverse values
            # These need to be initialized before assignment if not found in loop
            # However, Item model has defaults, so direct assignment is fine if values were parsed.
            if "fade_in_shape" in locals() and fade_in_shape is not None:
                item.fade_in_shape = fade_in_shape
            if "fade_in_length" in locals() and fade_in_length is not None:
                item.fade_in_length = fade_in_length
            if "fade_out_shape" in locals() and fade_out_shape is not None:
                item.fade_out_shape = fade_out_shape
            if "fade_out_length" in locals() and fade_out_length is not None:
                item.fade_out_length = fade_out_length
            if "playrate_val" in locals() and playrate_val is not None:
                item.playrate = playrate_val
            if "reverse_flag_str" in locals() and reverse_flag_str is not None:
                item.is_reversed = reverse_flag_str == "1"

            item.takes = self._extract_takes_from_item(item_element, item_guid)
            items.append(item)
        return items

    def _extract_takes_from_item(
        self, item_element: Element, item_guid: str
    ) -> List[Take]:
        """
        Extract takes from an item element.
        `item_guid` is used for more informative warnings/errors.
        """
        takes: List[Take] = []

        # Take-specific properties like its own GUID or Name might be at the ITEM level
        # or within each SOURCE block if it represents a distinct take.
        # The original code implies take GUID/Name are extracted once per item, then associated with sources.
        # This might need refinement if a single ITEM can have multiple takes with distinct names/GUIDs
        # not tied directly to a SOURCE block. For now, replicating original structure.

        take_guid_from_item_level: Optional[str] = None
        take_name_from_item_level: str = "Unnamed Take"

        # First pass for Item-level Take properties (if any, RPP structure can vary)
        for child_list in item_element:
            if not isinstance(child_list, list) or len(child_list) < 1:
                continue
            key = child_list[0]
            # Assuming a take might have its own GUID or NAME directly under ITEM,
            # distinct from the item's GUID/NAME, if it's a single-take item.
            # This part is speculative based on common patterns, original code was simpler.
            # For now, let's assume take GUID/NAME are derived per SOURCE or are same as item.
            # The original code used item's GUID/NAME for the take if SOURCE was found.
            # Let's simplify and assume each SOURCE implies a take, and take name/guid can be found within SOURCE or ITEM.
            pass  # No common take-level GUID/NAME tags at item level usually.

        # Find all SOURCE elements, each representing a take or part of a take
        source_elements = self._find_all_elements(item_element, "SOURCE")
        for i, source_element in enumerate(source_elements):
            default_take_guid = f"{{DEFAULT-TAKE-GUID-{item_guid}-{i}}}"
            # Try to find take-specific name or GUID within SOURCE, else use item's or default
            current_take_guid = default_take_guid
            current_take_name = f"Take {i+1}"  # Default if no specific name found

            file_path: Optional[str] = None
            sample_rate: Optional[int] = None
            bit_depth: Optional[int] = None
            file_type: str = "WAVE"  # Default type

            # SOURCE element's children are the ['KEY', 'VALUE'] lists
            for source_child_list in source_element:
                if (
                    not isinstance(source_child_list, list)
                    or len(source_child_list) < 1
                ):
                    continue

                key = source_child_list[0]
                if key == "FILE":
                    file_path = self._get_typed_value_from_list(
                        source_child_list, 1, str
                    )
                elif key == "SAMPLERATE":
                    sr_val = self._get_typed_value_from_list(source_child_list, 1, int)
                    if sr_val is not None:
                        sample_rate = sr_val
                elif key == "BITDEPTH":
                    bd_val = self._get_typed_value_from_list(source_child_list, 1, int)
                    if bd_val is not None:
                        bit_depth = bd_val
                elif key == "TAKENAME":  # Example if RPP has specific take names
                    current_take_name = self._get_typed_value_from_list(
                        source_child_list, 1, str, default=current_take_name
                    )
                elif key == "TAKEGUID":  # Example
                    current_take_guid = self._get_typed_value_from_list(
                        source_child_list, 1, str, default=current_take_guid
                    )
                # RPP source types
                elif key == "MIDI":
                    file_type = "MIDI"
                    file_path = (
                        file_path or "Embedded MIDI"
                    )  # MIDI might not have a FILE tag
                elif key == "DUMMY":
                    file_type = "DUMMY"
                elif key == "VIDEO":
                    file_type = "VIDEO"
                # Note: Some sources like "SECTION" are not files but references to other project parts.

            source_obj = None
            if (
                file_path or file_type == "MIDI"
            ):  # Create source if path exists or it's MIDI
                source_obj = Source(
                    file_path=file_path,  # Can be None if it's embedded MIDI with no explicit path
                    file_type=file_type,
                    sample_rate=sample_rate,
                    bit_depth=bit_depth,
                )
                # Phase D2: Offline media detection
                if file_path and file_type != "MIDI":
                    self._check_offline_media(source_element, source_obj)
            elif (
                file_type != "WAVE"
            ):  # If not WAVE and no path, still create Source for type info
                source_obj = Source(file_type=file_type)

            take = Take(
                guid=current_take_guid,  # Use take-specific GUID if found, else default
                name=current_take_name,  # Use take-specific name if found, else default
                source=source_obj,
            )
            takes.append(take)

        if not source_elements and item_element.find(
            "MIDI"
        ):  # Check for item-level MIDI if no SOURCE
            # This handles cases where MIDI data is directly under ITEM without a SOURCE wrapper
            # This is a simplification; MIDI structure can be complex.
            midi_source = Source(file_type="MIDI", file_path="Embedded Item MIDI")
            default_midi_take_guid = f"{{DEFAULT-TAKE-GUID-{item_guid}-MIDI-0}}"
            midi_take = Take(
                guid=default_midi_take_guid, name="MIDI Take", source=midi_source
            )
            takes.append(midi_take)
            self.context.add_warning(
                f"Item {item_guid} has direct MIDI content without a SOURCE element. Created a basic MIDI take."
            )

        return takes

    def _check_offline_media(self, source_element, source: Source) -> None:
        """
        Check if media file is marked as offline in the RPP file.
        Phase D2: Offline media detection - only checks RPP internal flags.
        """
        if not source.file_path or source.file_type == "MIDI":
            return
            
        try:
            # Check for explicit offline markers in file path
            if source.file_path.startswith("<OFFLINE>") or "OFFLINE" in source.file_path:
                source.is_offline = True
                source.is_missing = True
                return
                
            # Check REAPER's internal offline flag in the source element
            # REAPER stores offline status in source elements with OFFLINE flag
            for source_child_list in source_element:
                if isinstance(source_child_list, list) and source_child_list:
                    key = source_child_list[0]
                    if key == "OFFLINE":
                        offline_flag = self._get_typed_value_from_list(source_child_list, 1, int)
                        if offline_flag == 1:
                            source.is_offline = True
                            source.is_missing = True
                            return
            
        except Exception as e:
            # If checking fails, log but don't crash
            self.context.add_warning(f"Could not check offline status for {source.file_path}: {str(e)}")

    # TODO: Refactor and move these analysis functions to dedicated analysis modules (e.g., gain_staging_analyzer.py)
    # These will likely take a populated Item object and ParsingContext (for genre_rules, etc.)
    def analyze_item_gain_staging(
        self, item: Item, genre_rules: Dict[str, Any]
    ) -> None:
        """Placeholder: Analyze gain staging for an item based on genre rules."""
        self.context.add_warning(
            f"Gain staging analysis for item {item.guid} is not yet implemented here."
        )

    def check_media_file_consistency(self, item: Item) -> None:
        """Placeholder: Check for missing files and sample rate mismatches for an item's takes."""
        # project_sample_rate = self.context.metadata.sample_rate # Assuming metadata is available in context
        self.context.add_warning(
            f"Media file consistency check for item {item.guid} is not yet implemented here."
        )


# Standalone function for backward compatibility with core.py
def extract_items_from_track(
    track_element: Element, project_sample_rate: int
) -> List[Item]:
    """Standalone function for backward compatibility."""
    from ..infrastructure.parsing_context import ParsingContext
    from ..infrastructure.rpp_tree_cache import RPPTreeCache

    # Create a minimal context for the extractor
    # Note: For single track extraction, we create a minimal project tree
    from xml.etree.ElementTree import Element as ET

    minimal_tree = ET("PROJECT")
    context = ParsingContext(project_tree=minimal_tree, file_path="unknown.rpp")

    extractor = ItemExtractor(context)
    return extractor.extract_items_for_track(
        track_element,
        track_element.attrib[0] if track_element.attrib else "UNKNOWN_GUID",
    )
