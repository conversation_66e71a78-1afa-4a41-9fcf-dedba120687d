"""
Core functionality for parsing RPP files.

This module orchestrates the parsing process by loading the RPP content
and calling individual extractor modules in a structured way.
"""

from typing import Dict, Any, BinaryIO, List, Optional
import io
from rpp import loads, Element
from .models import (
    ParsedSession,
    Metadata,
    Track,
    RoutingLink,
    MasterTrack,  # Added for type checking
)
from .extractors.metadata_extractor import MetadataExtractor
from .infrastructure.parsing_context import ParsingContext
from .engines.plugin_analysis_engine import PluginAnalysisEngine

# Import specific extractor functions needed for orchestration
from .extractors.track_extractor import TrackExtractor
from .extractors.master_track_extractor import MasterTrackExtractor

from .extractors.track_automation_extractor import (
    extract_track_automation,
    extract_send_automation_mappings,
)

# from .extractors.plugin_automation_extractor import extract_plugin_automation # Now handled by PluginAnalysisEngine
from .extractors.routing_extractor import (
    extract_routing,
    create_track_mapping,
    create_guid_mapping,
    update_routing_automation_flags,
)
from .extractors.track_analysis import (
    determine_track_type,
)
from .extractors.session_hygiene_extractor import (
    SessionHygieneExtractor,
    HygieneConfig,
)
from .extractors.item_extractor import extract_items_from_track
from .extractors.gain_analysis import analyze_track_gain_staging
from .extractors.file_reference_extractor import analyze_file_references


from .genre_rules import Genre


def assign_folder_relationships(
    tracks: List[Track],
    project_tree: Element,
    guid_to_track_model_map: Dict[str, Track],
) -> None:
    rpp_track_elements = project_tree.findall("TRACK")
    current_folder_model: Optional[Track] = None
    current_folder_name: Optional[str] = None

    for rpp_track_element in rpp_track_elements:
        track_guid = rpp_track_element.attrib[0] if rpp_track_element.attrib else None
        if not track_guid or track_guid not in guid_to_track_model_map:
            continue
        track_model = guid_to_track_model_map[track_guid]
        if track_model.is_folder:
            current_folder_model = track_model
            current_folder_name = track_model.name
            track_model.is_in_folder = False
            track_model.parent_folder_name = None
        else:
            if current_folder_model is not None:
                track_model.is_in_folder = True
                track_model.parent_folder_name = current_folder_name
            else:
                track_model.is_in_folder = False
                track_model.parent_folder_name = None
        found_folder_end_marker_in_track = False
        for child_of_track in rpp_track_element.children:
            if (
                isinstance(child_of_track, list)
                and len(child_of_track) >= 3
                and child_of_track[0] == "ISBUS"
                and child_of_track[1] == "2"
                and child_of_track[2] == "-1"
            ):
                found_folder_end_marker_in_track = True
                break
            elif (
                isinstance(child_of_track, Element)
                and hasattr(child_of_track, "tag")
                and child_of_track.tag == "ISBUS"
            ):
                if (
                    isinstance(child_of_track.children, list)
                    and len(child_of_track.children) > 0
                    and isinstance(child_of_track.children[0], list)
                    and len(child_of_track.children[0]) >= 2
                ):
                    try:
                        if (
                            child_of_track.children[0][0] == "2"
                            and child_of_track.children[0][1] == "-1"
                        ):
                            found_folder_end_marker_in_track = True
                            break
                    except (IndexError, TypeError):
                        pass
        if found_folder_end_marker_in_track:
            current_folder_model = None
            current_folder_name = None


def parse_rpp_content(
    rpp_content: bytes,
    filename: str = "Unknown.rpp",
    genre: Genre = "general",
    strict_mode: bool = False,
    check_hygiene: bool = True,
    strict_generic_names: bool = True,
    custom_generic_names: list = None,
    genre_rules: dict = None,
) -> ParsedSession:  # Changed return type
    try:
        rpp_string = rpp_content.decode("utf-8", errors="replace")
        project_tree: Element = loads(rpp_string)

        parsing_context = ParsingContext(
            project_tree=project_tree,
            file_path=filename,
            genre=genre,
            strict_mode=strict_mode,
        )
        plugin_engine = PluginAnalysisEngine(parsing_context)

        all_tracks: List[Track] = []
        routing_data: List[RoutingLink] = []
        guid_to_track_model_map: Dict[str, Track] = {}

        metadata_extractor = MetadataExtractor(parsing_context)
        metadata = metadata_extractor.extract()

        # Analyze metadata for project completeness (Phase 1.3)
        if not metadata.title or metadata.title.strip() == "":
            metadata.has_incomplete_project_info = True

        # Analyze metadata for project quality checks (Phase 1.2)
        if metadata.project_bit_depth is not None and metadata.project_bit_depth < 24:
            metadata.is_suboptimal_bit_depth = True

        common_sample_rates = [
            44100,
            48000,
            88200,
            96000,
            176400,
            192000,
        ]  # Added higher common rates
        if metadata.sample_rate not in common_sample_rates:
            metadata.is_nonstandard_sample_rate = True

        # Master track FX processing will be handled by extract_master_track, which needs updating
        master_track_extractor_instance = MasterTrackExtractor(
            parsing_context
        )  # Instantiate
        master_track = master_track_extractor_instance.extract(
            plugin_engine
        )  # Call extract method
        all_tracks.insert(0, master_track)
        metadata.has_master_track = True

        track_name_map = create_track_mapping(project_tree)
        guid_map = create_guid_mapping(project_tree)

        # Use the proper TrackExtractor instead of the old standalone function
        track_extractor = TrackExtractor(parsing_context)
        extracted_tracks = track_extractor.extract()

        for i, track_element in enumerate(project_tree.findall("TRACK")):
            # Get the corresponding extracted track
            track = (
                extracted_tracks[i]
                if i < len(extracted_tracks)
                else Track(guid=f"UNKNOWN-{i}", name=f"Track {i+1}")
            )

            # Use PluginAnalysisEngine for FX
            # The Track model's .fx attribute will store List[PluginAnalysisResult] (which are dicts)
            track.fx = plugin_engine.analyze_fx_chain(
                fxchain_parent_element=track_element,
                track_guid=track.guid,
                track_name=track.name,
                is_master_bus=False,
            )

            # Chain Oversampling Rate
            # This specific logic looks for a global FX_OVERSAMPLE for the chain,
            # distinct from individual plugin oversampling handled by the engine.
            track.chain_oversampling_rate = 0  # Default
            fxchain_node_for_oversample = track_element.find("FXCHAIN")
            if fxchain_node_for_oversample is None:
                fxchain_node_for_oversample = (
                    track_element  # Check track itself if no FXCHAIN tag
                )

            fx_oversample_el = fxchain_node_for_oversample.find("FX_OVERSAMPLE")
            if fx_oversample_el is not None:
                if (
                    isinstance(fx_oversample_el, list) and len(fx_oversample_el) > 1
                ):  # Old rpp lib style
                    try:
                        track.chain_oversampling_rate = int(fx_oversample_el[1])
                    except (ValueError, IndexError):
                        pass
                elif (
                    isinstance(fx_oversample_el, Element) and fx_oversample_el.text
                ):  # New rpp lib style
                    try:
                        track.chain_oversampling_rate = int(
                            fx_oversample_el.text.split()[0]
                        )
                    except (ValueError, IndexError, AttributeError):
                        pass

            track.automation_lanes, track.volume_automated, track.pan_automated = (
                extract_track_automation(track_element)
            )

            # Plugin automation is now handled by PluginAnalysisEngine when it processes each plugin (fx_element)
            # The results are stored in plugin_data["automation"] within the list track.fx.
            # If these automation lanes need to be merged into track.automation_lanes,
            # that would be a separate step here, iterating through track.fx.
            # For now, plugin automation data is within each plugin's analysis result.

            track.type = determine_track_type(track_element, track.is_folder)
            track.items = extract_items_from_track(track_element, metadata.sample_rate)
            analyze_track_gain_staging(track, genre_rules)

            all_tracks.append(track)
            if track_element.attrib and track_element.attrib[0]:
                guid_to_track_model_map[track_element.attrib[0]] = track

        send_automation_mappings = extract_send_automation_mappings(project_tree)
        for source_idx, destination_lanes in send_automation_mappings.items():
            if 0 <= source_idx < len(all_tracks) - 1:
                track_to_update = all_tracks[source_idx + 1]
                for dest_name, lane in destination_lanes:
                    track_to_update.automation_lanes.append(lane)

        routing_data = extract_routing(project_tree)
        assign_folder_relationships(all_tracks, project_tree, guid_to_track_model_map)
        update_routing_automation_flags(routing_data, all_tracks)

        parsed_session = ParsedSession(
            metadata=metadata,
            tracks=all_tracks,
            routing=routing_data,
            parsing_status="Success",
        )

        # Old PluginBlacklistEngine call - its functionality is partially moved.
        # Per-plugin blacklisting is done by PluginAnalysisEngine.
        # Master bus chain analysis is also done by PluginAnalysisEngine when master track FX are processed.
        # The results are in parsing_context.custom_analysis_results.
        # We might need to retrieve it from context and add to session output if needed.
        # The old PluginBlacklistEngine import is removed as its functionality is being integrated.
        # For now, use parsed_session directly, assuming engine handles flags on plugin data.
        analyzed_session = parsed_session

        if check_hygiene:
            config = HygieneConfig(
                strict_generic_patterns=strict_generic_names,
                custom_generic_names=set(custom_generic_names or []),
            )
            hygiene_extractor = SessionHygieneExtractor(parsing_context, config=config)
            hygiene_extractor.analyze_session_hygiene(
                project_tree,
                analyzed_session.tracks,
                analyzed_session.metadata,
                genre_rules=genre_rules,
            )

        file_ref_analysis_results = analyze_file_references(
            project_tree,
            (
                analyzed_session.tracks[0].items
                if analyzed_session.tracks and analyzed_session.tracks[0].items
                else []
            ),
            analyzed_session.metadata,
            analyzed_session.tracks,
        )

        # Ensure file_reference_analysis field exists on metadata
        if not hasattr(analyzed_session.metadata, "file_reference_analysis"):
            analyzed_session.metadata.file_reference_analysis = {}  # type: ignore

        analyzed_session.metadata.file_reference_analysis = file_ref_analysis_results  # type: ignore

        for (
            track_model
        ) in analyzed_session.tracks:  # Renamed track to track_model for clarity
            for item in track_model.items:
                for problematic_ref in file_ref_analysis_results.get(
                    "problematic_file_references", []
                ):
                    if item.guid == problematic_ref.get("item_id"):
                        item_issues = problematic_ref.get("issues", [])
                        item.has_absolute_path = "Absolute path detected" in item_issues
                        item.has_system_temp_path = (
                            "References system/temp directory" in item_issues
                        )
                        item.has_external_relative_path = (
                            "Relative path outside common media folders" in item_issues
                        )
                        item.has_sample_rate_mismatch = (
                            "Sample rate mismatch" in item_issues
                        )
                        item.file_reference_issues.extend(item_issues)
                        break

        # Analyze item fades and project start (Phase 1.2)
        ABRUPT_FADE_THRESHOLD = 0.001  # seconds (1ms)
        min_item_position = float("inf")
        first_items_at_zero_with_abrupt_start = False

        all_project_items = []
        for track_model_for_items in analyzed_session.tracks:
            all_project_items.extend(track_model_for_items.items)
            for item in track_model_for_items.items:
                if (
                    item.fade_in_length is None
                    or item.fade_in_length < ABRUPT_FADE_THRESHOLD
                ):
                    item.has_abrupt_start = (
                        True  # This flag needs to be added to Item model
                    )
                if (
                    item.fade_out_length is None
                    or item.fade_out_length < ABRUPT_FADE_THRESHOLD
                ):
                    item.has_abrupt_end = True

                # Analyze item for unusual properties (Phase 1.3)
                unusual_warnings = []
                if item.is_reversed:
                    unusual_warnings.append("Item is reversed.")
                # Using a small epsilon for float comparison
                if abs(item.playrate - 1.0) > 0.00001:
                    unusual_warnings.append(
                        f"Item playrate is not 1.0 (actual: {item.playrate:.2f})."
                    )

                if unusual_warnings:
                    item.has_unusual_properties_warning = " ".join(unusual_warnings)

                if item.position < min_item_position:
                    min_item_position = item.position

        if abs(min_item_position) < 0.00001:  # Check if effectively zero
            for item in all_project_items:
                if (
                    abs(item.position - min_item_position) < 0.00001
                    and item.has_abrupt_start
                ):
                    # This flag needs to be added to Metadata model
                    analyzed_session.metadata.first_item_has_abrupt_start_at_zero = True
                    break

        # Set track-level flags based on plugin states
        for (
            track_model_item
        ) in analyzed_session.tracks:  # Iterate through all tracks including master
            for plugin_result in track_model_item.fx:
                if plugin_result.get("is_bypassed", False):
                    track_model_item.has_bypassed_fx_in_chain = True
                    break  # Found one bypassed plugin, no need to check further for this track

        # Set master track specific flags based on its plugins
        # Ensure master track is present and is indeed a MasterTrack instance
        if analyzed_session.tracks and isinstance(
            analyzed_session.tracks[0], MasterTrack
        ):
            master_track_model = analyzed_session.tracks[0]
            for plugin_result in master_track_model.fx:
                # plugin_result is a PluginAnalysisResult (TypedDict)
                plugin_category = plugin_result.get("category", "").lower()
                # Check for common terms indicating a limiter or maximizer
                if "limiter" in plugin_category or "maximizer" in plugin_category:
                    master_track_model.has_limiter_on_master = True
                    break  # Found one, no need to check further

            # Set master bus automation flag
            # This assumes master_track_model.volume_automated and .pan_automated are correctly
            # populated by an extractor (e.g., MasterTrackExtractor or a dedicated master automation extractor).
            # If these are not being set, the underlying extraction logic needs enhancement.
            if master_track_model.volume_automated or master_track_model.pan_automated:
                master_track_model.has_master_bus_automation = True

        # Stereo balance check (Phase 1.2)
        # Exclude master track from pan analysis
        non_master_tracks = [
            tm for tm in analyzed_session.tracks if not isinstance(tm, MasterTrack)
        ]
        if non_master_tracks:  # Only proceed if there are non-master tracks
            pan_hard_left_count = 0
            pan_hard_right_count = 0
            pan_center_ish_count = 0
            pan_other_count = 0  # Tracks panned but not hard left/right or center

            HARD_PAN_THRESHOLD = 0.9  # Panned 90% or more
            CENTER_ISH_THRESHOLD = 0.1  # Panned within 10% of center

            for track_model_item in non_master_tracks:
                pan_value = track_model_item.pan
                if pan_value <= -HARD_PAN_THRESHOLD:
                    pan_hard_left_count += 1
                elif pan_value >= HARD_PAN_THRESHOLD:
                    pan_hard_right_count += 1
                elif abs(pan_value) < CENTER_ISH_THRESHOLD:
                    pan_center_ish_count += 1
                else:
                    pan_other_count += 1

            total_panned_tracks = (
                pan_hard_left_count
                + pan_hard_right_count
                + pan_center_ish_count
                + pan_other_count
            )

            if (
                total_panned_tracks > 0
            ):  # Avoid division by zero and issues with empty projects
                # Heuristic 1: Significant skew to one side
                # Check if more than, e.g., 75% of panned tracks are on one side, and very few on the other or center
                # And there are at least a few tracks to make the percentage meaningful (e.g., > 3 tracks)
                MIN_TRACKS_FOR_SKEW_CHECK = 3
                SKEW_PERCENTAGE_THRESHOLD = 0.75

                if (
                    pan_hard_left_count > MIN_TRACKS_FOR_SKEW_CHECK
                    and (pan_hard_left_count / total_panned_tracks)
                    > SKEW_PERCENTAGE_THRESHOLD
                    and pan_hard_right_count < (pan_hard_left_count * 0.25)
                ):  # Significantly fewer on the other side
                    analyzed_session.metadata.stereo_balance_issue_description = (
                        "Mix appears significantly skewed to the left. Check panning."
                    )
                elif (
                    pan_hard_right_count > MIN_TRACKS_FOR_SKEW_CHECK
                    and (pan_hard_right_count / total_panned_tracks)
                    > SKEW_PERCENTAGE_THRESHOLD
                    and pan_hard_left_count < (pan_hard_right_count * 0.25)
                ):
                    analyzed_session.metadata.stereo_balance_issue_description = (
                        "Mix appears significantly skewed to the right. Check panning."
                    )
                # Heuristic 2: Lack of center elements (potential mono compatibility issue)
                # This is more genre-dependent, but a general warning can be raised.
                # If many tracks are panned wide and very few are center.
                elif (
                    (pan_hard_left_count + pan_hard_right_count)
                    > (pan_center_ish_count * 2)
                    and pan_center_ish_count <= 1
                    and total_panned_tracks > MIN_TRACKS_FOR_SKEW_CHECK
                ):  # e.g. only 0 or 1 track in center
                    analyzed_session.metadata.stereo_balance_issue_description = (
                        "Very few centrally panned elements. Check mono compatibility."
                    )

        # Add master bus chain analysis results from context to the final output if present
        if hasattr(parsing_context, "custom_analysis_results") and "master_bus_chain" in parsing_context.custom_analysis_results:  # type: ignore
            # How to add this to final dict? Maybe under a global_analysis key.
            # For now, let's assume to_dict() will be extended or we add it manually.
            # If master_bus_chain_analysis needs to be part of ParsedSession, it should be added to the model.
            # For now, returning the object directly. If this data is crucial, ParsedSession model needs update.
            # Or, the caller (main.py) could potentially access parsing_context if it were returned too.
            # For simplicity, we are not including it directly in ParsedSession object here.
            pass  # This custom_analysis_results was for a dictionary output.

        return analyzed_session  # Return the ParsedSession object

    except Exception as e:
        print(f"Error parsing RPP content: {e}")  # Basic logging
        raise Exception(f"RPP Parsing failed: {e}")


def parse_rpp_file(file_path: str) -> ParsedSession:  # Changed return type
    filename = file_path.split("/")[-1]
    with open(file_path, "rb") as f:
        content = f.read()
    return parse_rpp_content(content, filename)


def parse_rpp_fileobj(
    file_obj: BinaryIO, filename: str = "Unknown.rpp"
) -> ParsedSession:  # Changed return type
    content = file_obj.read()
    return parse_rpp_content(content, filename)
