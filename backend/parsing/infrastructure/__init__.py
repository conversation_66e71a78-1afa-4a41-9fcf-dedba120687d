# backend/parsing/infrastructure/__init__.py

"""
This package contains core infrastructure components for the RPP parsing system.
These components provide foundational services like:
- Custom exception handling (`exceptions.py`)
- Caching of RPP elements (`rpp_tree_cache.py`)
- Centralized parsing context (`parsing_context.py`)
- Base class for extractors (`base_extractor.py`)
- Common validation utilities (`validation_utils.py`)
"""

# You can choose to expose specific classes/functions at the package level for convenience, e.g.:
# from .exceptions import RPPParsingError, MissingElementError, InvalidDataError, PluginAnalysisError
# from .rpp_tree_cache import RPPTreeCache
# from .parsing_context import ParsingContext
# from .base_extractor import BaseExtractor

# For now, users of this package will import directly from the modules.
