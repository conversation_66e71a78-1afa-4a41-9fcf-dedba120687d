from typing import Dict, Any, List
from .rpp_tree_cache import RPP<PERSON>reeCache
from .exceptions import RPPParsingError
from xml.etree.ElementTree import Element  # Changed from rpp.rpp

from ..genre_rules import Genre, get_rules_for_genre  # Uncommented and corrected


class ParsingContext:
    def __init__(
        self,
        project_tree: Element,
        file_path: str,
        genre: str = "general",
        strict_mode: bool = False,
        max_warnings: int = 1000,
        max_errors: int = 500,
    ):
        self.file_path = file_path
        self.project_tree = project_tree
        self.genre_name: str = (
            genre  # Storing genre name, actual rules object can be loaded
        )
        self.strict_mode: bool = strict_mode
        self.cache = RPPTreeCache(project_tree)
        self.genre_rules: Dict[str, Any] = get_rules_for_genre(
            self.genre_name, self.strict_mode
        )  # Load once
        
        # Bounded collections to prevent memory leaks
        self.warnings: List[str] = []
        self.errors: List[RPPParsingError] = []
        self.max_warnings = max_warnings
        self.max_errors = max_errors
        self.warnings_dropped = 0
        self.errors_dropped = 0

    def add_warning(self, message: str):
        """Add a warning with automatic size management."""
        if len(self.warnings) >= self.max_warnings:
            # Remove oldest warnings and track dropped count
            removed_count = len(self.warnings) - self.max_warnings + 1
            self.warnings = self.warnings[removed_count:]
            self.warnings_dropped += removed_count
        
        self.warnings.append(message)

    def add_error(self, error: RPPParsingError):
        """Add an error with automatic size management."""
        if len(self.errors) >= self.max_errors:
            # Remove oldest errors and track dropped count
            removed_count = len(self.errors) - self.max_errors + 1
            self.errors = self.errors[removed_count:]
            self.errors_dropped += removed_count
        
        self.errors.append(error)

    def get_warning_summary(self) -> Dict[str, Any]:
        """Get summary of warnings including dropped count."""
        return {
            "total_warnings": len(self.warnings) + self.warnings_dropped,
            "current_warnings": len(self.warnings),
            "warnings_dropped": self.warnings_dropped,
            "warnings": self.warnings
        }

    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of errors including dropped count."""
        return {
            "total_errors": len(self.errors) + self.errors_dropped,
            "current_errors": len(self.errors),
            "errors_dropped": self.errors_dropped,
            "errors": self.errors
        }

    # Potentially add a property for genre_rules that loads them on first access
    # @property
    # def genre_rules(self) -> Any:
    #     if not hasattr(self, '_genre_rules_instance'):
    #         # self._genre_rules_instance = get_rules_for_genre(self.genre_name, self.strict_mode)
    #         pass # Placeholder for actual loading
    #     return self._genre_rules_instance
