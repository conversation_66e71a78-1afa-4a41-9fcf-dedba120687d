from abc import ABC, abstractmethod
from typing import Any, Optional, Type, TypeVar, List as PyList
from .parsing_context import ParsingContext
from .exceptions import MissingElementError, InvalidDataError
from rpp import Element

T = TypeVar("T")


class BaseExtractor(ABC):
    def __init__(self, context: ParsingContext):
        self.context = context

    @abstractmethod
    def extract(self) -> Any:
        """Main extraction method for the extractor."""
        pass

    def _find_element(
        self, parent: Element, tag_name: str, required: bool = False
    ) -> Optional[Element]:
        element = parent.find(tag_name)
        if element is None and required:
            error = MissingElementError(
                tag_name, parent.tag if hasattr(parent, "tag") else "UnknownElement"
            )
            self.context.add_error(error)
            # Decide on error handling strategy: raise immediately or allow collection
            # For now, let's raise to make missing required elements immediately obvious
            raise error
        return element

    def _find_all_elements(self, parent: Element, tag_name: str) -> PyList[Element]:
        return parent.findall(tag_name)

    def _get_element_text(
        self,
        element: Optional[Element],
        default: Optional[str] = None,
        required: bool = False,
    ) -> Optional[str]:
        if element is None or not hasattr(element, "text") or element.text is None:
            if required:
                element_tag = (
                    element.tag
                    if element is not None and hasattr(element, "tag")
                    else "UnknownElement"
                )
                error = InvalidDataError(
                    f"Required text content missing for element '{element_tag}'."
                )
                self.context.add_error(error)
                raise error
            return default
        return element.text.strip()

    def _get_typed_value_from_list(
        self,
        data_list: Optional[PyList[str]],
        index: int,
        value_type: Type[T],
        default: Optional[T] = None,
        required: bool = False,
    ) -> Optional[T]:
        if data_list is None or index >= len(data_list) or data_list[index] is None:
            if required:
                error = InvalidDataError(
                    f"Required value at index {index} is missing or list is too short."
                )
                self.context.add_error(error)
                raise error
            return default

        value_str = data_list[index]
        try:
            if (
                value_type is bool
            ):  # Special handling for bool from string "0" "1" "true" "false"
                if value_str.lower() in ("true", "1"):
                    return True  # type: ignore
                elif value_str.lower() in ("false", "0"):
                    return False  # type: ignore
                else:
                    raise ValueError(f"Cannot convert '{value_str}' to bool.")
            return value_type(value_str)
        except (ValueError, TypeError) as e:
            if required:
                error = InvalidDataError(
                    f"Cannot convert value '{value_str}' at index {index} to {value_type.__name__}: {e}"
                )
                self.context.add_error(error)
                raise error
            self.context.add_warning(
                f"Conversion failed for value '{value_str}' at index {index} to {value_type.__name__}. Using default: {default}."
            )
            return default
