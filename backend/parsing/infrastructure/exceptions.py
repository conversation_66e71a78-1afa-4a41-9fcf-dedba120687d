class RPPParsingError(Exception):
    """Base class for all RPP parsing errors."""

    pass


class MissingElementError(RPPParsingError):
    """Raised when a required RPP element is not found."""

    def __init__(self, element_name: str, parent_context: str = "Unknown"):
        super().__init__(
            f"Required element '{element_name}' not found in {parent_context}."
        )


class InvalidDataError(RPPParsingError):
    """Raised when RPP data is malformed or unexpected."""

    pass


class PluginAnalysisError(RPPParsingError):
    """Specific errors related to plugin analysis."""

    pass
