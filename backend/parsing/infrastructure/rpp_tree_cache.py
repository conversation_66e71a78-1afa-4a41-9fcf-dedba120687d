from typing import List, Dict, Any

# Using the RPP library's Element type
from rpp import Element


class RPPTreeCache:
    def __init__(self, project_tree: Element):
        self._project_tree = project_tree
        self._tracks: List[Element] = []
        self._track_guid_map: Dict[str, str] = {}  # GUID -> Track Name
        self._track_name_map: Dict[str, Element] = {}  # Track Name -> Track Element
        # Add more cached items as needed (e.g., FX chains, master track)
        self._initialized = False

    def _initialize_caches(self):
        if self._initialized:
            return

        # RPP library's findall method
        self._tracks = self._project_tree.findall("TRACK")

        for i, track_element in enumerate(self._tracks):
            # Get track GUID from attrib (first element)
            track_guid = (
                track_element.attrib[0]
                if track_element.attrib
                else f"{{DEFAULT-GUID-{i}}}"
            )

            # Get track name from NAME child element
            track_name = f"Track {i+1}"  # Default name
            for child in track_element.children:
                if isinstance(child, list) and len(child) > 1 and child[0] == "NAME":
                    track_name = child[1]
                    break

            self._track_name_map[track_name] = track_element
            self._track_guid_map[track_guid] = track_name
        self._initialized = True

    @property
    def tracks(self) -> List[Element]:
        self._initialize_caches()
        return self._tracks

    def get_track_by_name(self, name: str) -> Element | None:
        self._initialize_caches()
        return self._track_name_map.get(name)

    def get_track_name_by_guid(self, guid: str) -> str | None:
        self._initialize_caches()
        return self._track_guid_map.get(guid)

    # Add more specific getters as needed
    # Example:
    # @property
    # def master_track_element(self) -> Element | None:
    #     self._initialize_caches()
    #     # Logic to find and cache master track element
    #     # master_element = self._project_tree.find("MASTERTRACK_TAG_HERE") # Replace with actual tag
    #     # return master_element
    #     return None # Placeholder
