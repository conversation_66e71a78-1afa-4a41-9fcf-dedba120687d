"""
Data models representing structures within a REAPER project file.
"""

from __future__ import annotations  # Added for postponed evaluation of type annotations
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple, TypedDict

# PluginAnalysisResult definition will be moved here.
# The import from .engines.plugin_analysis_engine will be removed.


# Moved from plugin_analysis_engine.py to break circular import
class PluginAnalysisResult(TypedDict, total=False):
    name: Optional[str]
    type: Optional[str]  # VST, JS, AU, etc.
    guid: Optional[str]
    raw_text: Optional[str]
    encoded_params: Optional[str]
    program_chunk: Optional[str]
    is_bypassed: bool
    has_oversampling: bool
    oversampling_rate: int
    is_blacklisted_general: bool
    is_blacklisted_master: bool
    would_be_blacklisted_general_if_active: bool
    would_be_blacklisted_master_if_active: bool
    category: str
    confidence: float  # Add confidence score
    parameters: Optional[Dict[str, Any]]
    automation: List[AutomationLane]
    track_guid: str
    track_name: str


@dataclass
class Metadata:
    """Project-level metadata."""

    filename: str = "Unknown.rpp"
    reaper_version: str = "Unknown"
    save_timestamp: str = "Unknown"
    title: Optional[str] = None
    notes: Optional[str] = None
    has_master_track: bool = False
    sample_rate: int = 44100
    tempo: float = 120.0
    time_signature: str = "4/4"
    # Hygiene flags (session-level)
    has_missing_markers: bool = False
    has_missing_regions: bool = False
    has_missing_notes: bool = False
    project_bit_depth: Optional[int] = None  # Added for file consistency analysis
    file_reference_analysis: Optional[Dict[str, Any]] = (
        None  # Added for file consistency analysis results
    )
    # Added for Phase 1.2 Project-Level Quality Checks
    is_suboptimal_bit_depth: bool = field(default=False)
    is_nonstandard_sample_rate: bool = field(default=False)
    first_item_has_abrupt_start_at_zero: bool = field(
        default=False
    )  # Added for Phase 1.2 boundary checks
    stereo_balance_issue_description: Optional[str] = field(
        default=None
    )  # Added for Phase 1.2 pan analysis
    # Added for Phase 1.3 Project Metadata Completeness
    has_incomplete_project_info: bool = field(default=False)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "filename": self.filename,
            "reaper_version": self.reaper_version,
            "save_timestamp": self.save_timestamp,
            "title": self.title,
            "notes": self.notes,
            "has_master_track": self.has_master_track,
            "sample_rate": self.sample_rate,
            "tempo": self.tempo,
            "time_signature": self.time_signature,
            "has_missing_markers": self.has_missing_markers,
            "has_missing_regions": self.has_missing_regions,
            "has_missing_notes": self.has_missing_notes,
            "project_bit_depth": self.project_bit_depth,  # Added to dict
            "file_reference_analysis": self.file_reference_analysis,  # Added to dict
            # Added for Phase 1.2
            "is_suboptimal_bit_depth": self.is_suboptimal_bit_depth,
            "is_nonstandard_sample_rate": self.is_nonstandard_sample_rate,
            "first_item_has_abrupt_start_at_zero": self.first_item_has_abrupt_start_at_zero,
            "stereo_balance_issue_description": self.stereo_balance_issue_description,
            "has_incomplete_project_info": self.has_incomplete_project_info,
        }


@dataclass
class FX:
    """Represents an FX plugin in a REAPER track."""

    name: str  # This FX class might become obsolete or merged with PluginAnalysisResult
    plugin_type: str = "Unknown"  # VST, JS, AU, etc.
    has_oversampling: bool = False
    oversampling_rate: int = 0  # 0=None, 1=2x, 2=4x, 3=8x, 4=16x
    is_bypassed: bool = False
    # Mastering Audit Flags
    is_blacklisted_general: bool = False
    is_blacklisted_master: bool = False
    is_finalizer_plugin: bool = False  # e.g., Ozone, Pro-L, Elevate
    oversampling_recommended: bool = False  # Based on .clinerules
    oversampling_enabled: bool = False  # If has_oversampling and oversampling_rate > 0
    # Bypass-aware flags for bypassed plugins that would be problematic if active
    would_be_blacklisted_general_if_active: bool = False
    would_be_blacklisted_master_if_active: bool = False
    # Master Bus Chain Analysis Flags (set by PluginBlacklistEngine._classify_plugin)
    is_limiter: bool = False
    is_metering_plugin: bool = False
    is_mixing_plugin_on_master_candidate: bool = False

    # Raw and decoded parameter data
    plugin_guid: Optional[str] = None
    encoded_params: Optional[str] = None  # Raw base64 encoded parameter chunk
    program_chunk: Optional[str] = None  # Raw base64 for program name / simpler state
    decoded_params: Optional[Dict[str, Any]] = field(
        default_factory=dict
    )  # For generic decoded key-value pairs

    # Specific decoded parameters for common mastering analysis (populated by decoders)
    limiter_threshold: Optional[float] = None
    limiter_true_peak_enabled: Optional[bool] = None
    compressor_threshold: Optional[float] = None
    compressor_ratio: Optional[float] = None
    compressor_attack: Optional[float] = None
    compressor_release: Optional[float] = None
    eq_band_count: Optional[int] = None
    eq_low_shelf_gain: Optional[float] = None
    eq_high_shelf_gain: Optional[float] = None
    clipper_ceiling: Optional[float] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        data = {
            "name": self.name,
            "plugin_type": self.plugin_type,
            "has_oversampling": self.has_oversampling,
            "oversampling_rate": self.oversampling_rate,
            "is_bypassed": self.is_bypassed,
            "is_blacklisted_general": self.is_blacklisted_general,
            "is_blacklisted_master": self.is_blacklisted_master,
            "is_finalizer_plugin": self.is_finalizer_plugin,
            "oversampling_recommended": self.oversampling_recommended,
            "oversampling_enabled": self.oversampling_enabled,
            "would_be_blacklisted_general_if_active": self.would_be_blacklisted_general_if_active,
            "would_be_blacklisted_master_if_active": self.would_be_blacklisted_master_if_active,
            "is_limiter": self.is_limiter,
            "is_metering_plugin": self.is_metering_plugin,
            "is_mixing_plugin_on_master_candidate": self.is_mixing_plugin_on_master_candidate,
            "plugin_guid": self.plugin_guid,
        }
        # Add specific decoded params if they exist
        if self.limiter_threshold is not None:
            data["limiter_threshold"] = self.limiter_threshold
        if self.limiter_true_peak_enabled is not None:
            data["limiter_true_peak_enabled"] = self.limiter_true_peak_enabled
        if self.compressor_threshold is not None:
            data["compressor_threshold"] = self.compressor_threshold
        if self.compressor_ratio is not None:
            data["compressor_ratio"] = self.compressor_ratio
        if self.compressor_attack is not None:
            data["compressor_attack"] = self.compressor_attack
        if self.compressor_release is not None:
            data["compressor_release"] = self.compressor_release
        if self.eq_band_count is not None:
            data["eq_band_count"] = self.eq_band_count
        if self.eq_low_shelf_gain is not None:
            data["eq_low_shelf_gain"] = self.eq_low_shelf_gain
        if self.eq_high_shelf_gain is not None:
            data["eq_high_shelf_gain"] = self.eq_high_shelf_gain
        if self.clipper_ceiling is not None:
            data["clipper_ceiling"] = self.clipper_ceiling

        return data


@dataclass
class Track:
    """Represents a track in the REAPER project."""

    guid: str
    name: str = "Unnamed Track"
    type: str = "AUDIO"  # AUDIO, MIDI, BUS, AUX, MASTER
    fx: List[PluginAnalysisResult] = field(
        default_factory=list
    )  # Changed type to List[PluginAnalysisResult]
    color: str = "FFFFFF"  # RGB hex color
    volume: float = 1.0
    pan: float = 0.0
    muted: bool = False
    soloed: bool = False
    is_folder: bool = False
    folder_depth: int = 0
    parent_folder_name: Optional[str] = (
        None  # Name of the parent folder if track is in a folder
    )
    is_in_folder: bool = False  # True if track is inside a folder
    chain_oversampling_rate: int = 0  # 0=None, 1=2x, 2=4x, 3=8x, 4=16x
    volume_automated: bool = False  # Flag indicating if the track volume is automated
    pan_automated: bool = False  # Flag indicating if the track pan is automated
    automation_lanes: List["AutomationLane"] = field(default_factory=list)
    items: List["Item"] = field(default_factory=list)
    # Mastering Audit Flags
    has_blacklisted_plugins_general: bool = False
    has_blacklisted_plugins_master: bool = False
    has_finalizer_plugins: bool = False
    has_oversampling_issues: bool = False  # If oversampling recommended but not enabled
    is_hygiene_issue: bool = (
        False  # For generic names, not in folder, empty items, etc.
    )
    gain_staging_warning: Optional[str] = None  # e.g., "Too Hot", "Too Low"
    has_missing_media: bool = False
    has_sample_rate_mismatch: bool = False
    has_unused_media: bool = False  # Post-MVP
    has_master_bus_automation: bool = False  # For master track volume/pan automation
    has_limiter_on_master: bool = False
    has_clipper_on_master: bool = False
    # Master Bus Chain Analysis Flags (set by PluginBlacklistEngine.analyze_master_bus_chain)
    has_multiple_limiters_on_master: bool = False
    limiter_not_last_on_master: bool = False
    has_mixing_plugins_on_master: bool = False
    has_problematic_master_chain_order: bool = False
    # Hygiene flags
    has_duplicate_name: bool = False
    has_empty_items: bool = False
    # Hygiene flags (detailed)
    has_generic_name: bool = False
    not_in_folder_structure: bool = False
    hygiene_details: Dict[str, str] = field(default_factory=dict)
    is_record_armed: bool = field(default=False)  # Added for foundational enhancements
    has_bypassed_fx_in_chain: bool = field(
        default=False
    )  # Added for Phase 1.1 analysis

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "guid": self.guid,
            "name": self.name,
            "type": self.type,
            "fx": self.fx,  # fx is now List[PluginAnalysisResult], which are already dicts
            "color": self.color,
            "volume": self.volume,
            "pan": self.pan,
            "muted": self.muted,
            "soloed": self.soloed,
            "is_folder": self.is_folder,
            "folder_depth": self.folder_depth,
            "parent_folder_name": self.parent_folder_name,
            "is_in_folder": self.is_in_folder,
            "chain_oversampling_rate": self.chain_oversampling_rate,
            "volume_automated": self.volume_automated,
            "pan_automated": self.pan_automated,
            "automation_lanes": [lane.to_dict() for lane in self.automation_lanes],
            "items": [item.to_dict() for item in self.items],
            "has_blacklisted_plugins_general": self.has_blacklisted_plugins_general,
            "has_blacklisted_plugins_master": self.has_blacklisted_plugins_master,
            "has_finalizer_plugins": self.has_finalizer_plugins,
            "has_oversampling_issues": self.has_oversampling_issues,
            "is_hygiene_issue": self.is_hygiene_issue,
            "gain_staging_warning": self.gain_staging_warning,
            "has_missing_media": self.has_missing_media,
            "has_sample_rate_mismatch": self.has_sample_rate_mismatch,
            "has_unused_media": self.has_unused_media,
            "has_master_bus_automation": self.has_master_bus_automation,
            "has_limiter_on_master": self.has_limiter_on_master,
            "has_clipper_on_master": self.has_clipper_on_master,
            "has_multiple_limiters_on_master": self.has_multiple_limiters_on_master,
            "limiter_not_last_on_master": self.limiter_not_last_on_master,
            "has_mixing_plugins_on_master": self.has_mixing_plugins_on_master,
            "has_problematic_master_chain_order": self.has_problematic_master_chain_order,
            "has_duplicate_name": self.has_duplicate_name,
            "has_empty_items": self.has_empty_items,
            "has_generic_name": self.has_generic_name,
            "not_in_folder_structure": self.not_in_folder_structure,
            "hygiene_details": self.hygiene_details,
            "is_record_armed": self.is_record_armed,  # Added for foundational enhancements
            "has_bypassed_fx_in_chain": self.has_bypassed_fx_in_chain,  # Added for Phase 1.1 analysis
        }


@dataclass
class MasterTrack(Track):
    """Special case of Track representing the Master track."""

    def __post_init__(self):
        """Set default values specific to Master track."""
        self.guid = "MASTER_TRACK"
        self.name = "Master"
        self.type = "MASTER"


@dataclass
class AutomationPoint:
    """Represents a single point in an automation envelope."""

    time: float  # Position in seconds
    value: float  # Value at this point
    shape: int = 0  # Curve shape (0=linear, 1=square, etc.)
    tension: Optional[float] = None  # Bezier tension if applicable

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        result = {"time": self.time, "value": self.value, "shape": self.shape}
        if self.tension is not None:
            result["tension"] = self.tension
        return result


@dataclass
class AutomationLane:
    """Represents an automation envelope lane in a REAPER project."""

    display_name: (
        str  # User-friendly name (e.g., "Pan (Post-FX)" or "ReaSynth: Attack")
    )
    guid: str  # Envelope GUID
    is_active: bool = True
    is_visible: bool = True
    is_armed: bool = False
    lane_height: int = 0
    default_shape: int = 0  # Default curve shape for new points
    parameter_name: Optional[str] = None  # Used for plugin parameters (e.g., "_Attack")
    parameter_index: Optional[int] = None  # Used for plugin parameters
    plugin_name: Optional[str] = None  # Used for plugin parameters
    min_value: Optional[float] = None  # Used for plugin parameters
    max_value: Optional[float] = None  # Used for plugin parameters
    default_value: Optional[float] = None  # Used for plugin parameters
    dest_channel: Optional[int] = (
        None  # For send automation: destination channel (e.g., 0 for 1-2, 2 for 3-4)
    )
    points: List[AutomationPoint] = field(default_factory=list)
    effective_points: int = 0  # Points beyond the default one

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        result = {
            "display_name": self.display_name,
            "guid": self.guid,
            "is_active": self.is_active,
            "is_visible": self.is_visible,
            "is_armed": self.is_armed,
            "lane_height": self.lane_height,
            "default_shape": self.default_shape,
            "points": [point.to_dict() for point in self.points],
            "effective_points": self.effective_points,
        }

        # Include plugin parameter details if available
        if self.parameter_name:
            result["parameter_name"] = self.parameter_name
        if self.parameter_index is not None:
            result["parameter_index"] = self.parameter_index
        if self.plugin_name:
            result["plugin_name"] = self.plugin_name
        if self.min_value is not None:
            result["min_value"] = self.min_value
        if self.max_value is not None:
            result["default_value"] = self.default_value
        if self.dest_channel is not None:
            result["dest_channel"] = self.dest_channel

        return result


@dataclass
class RoutingLink:
    """Represents a send or receive between tracks."""

    source: str  # Source track name
    destination: str  # Destination track name
    type: str  # "Send" or "Receive"
    source_guid: str = ""  # Source track GUID
    destination_guid: str = ""  # Destination track GUID
    volume: float = 1.0
    pan: float = 0.0
    muted: bool = False
    src_channel: int = 0  # Source channel index
    dest_channel: int = 0  # Destination channel index
    channels: int = 2  # Number of channels (typically 2 for stereo)
    volume_automated: bool = False  # Flag indicating if the send volume is automated
    pan_automated: bool = False  # Flag indicating if the send pan is automated
    mute_automated: bool = False  # Flag indicating if the send mute is automated

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "source": self.source,
            "destination": self.destination,
            "type": self.type,
            "source_guid": self.source_guid,
            "destination_guid": self.destination_guid,
            "volume": self.volume,
            "pan": self.pan,
            "muted": self.muted,
            "src_channel": self.src_channel,
            "dest_channel": self.dest_channel,
            "channels": self.channels,
            "volume_automated": self.volume_automated,
            "pan_automated": self.pan_automated,
            "mute_automated": self.mute_automated,
        }


@dataclass
class ParsedSession:
    """Represents the complete parsed data from an RPP file."""

    metadata: Metadata
    tracks: List[Track] = field(default_factory=list)
    routing: List[RoutingLink] = field(default_factory=list)
    parsing_status: str = "Success"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "filename": self.metadata.filename,
            "metadata": self.metadata.to_dict(),
            "tracks": [track.to_dict() for track in self.tracks],
            "routing": [route.to_dict() for route in self.routing],
            "parsing_status": self.parsing_status,
        }


@dataclass
class Source:
    """Represents the source media file for a take."""

    file_path: str
    file_type: str = "WAVE"  # WAVE, MIDI, etc.
    sample_rate: Optional[int] = None
    bit_depth: Optional[int] = None
    is_offline: bool = False  # Phase D2: Offline media detection (from RPP flags only)
    is_missing: bool = False  # Phase D2: Missing/offline detection (from RPP flags only)

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "file_path": self.file_path,
            "file_type": self.file_type,
            "is_offline": self.is_offline,
            "is_missing": self.is_missing,
        }
        if self.sample_rate is not None:
            result["sample_rate"] = self.sample_rate
        if self.bit_depth is not None:
            result["bit_depth"] = self.bit_depth
        return result


@dataclass
class Take:
    """Represents a take within an item."""

    guid: str
    name: str = "Unnamed Take"
    source: Optional[Source] = None  # Changed from source_file: Optional[str]
    volume: float = 1.0
    pan: float = 0.0
    start_offset: float = 0.0
    playrate: float = 1.0
    # Gain staging analysis
    volume_db: Optional[float] = None
    gain_staging_flag: Optional[str] = None  # "too_hot", "too_low", "ok"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "guid": self.guid,
            "name": self.name,
            "source": (
                self.source.to_dict() if self.source else None
            ),  # Updated to handle Source object
            "volume": self.volume,
            "pan": self.pan,
            "start_offset": self.start_offset,
            "playrate": self.playrate,
            "volume_db": self.volume_db,
            "gain_staging_flag": self.gain_staging_flag,
        }


@dataclass
class Item:
    """Represents an item (media item) in a track."""

    guid: str
    name: str = "Unnamed Item"
    position: float = 0.0
    length: float = 0.0
    volume: float = 1.0
    pan: float = 0.0
    muted: bool = False
    takes: List[Take] = field(default_factory=list)
    # Analysis flags
    is_empty: bool = False
    has_missing_media: bool = False  # This flag will now be set based on path patterns
    has_sample_rate_mismatch: bool = False
    gain_staging_warning: Optional[str] = None
    # File Reference Analysis Flags
    has_absolute_path: bool = False
    has_system_temp_path: bool = False
    has_external_relative_path: bool = False
    file_reference_issues: List[str] = field(default_factory=list)
    # Added for foundational enhancements (Phase 1)
    fade_in_length: Optional[float] = field(default=None)
    fade_out_length: Optional[float] = field(default=None)
    fade_in_shape: Optional[int] = field(default=None)
    fade_out_shape: Optional[int] = field(default=None)
    is_reversed: bool = field(default=False)
    playrate: float = field(default=1.0)
    pitch_envelope_details: Optional[Any] = field(
        default=None
    )  # Placeholder for potential future detailed parsing
    # Added for Phase 1.2 boundary/fade checks
    has_abrupt_start: bool = field(default=False)
    has_abrupt_end: bool = field(default=False)
    # Added for Phase 1.3 Media Item Properties Analysis
    has_unusual_properties_warning: Optional[str] = field(default=None)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "guid": self.guid,
            "name": self.name,
            "position": self.position,
            "length": self.length,
            "volume": self.volume,
            "pan": self.pan,
            "muted": self.muted,
            "takes": [take.to_dict() for take in self.takes],
            "is_empty": self.is_empty,
            "has_missing_media": self.has_missing_media,
            "has_sample_rate_mismatch": self.has_sample_rate_mismatch,
            "gain_staging_warning": self.gain_staging_warning,
            "has_absolute_path": self.has_absolute_path,
            "has_system_temp_path": self.has_system_temp_path,
            "has_external_relative_path": self.has_external_relative_path,
            "file_reference_issues": self.file_reference_issues,
            # Added for foundational enhancements (Phase 1)
            "fade_in_length": self.fade_in_length,
            "fade_out_length": self.fade_out_length,
            "fade_in_shape": self.fade_in_shape,
            "fade_out_shape": self.fade_out_shape,
            "is_reversed": self.is_reversed,
            "playrate": self.playrate,
            "pitch_envelope_details": self.pitch_envelope_details,
            # Added for Phase 1.2
            "has_abrupt_start": self.has_abrupt_start,
            "has_abrupt_end": self.has_abrupt_end,
            # Added for Phase 1.3
            "has_unusual_properties_warning": self.has_unusual_properties_warning,
        }


class AffectedElement(TypedDict, total=False):
    """Represents an element (track, item, plugin) affected by an issue."""

    type: str  # e.g., "track", "item", "plugin", "project"
    guid: Optional[str]
    name: Optional[str]
    details: Optional[Dict[str, Any]]  # e.g., specific parameter for a plugin


@dataclass
class MasteringAnalysisIssue:
    """Represents a single issue identified during mastering analysis."""

    id: str  # Unique identifier for the issue instance, e.g., "track-guid-blmaster"
    rule_id: str  # Identifier for the rule that generated this issue, e.g., "MASTER_BUS_MULTIPLE_LIMITERS"
    category: str  # Broad category, e.g., "Plugin Analysis", "Master Bus", "Session Hygiene", "File Reference"
    severity: str  # "critical", "warning", "info", "pass"
    message: str  # User-facing message describing the issue
    recommendation: Optional[str] = None  # User-facing recommendation
    is_mastering_critical: bool = True  # True for mastering readiness issues, False for general hygiene
    details_key: Optional[str] = (
        None  # Optional key to link to more detailed data in the main API response
    )
    affected_elements: List[AffectedElement] = field(default_factory=list)
    # Optional: Add a field for specific data related to this issue if not covered by affected_elements
    # issue_specific_data: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        data = {
            "id": self.id,
            "rule_id": self.rule_id,
            "category": self.category,
            "severity": self.severity,
            "message": self.message,
            "is_mastering_critical": self.is_mastering_critical,
            "affected_elements": self.affected_elements,  # affected_elements are already dicts
        }
        if self.recommendation is not None:
            data["recommendation"] = self.recommendation
        if self.details_key is not None:
            data["details_key"] = self.details_key
        return data


@dataclass
class IssueGroup:
    """Represents a group of similar issues for cleaner display."""
    
    rule_id: str  # The common rule that generated these issues
    category: str  # Broad category
    severity: str  # Highest severity among grouped issues
    title: str  # Summary title for the group
    description: str  # Combined description
    count: int  # Number of issues in this group
    is_mastering_critical: bool  # Whether this group affects mastering readiness
    affected_elements: List[AffectedElement] = field(default_factory=list)  # All affected elements
    sample_issue: Optional['MasteringAnalysisIssue'] = None  # Representative issue for details
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        data = {
            "rule_id": self.rule_id,
            "category": self.category,
            "severity": self.severity,
            "title": self.title,
            "description": self.description,
            "count": self.count,
            "is_mastering_critical": self.is_mastering_critical,
            "affected_elements": self.affected_elements,
        }
        if self.sample_issue:
            data["sample_issue"] = self.sample_issue.to_dict()
        return data


@dataclass
class GroupedMasteringAnalysis:
    """Enhanced mastering analysis with grouped issues for better UX."""
    
    overall_health_score: float
    genre: str
    strict_mode: bool
    summary_metrics: Dict[str, Any]
    issue_groups: List[IssueGroup] = field(default_factory=list)  # Grouped issues
    ungrouped_issues: List[MasteringAnalysisIssue] = field(default_factory=list)  # Issues that couldn't be grouped
    detailed_analysis: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "overall_health_score": self.overall_health_score,
            "genre": self.genre,
            "strict_mode": self.strict_mode,
            "summary_metrics": self.summary_metrics,
            "issue_groups": [group.to_dict() for group in self.issue_groups],
            "ungrouped_issues": [issue.to_dict() for issue in self.ungrouped_issues],
            "detailed_analysis": self.detailed_analysis,
        }


@dataclass  
class MasterOutputInfo:
    """Information about master output configuration and validation."""
    
    master_volume: float = 0.0  # Master volume in dB
    master_mute: bool = False  # Whether master is muted
    master_solo: bool = False  # Whether master is soloed
    has_excessive_gain: bool = False  # Volume > +3dB (potential clipping)
    has_insufficient_headroom: bool = False  # Volume < -12dB (potential gain staging issue)
    volume_automation_present: bool = False  # Has volume automation on master
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "master_volume": self.master_volume,
            "master_mute": self.master_mute,
            "master_solo": self.master_solo,
            "has_excessive_gain": self.has_excessive_gain,
            "has_insufficient_headroom": self.has_insufficient_headroom,
            "volume_automation_present": self.volume_automation_present,
        }
