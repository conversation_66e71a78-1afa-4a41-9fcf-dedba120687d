"""
Database module for persistent caching of analysis results.
Uses SQLite for lightweight, file-based storage.
"""
import sqlite3
import json
import hashlib
import time
from typing import Optional, Dict, Any, List
from pathlib import Path
import asyncio
import aiosqlite
from contextlib import asynccontextmanager


class AnalysisCache:
    """
    SQLite-based cache for analysis results with async support.
    Provides persistent storage across application restarts.
    """
    
    def __init__(self, db_path: str = "analysis_cache.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Initialize the database tables if they don't exist."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS analysis_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_hash TEXT UNIQUE NOT NULL,
                    filename TEXT NOT NULL,
                    file_size INTEGER,
                    genre TEXT,
                    strict_mode BOOLEAN,
                    analysis_type TEXT NOT NULL,
                    result_data TEXT NOT NULL,
                    created_at REAL NOT NULL,
                    accessed_at REAL NOT NULL,
                    access_count INTEGER DEFAULT 1
                )
            """)
            
            # Create indexes for better performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_file_hash ON analysis_cache(file_hash)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_analysis_type ON analysis_cache(analysis_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_accessed_at ON analysis_cache(accessed_at)")
            
            conn.commit()
    
    def _calculate_file_hash(self, filename: str, file_size: int, genre: str, strict_mode: bool) -> str:
        """Calculate a unique hash for the file and analysis parameters."""
        content = f"{filename}_{file_size}_{genre}_{strict_mode}"
        return hashlib.sha256(content.encode()).hexdigest()
    
    def get_cached_result(self, filename: str, file_size: int, genre: str, 
                         strict_mode: bool, analysis_type: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached analysis result if available.
        Updates access statistics automatically.
        """
        file_hash = self._calculate_file_hash(filename, file_size, genre, strict_mode)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT result_data FROM analysis_cache 
                WHERE file_hash = ? AND analysis_type = ?
            """, (file_hash, analysis_type))
            
            result = cursor.fetchone()
            if result:
                # Update access statistics
                conn.execute("""
                    UPDATE analysis_cache 
                    SET accessed_at = ?, access_count = access_count + 1
                    WHERE file_hash = ? AND analysis_type = ?
                """, (time.time(), file_hash, analysis_type))
                conn.commit()
                
                try:
                    return json.loads(result[0])
                except json.JSONDecodeError:
                    # If data is corrupted, remove it
                    self._remove_corrupted_entry(file_hash, analysis_type)
                    return None
        
        return None
    
    def cache_result(self, filename: str, file_size: int, genre: str, 
                    strict_mode: bool, analysis_type: str, result_data: Dict[str, Any]) -> bool:
        """
        Cache analysis result for future use.
        Returns True if successfully cached, False otherwise.
        """
        file_hash = self._calculate_file_hash(filename, file_size, genre, strict_mode)
        current_time = time.time()
        
        try:
            result_json = json.dumps(result_data, default=str)
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO analysis_cache 
                    (file_hash, filename, file_size, genre, strict_mode, analysis_type, 
                     result_data, created_at, accessed_at, access_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                """, (file_hash, filename, file_size, genre, strict_mode, analysis_type,
                      result_json, current_time, current_time))
                conn.commit()
                return True
                
        except (json.JSONEncodeError, sqlite3.Error) as e:
            print(f"Failed to cache result: {e}")
            return False
    
    def _remove_corrupted_entry(self, file_hash: str, analysis_type: str):
        """Remove corrupted cache entry."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                DELETE FROM analysis_cache 
                WHERE file_hash = ? AND analysis_type = ?
            """, (file_hash, analysis_type))
            conn.commit()
    
    def cleanup_old_entries(self, max_age_days: int = 30, max_entries: int = 1000):
        """
        Clean up old cache entries to manage database size.
        Removes entries older than max_age_days and keeps only max_entries most recent.
        """
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
        
        with sqlite3.connect(self.db_path) as conn:
            # Remove old entries
            conn.execute("""
                DELETE FROM analysis_cache 
                WHERE accessed_at < ?
            """, (cutoff_time,))
            
            # Keep only the most recently accessed entries
            conn.execute("""
                DELETE FROM analysis_cache 
                WHERE id NOT IN (
                    SELECT id FROM analysis_cache 
                    ORDER BY accessed_at DESC 
                    LIMIT ?
                )
            """, (max_entries,))
            
            deleted_count = conn.total_changes
            conn.commit()
            
        return deleted_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_entries,
                    COUNT(DISTINCT file_hash) as unique_files,
                    AVG(access_count) as avg_access_count,
                    MAX(accessed_at) as last_access,
                    MIN(created_at) as oldest_entry
                FROM analysis_cache
            """)
            
            result = cursor.fetchone()
            if result:
                return {
                    "total_entries": result[0],
                    "unique_files": result[1], 
                    "avg_access_count": round(result[2] or 0, 2),
                    "last_access": result[3],
                    "oldest_entry": result[4]
                }
            
        return {"total_entries": 0, "unique_files": 0, "avg_access_count": 0}


class AsyncAnalysisCache:
    """
    Async version of AnalysisCache for high-performance database operations.
    """
    
    def __init__(self, db_path: str = "analysis_cache.db"):
        self.db_path = db_path
        asyncio.create_task(self._init_database())
    
    async def _init_database(self):
        """Initialize the database tables asynchronously."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS analysis_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_hash TEXT UNIQUE NOT NULL,
                    filename TEXT NOT NULL,
                    file_size INTEGER,
                    genre TEXT,
                    strict_mode BOOLEAN,
                    analysis_type TEXT NOT NULL,
                    result_data TEXT NOT NULL,
                    created_at REAL NOT NULL,
                    accessed_at REAL NOT NULL,
                    access_count INTEGER DEFAULT 1
                )
            """)
            
            await db.execute("CREATE INDEX IF NOT EXISTS idx_file_hash ON analysis_cache(file_hash)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_analysis_type ON analysis_cache(analysis_type)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_accessed_at ON analysis_cache(accessed_at)")
            
            await db.commit()
    
    def _calculate_file_hash(self, filename: str, file_size: int, genre: str, strict_mode: bool) -> str:
        """Calculate a unique hash for the file and analysis parameters."""
        content = f"{filename}_{file_size}_{genre}_{strict_mode}"
        return hashlib.sha256(content.encode()).hexdigest()
    
    async def get_cached_result(self, filename: str, file_size: int, genre: str, 
                               strict_mode: bool, analysis_type: str) -> Optional[Dict[str, Any]]:
        """Async version of cache retrieval."""
        file_hash = self._calculate_file_hash(filename, file_size, genre, strict_mode)
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT result_data FROM analysis_cache 
                WHERE file_hash = ? AND analysis_type = ?
            """, (file_hash, analysis_type))
            
            result = await cursor.fetchone()
            if result:
                # Update access statistics
                await db.execute("""
                    UPDATE analysis_cache 
                    SET accessed_at = ?, access_count = access_count + 1
                    WHERE file_hash = ? AND analysis_type = ?
                """, (time.time(), file_hash, analysis_type))
                await db.commit()
                
                try:
                    return json.loads(result[0])
                except json.JSONDecodeError:
                    await self._remove_corrupted_entry(file_hash, analysis_type)
                    return None
        
        return None
    
    async def cache_result(self, filename: str, file_size: int, genre: str, 
                          strict_mode: bool, analysis_type: str, result_data: Dict[str, Any]) -> bool:
        """Async version of result caching."""
        file_hash = self._calculate_file_hash(filename, file_size, genre, strict_mode)
        current_time = time.time()
        
        try:
            result_json = json.dumps(result_data, default=str)
            
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT OR REPLACE INTO analysis_cache 
                    (file_hash, filename, file_size, genre, strict_mode, analysis_type, 
                     result_data, created_at, accessed_at, access_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                """, (file_hash, filename, file_size, genre, strict_mode, analysis_type,
                      result_json, current_time, current_time))
                await db.commit()
                return True
                
        except (json.JSONEncodeError, aiosqlite.Error) as e:
            print(f"Failed to cache result: {e}")
            return False
    
    async def _remove_corrupted_entry(self, file_hash: str, analysis_type: str):
        """Remove corrupted cache entry asynchronously."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                DELETE FROM analysis_cache 
                WHERE file_hash = ? AND analysis_type = ?
            """, (file_hash, analysis_type))
            await db.commit()


# Global cache instance
_cache_instance = None

def get_cache() -> AnalysisCache:
    """Get the global cache instance (singleton pattern)."""
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = AnalysisCache()
    return _cache_instance

def get_async_cache() -> AsyncAnalysisCache:
    """Get the global async cache instance."""
    global _async_cache_instance
    if not hasattr(get_async_cache, '_instance'):
        get_async_cache._instance = AsyncAnalysisCache()
    return get_async_cache._instance