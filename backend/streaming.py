"""
Streaming response module for handling large analysis operations.
Provides real-time progress updates and chunked data delivery.
"""
import asyncio
import j<PERSON>
from typing import Async<PERSON>enerator, Dict, Any, List, Optional
from fastapi import HTTPException
from fastapi.responses import StreamingResponse
from parsing.engines.mastering_analysis_engine import MasteringAnalysisEngine
from parsing.models import ParsedSession


class AnalysisStreamer:
    """
    Handles streaming analysis operations with progress updates.
    """
    
    def __init__(self):
        self.active_streams: Dict[str, asyncio.Task] = {}
    
    async def stream_mastering_analysis(
        self, 
        parsed_session: ParsedSession,
        genre: str = "general",
        strict_mode: bool = False,
        stream_id: str = None
    ) -> AsyncGenerator[str, None]:
        """
        Stream mastering analysis with progress updates.
        Yields JSON chunks with progress and partial results.
        """
        try:
            # Initialize the analysis engine
            engine = MasteringAnalysisEngine(parsed_session, genre, strict_mode)
            
            # Send initial status
            yield self._format_chunk({
                "type": "progress",
                "stage": "initialization",
                "progress": 0,
                "message": "Starting mastering analysis..."
            })
            
            # Perform analysis in stages with progress updates
            stages = [
                ("plugin_issues", "Analyzing plugins", 15),
                ("master_bus", "Analyzing master bus", 25),
                ("master_output", "Checking master output", 35),
                ("offline_media", "Detecting offline media", 45),
                ("sample_rates", "Checking sample rates", 55),
                ("playrates", "Analyzing playrates", 65),
                ("record_arms", "Checking record arms", 75),
                ("hygiene", "Session hygiene analysis", 85),
                ("finalization", "Finalizing results", 95)
            ]
            
            # Reset issues for fresh analysis
            engine.issues = []
            engine.detailed_analysis = {}
            
            for stage_name, stage_message, progress in stages:
                yield self._format_chunk({
                    "type": "progress",
                    "stage": stage_name,
                    "progress": progress,
                    "message": stage_message
                })
                
                # Perform the actual analysis step
                await self._execute_analysis_stage(engine, stage_name)
                
                # Send partial results for some stages
                if stage_name in ["plugin_issues", "master_bus", "hygiene"]:
                    yield self._format_chunk({
                        "type": "partial_result",
                        "stage": stage_name,
                        "issues_count": len(engine.issues),
                        "recent_issues": [issue.to_dict() for issue in engine.issues[-5:]]  # Last 5 issues
                    })
                
                # Small delay to allow for UI updates
                await asyncio.sleep(0.1)
            
            # Calculate final metrics
            summary_metrics = engine._calculate_summary_metrics()
            overall_health_score = engine._calculate_overall_health_score(summary_metrics)
            
            # Send final results
            yield self._format_chunk({
                "type": "complete",
                "progress": 100,
                "result": {
                    "overall_health_score": overall_health_score,
                    "genre": genre,
                    "strict_mode": strict_mode,
                    "summary_metrics": summary_metrics,
                    "issues": [issue.to_dict() for issue in engine.issues],
                    "detailed_analysis": engine.detailed_analysis,
                }
            })
            
        except Exception as e:
            yield self._format_chunk({
                "type": "error",
                "message": f"Analysis failed: {str(e)}"
            })
    
    async def _execute_analysis_stage(self, engine: MasteringAnalysisEngine, stage_name: str):
        """Execute a specific analysis stage asynchronously."""
        if stage_name == "plugin_issues":
            await asyncio.to_thread(engine._analyse_plugin_issues)
        elif stage_name == "master_bus":
            await asyncio.to_thread(engine._analyse_master_bus_chain)
        elif stage_name == "master_output":
            await asyncio.to_thread(engine._analyse_master_output)
        elif stage_name == "offline_media":
            await asyncio.to_thread(engine._analyse_offline_media)
        elif stage_name == "sample_rates":
            await asyncio.to_thread(engine._analyse_sample_rate_consistency)
        elif stage_name == "playrates":
            await asyncio.to_thread(engine._analyse_time_stretch_and_playrate)
        elif stage_name == "record_arms":
            await asyncio.to_thread(engine._analyse_record_arm_safety)
        elif stage_name == "hygiene":
            await asyncio.to_thread(engine._analyse_session_hygiene)
            await asyncio.to_thread(engine._analyse_file_references)
            await asyncio.to_thread(engine._analyse_item_properties)
            await asyncio.to_thread(engine._analyse_project_settings)
            await asyncio.to_thread(engine._analyse_track_specific_issues)
    
    def _format_chunk(self, data: Dict[str, Any]) -> str:
        """Format data as Server-Sent Events chunk."""
        json_data = json.dumps(data, default=str)
        return f"data: {json_data}\n\n"
    
    async def stream_grouped_analysis(
        self,
        parsed_session: ParsedSession,
        genre: str = "general", 
        strict_mode: bool = False
    ) -> AsyncGenerator[str, None]:
        """Stream grouped analysis with real-time grouping updates."""
        try:
            engine = MasteringAnalysisEngine(parsed_session, genre, strict_mode)
            
            yield self._format_chunk({
                "type": "progress",
                "stage": "analysis", 
                "progress": 0,
                "message": "Starting analysis..."
            })
            
            # Run regular analysis first
            await asyncio.to_thread(engine.analyse)
            
            yield self._format_chunk({
                "type": "progress",
                "stage": "grouping",
                "progress": 80,
                "message": "Grouping issues..."
            })
            
            # Group the issues
            issue_groups, ungrouped_issues = await asyncio.to_thread(
                engine._group_issues, engine.issues
            )
            
            # Send final grouped result
            summary_metrics = engine._calculate_summary_metrics()
            overall_health_score = engine._calculate_overall_health_score(summary_metrics)
            
            yield self._format_chunk({
                "type": "complete",
                "progress": 100,
                "result": {
                    "overall_health_score": overall_health_score,
                    "genre": genre,
                    "strict_mode": strict_mode,
                    "summary_metrics": summary_metrics,
                    "issue_groups": [group.to_dict() for group in issue_groups],
                    "ungrouped_issues": [issue.to_dict() for issue in ungrouped_issues],
                    "detailed_analysis": engine.detailed_analysis,
                }
            })
            
        except Exception as e:
            yield self._format_chunk({
                "type": "error",
                "message": f"Grouped analysis failed: {str(e)}"
            })


class BackgroundTaskManager:
    """
    Manages background analysis tasks for heavy operations.
    """
    
    def __init__(self):
        self.tasks: Dict[str, asyncio.Task] = {}
        self.results: Dict[str, Any] = {}
    
    async def start_background_analysis(
        self,
        task_id: str,
        parsed_session: ParsedSession,
        genre: str = "general",
        strict_mode: bool = False
    ) -> str:
        """Start a background analysis task."""
        if task_id in self.tasks and not self.tasks[task_id].done():
            raise HTTPException(status_code=409, detail="Task already running")
        
        # Create and start the background task
        task = asyncio.create_task(
            self._run_background_analysis(task_id, parsed_session, genre, strict_mode)
        )
        self.tasks[task_id] = task
        
        return task_id
    
    async def _run_background_analysis(
        self,
        task_id: str,
        parsed_session: ParsedSession,
        genre: str,
        strict_mode: bool
    ):
        """Run analysis in the background."""
        try:
            engine = MasteringAnalysisEngine(parsed_session, genre, strict_mode)
            result = await asyncio.to_thread(engine.analyse)
            
            self.results[task_id] = {
                "status": "completed",
                "result": result,
                "completed_at": asyncio.get_event_loop().time()
            }
            
        except Exception as e:
            self.results[task_id] = {
                "status": "failed",
                "error": str(e),
                "failed_at": asyncio.get_event_loop().time()
            }
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of a background task."""
        if task_id not in self.tasks:
            return {"status": "not_found"}
        
        task = self.tasks[task_id]
        
        if task_id in self.results:
            return self.results[task_id]
        elif task.done():
            return {"status": "completed_no_result"}
        else:
            return {"status": "running"}
    
    def cleanup_completed_tasks(self, max_age_seconds: int = 3600):
        """Clean up old completed tasks."""
        current_time = asyncio.get_event_loop().time()
        
        to_remove = []
        for task_id, result in self.results.items():
            if "completed_at" in result or "failed_at" in result:
                result_time = result.get("completed_at", result.get("failed_at", 0))
                if current_time - result_time > max_age_seconds:
                    to_remove.append(task_id)
        
        for task_id in to_remove:
            if task_id in self.tasks:
                del self.tasks[task_id]
            if task_id in self.results:
                del self.results[task_id]
        
        return len(to_remove)


# Global instances
analysis_streamer = AnalysisStreamer()
background_manager = BackgroundTaskManager()


def create_streaming_response(generator: AsyncGenerator[str, None]) -> StreamingResponse:
    """Create a Server-Sent Events streaming response."""
    return StreamingResponse(
        generator,
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )