from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Query
from fastapi.responses import JSONResponse, FileResponse, PlainTextResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from typing import List, Dict, Any
import os
import uuid
import asyncio

# Import the parser_wrapper module
import parser_wrapper
from streaming import analysis_streamer, background_manager, create_streaming_response
from database import get_cache
from connection_pool import get_async_cache_manager, performance_monitor

app = FastAPI(title="SessionView API", version="0.1.0")

# Add GZip compression middleware for large responses
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Configure CORS to allow requests from the frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
    ],  # Default Vite dev server ports
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from typing import List, Dict, Any, Optional  # Added Optional
from parsing.models import ParsedSession  # Import ParsedSession
from parsing.engines.mastering_analysis_engine import (
    MasteringAnalysisEngine,
)  # Import the new engine
from parsing.genre_rules import (
    Genre,
)  # Import Genre enum

# Placeholder for storing the last parsed session data
# This will now store the ParsedSession object directly.
session_data_store: Optional[ParsedSession] = (
    None  # Changed type hint and added Optional
)


@app.post("/api/v1/upload")
async def upload_rpp(file: UploadFile = File(...)):
    """
    Receives an RPP file, attempts to parse it, stores the result,
    and returns the parsed session summary.
    """
    if not file.filename.lower().endswith(".rpp"):
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Please upload an .rpp or .RPP file.",
        )

    try:
        # Read the uploaded file and parse its contents
        contents = await file.read()
        parsed_data = parser_wrapper.parse_rpp_content(contents, filename=file.filename)

        # Store the parsed data (now a ParsedSession object)
        # This assumes parser_wrapper.parse_rpp_content is updated to return ParsedSession object
        global session_data_store
        session_data_store = parsed_data

        # Return the dictionary representation for the API response
        return JSONResponse(content=parsed_data.to_dict(), status_code=200)

    except Exception as e:
        # Log the exception e
        raise HTTPException(
            status_code=500, detail=f"Failed to parse RPP file: {str(e)}"
        )
    finally:
        await file.close()


@app.get("/api/v1/summary", summary="Get summary of the last uploaded session")
async def get_summary():
    """
    Returns the summary data from the most recently uploaded and parsed RPP file.
    """
    if not session_data_store:
        raise HTTPException(
            status_code=404,
            detail="No session data available. Please upload an RPP file first.",
        )
    # Return the dictionary representation
    return JSONResponse(content=session_data_store.to_dict())


@app.post("/api/v1/export/fxchain", summary="Export FX chain")
async def export_fxchain(track_indices: List[int]):  # Or track names/GUIDs
    """
    Exports an FX chain (.RfxChain) for the specified track(s).
    (Placeholder - requires actual parsing and RfxChain generation)
    """
    if not session_data_store:
        raise HTTPException(status_code=404, detail="No session data available.")
    # Placeholder: Generate a dummy RfxChain file content
    # Accessing tracks from ParsedSession object
    dummy_content = "<REAPER_FXCHAIN_TEMPLATE>"
    for index in track_indices:
        if session_data_store and 0 <= index < len(
            session_data_store.tracks
        ):  # Check if session_data_store is not None
            track_name = session_data_store.tracks[index].name
            dummy_content += f'\n  <FXCHAIN TRACK="{track_name}">\n    // FX data for {track_name}\n  </FXCHAIN>'
        else:
            return JSONResponse(
                content={"detail": f"Invalid track index: {index}"}, status_code=400
            )
    dummy_content += "\n</REAPER_FXCHAIN_TEMPLATE>"

    return PlainTextResponse(
        dummy_content,
        media_type="application/octet-stream",
        headers={"Content-Disposition": "attachment; filename=exported.RfxChain"},
    )


@app.post("/api/v1/export/tracktemplate", summary="Export track template")
async def export_tracktemplate(track_indices: List[int]):  # Or track names/GUIDs
    """
    Exports selected tracks as a .TrackTemplate file.
    (Placeholder - requires actual parsing and TrackTemplate generation)
    """
    if not session_data_store:
        raise HTTPException(status_code=404, detail="No session data available.")
    # Placeholder: Generate dummy TrackTemplate content
    # Accessing tracks from ParsedSession object
    dummy_content = "<REAPER_TRACK_TEMPLATE>"
    for index in track_indices:
        if session_data_store and 0 <= index < len(
            session_data_store.tracks
        ):  # Check if session_data_store is not None
            track_name = session_data_store.tracks[index].name
            dummy_content += f'\n  <TRACK NAME="{track_name}">\n    // Track data for {track_name}\n  </TRACK>'
        else:
            return JSONResponse(
                content={"detail": f"Invalid track index: {index}"}, status_code=400
            )
    dummy_content += "\n</REAPER_TRACK_TEMPLATE>"

    return PlainTextResponse(
        dummy_content,
        media_type="application/octet-stream",
        headers={"Content-Disposition": "attachment; filename=exported.TrackTemplate"},
    )


@app.post("/api/v1/export/markdown", summary="Export session summary as Markdown")
async def export_markdown():
    """
    Generates and returns a Markdown summary of the session.
    """
    if not session_data_store:
        raise HTTPException(status_code=404, detail="No session data available.")

    # Use the enhanced markdown generator from parser_wrapper
    # generate_markdown expects a dictionary, so convert ParsedSession object
    md_content = parser_wrapper.generate_markdown(session_data_store.to_dict())

    return PlainTextResponse(
        md_content,
        media_type="text/markdown",
        headers={"Content-Disposition": "attachment; filename=summary.md"},
    )


# from parsing.genre_rules import Genre # This import is now covered by the one at the top for GenreRules, Genre


@app.post("/api/v1/export/pdf", summary="Export session summary as PDF")
async def export_pdf():
    """
    Generates and returns a PDF report of the session.
    (Placeholder - requires Markdown generation and PDF conversion library)
    """
    # This would involve:
    # 1. Generating Markdown content (similar to export_markdown)
    # 2. Using a library (WeasyPrint, pdfkit, reportlab) to convert MD/HTML to PDF
    # 3. Returning a FileResponse
    raise HTTPException(status_code=501, detail="PDF export not implemented yet.")


@app.get(
    "/api/v1/mastering-analysis",
    summary="Get 'Ready for Mastering' analysis of the last uploaded session",
)
async def get_mastering_analysis(
    genre: Genre = "general", 
    strict_mode: bool = False,
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of issues to return"),
    offset: int = Query(0, ge=0, description="Number of issues to skip"),
    fields: str = Query(None, description="Comma-separated list of fields to include (issues,detailed_analysis,summary_metrics)")
):
    """
    Performs a comprehensive 'Ready for Mastering' analysis on the last uploaded session
    using the MasteringAnalysisEngine and returns the results with optional pagination and field selection.
    """
    if not session_data_store:
        raise HTTPException(
            status_code=404,
            detail="No session data available. Please upload an RPP file first.",
        )

    try:
        # Instantiate the MasteringAnalysisEngine with the stored ParsedSession object
        engine = MasteringAnalysisEngine(
            parsed_session=session_data_store,
            genre=genre,
            strict_mode=strict_mode,
        )

        # Perform the analysis
        analysis_report = engine.analyse()
        
        # Apply pagination to issues if present
        if "issues" in analysis_report and isinstance(analysis_report["issues"], list):
            total_issues = len(analysis_report["issues"])
            paginated_issues = analysis_report["issues"][offset:offset + limit]
            analysis_report["issues"] = paginated_issues
            analysis_report["pagination"] = {
                "total": total_issues,
                "limit": limit,
                "offset": offset,
                "returned": len(paginated_issues)
            }
        
        # Apply field selection if specified
        if fields:
            requested_fields = set(field.strip() for field in fields.split(","))
            filtered_report = {}
            
            # Always include core fields
            for core_field in ["overall_health_score", "genre", "strict_mode", "summary_metrics"]:
                if core_field in analysis_report:
                    filtered_report[core_field] = analysis_report[core_field]
            
            # Include requested fields
            for field in requested_fields:
                if field in analysis_report:
                    filtered_report[field] = analysis_report[field]
            
            # Include pagination info if present
            if "pagination" in analysis_report:
                filtered_report["pagination"] = analysis_report["pagination"]
                
            analysis_report = filtered_report

        return JSONResponse(content=analysis_report, status_code=200)

    except Exception as e:
        # Log the exception for debugging
        # import traceback
        # print(f"Error during mastering analysis: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(
            status_code=500, detail=f"Failed to perform mastering analysis: {str(e)}"
        )


@app.get(
    "/api/v1/mastering-analysis-grouped",
    summary="Get 'Ready for Mastering' analysis with grouped issues for better UX",
)
async def get_mastering_analysis_grouped(
    genre: Genre = "general", 
    strict_mode: bool = False,
    group_limit: int = Query(50, ge=1, le=200, description="Maximum number of issue groups to return"),
    group_offset: int = Query(0, ge=0, description="Number of issue groups to skip"),
    ungrouped_limit: int = Query(50, ge=1, le=200, description="Maximum number of ungrouped issues to return"),
    fields: str = Query(None, description="Comma-separated list of fields to include")
):
    """
    Performs a comprehensive 'Ready for Mastering' analysis on the last uploaded session
    using the MasteringAnalysisEngine and returns grouped results with pagination for better UX.
    """
    if not session_data_store:
        raise HTTPException(
            status_code=404,
            detail="No session data available. Please upload an RPP file first.",
        )

    try:
        # Instantiate the MasteringAnalysisEngine with the stored ParsedSession object
        engine = MasteringAnalysisEngine(
            parsed_session=session_data_store,
            genre=genre,
            strict_mode=strict_mode,
        )

        # Perform the grouped analysis
        analysis_report = engine.analyse_grouped()
        
        # Apply pagination to issue groups
        if "issue_groups" in analysis_report and isinstance(analysis_report["issue_groups"], list):
            total_groups = len(analysis_report["issue_groups"])
            paginated_groups = analysis_report["issue_groups"][group_offset:group_offset + group_limit]
            analysis_report["issue_groups"] = paginated_groups
            
            # Add pagination metadata
            if "pagination" not in analysis_report:
                analysis_report["pagination"] = {}
            analysis_report["pagination"]["groups"] = {
                "total": total_groups,
                "limit": group_limit,
                "offset": group_offset,
                "returned": len(paginated_groups)
            }
        
        # Apply pagination to ungrouped issues
        if "ungrouped_issues" in analysis_report and isinstance(analysis_report["ungrouped_issues"], list):
            total_ungrouped = len(analysis_report["ungrouped_issues"])
            paginated_ungrouped = analysis_report["ungrouped_issues"][:ungrouped_limit]
            analysis_report["ungrouped_issues"] = paginated_ungrouped
            
            # Add pagination metadata
            if "pagination" not in analysis_report:
                analysis_report["pagination"] = {}
            analysis_report["pagination"]["ungrouped"] = {
                "total": total_ungrouped,
                "limit": ungrouped_limit,
                "returned": len(paginated_ungrouped)
            }
        
        # Apply field selection if specified
        if fields:
            requested_fields = set(field.strip() for field in fields.split(","))
            filtered_report = {}
            
            # Always include core fields
            for core_field in ["overall_health_score", "genre", "strict_mode", "summary_metrics"]:
                if core_field in analysis_report:
                    filtered_report[core_field] = analysis_report[core_field]
            
            # Include requested fields
            for field in requested_fields:
                if field in analysis_report:
                    filtered_report[field] = analysis_report[field]
            
            # Include pagination info if present
            if "pagination" in analysis_report:
                filtered_report["pagination"] = analysis_report["pagination"]
                
            analysis_report = filtered_report

        return JSONResponse(content=analysis_report, status_code=200)

    except Exception as e:
        # Log the exception for debugging
        # import traceback
        # print(f"Error during grouped mastering analysis: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(
            status_code=500, detail=f"Failed to perform grouped mastering analysis: {str(e)}"
        )


# Add a simple root endpoint for testing
@app.get("/")
async def root():
    return {"message": "SessionView API is running"}


# === Phase 4: Streaming and Background Task Endpoints ===

@app.get("/api/v1/stream/mastering-analysis")
async def stream_mastering_analysis(
    genre: str = Query("general", description="Music genre for analysis rules"),
    strict_mode: bool = Query(False, description="Enable strict analysis mode")
):
    """
    Stream mastering analysis with real-time progress updates.
    Returns Server-Sent Events for real-time UI updates.
    """
    if session_data_store is None:
        raise HTTPException(status_code=400, detail="No session data available. Upload a file first.")
    
    return create_streaming_response(
        analysis_streamer.stream_mastering_analysis(
            session_data_store, genre, strict_mode
        )
    )


@app.get("/api/v1/stream/grouped-analysis")
async def stream_grouped_analysis(
    genre: str = Query("general", description="Music genre for analysis rules"),
    strict_mode: bool = Query(False, description="Enable strict analysis mode")
):
    """
    Stream grouped analysis with real-time progress updates.
    """
    if session_data_store is None:
        raise HTTPException(status_code=400, detail="No session data available. Upload a file first.")
    
    return create_streaming_response(
        analysis_streamer.stream_grouped_analysis(
            session_data_store, genre, strict_mode
        )
    )


@app.post("/api/v1/background/mastering-analysis")
async def start_background_analysis(
    genre: str = Query("general", description="Music genre for analysis rules"),
    strict_mode: bool = Query(False, description="Enable strict analysis mode")
):
    """
    Start a background mastering analysis task.
    Returns a task ID for checking progress.
    """
    if session_data_store is None:
        raise HTTPException(status_code=400, detail="No session data available. Upload a file first.")
    
    task_id = str(uuid.uuid4())
    
    await background_manager.start_background_analysis(
        task_id, session_data_store, genre, strict_mode
    )
    
    return {"task_id": task_id, "status": "started"}


@app.get("/api/v1/background/task/{task_id}")
async def get_task_status(task_id: str):
    """
    Get the status of a background analysis task.
    """
    status = background_manager.get_task_status(task_id)
    return status


@app.get("/api/v1/cache/stats")
async def get_cache_stats():
    """
    Get database cache statistics for monitoring.
    """
    cache = get_cache()
    stats = cache.get_cache_stats()
    return stats


@app.post("/api/v1/cache/cleanup")
async def cleanup_cache(
    max_age_days: int = Query(30, description="Maximum age in days for cache entries"),
    max_entries: int = Query(1000, description="Maximum number of entries to keep")
):
    """
    Clean up old cache entries to manage database size.
    """
    cache = get_cache()
    deleted_count = cache.cleanup_old_entries(max_age_days, max_entries)
    
    # Also cleanup background tasks
    task_cleanup_count = background_manager.cleanup_completed_tasks(max_age_days * 24 * 60 * 60)
    
    return {
        "cache_entries_deleted": deleted_count,
        "background_tasks_cleaned": task_cleanup_count
    }


@app.get("/api/v1/performance/stats")
async def get_performance_stats():
    """
    Get performance statistics for database operations.
    """
    return {
        "database_performance": performance_monitor.get_performance_stats(),
        "background_tasks": {
            "active_tasks": len(background_manager.tasks),
            "completed_results": len(background_manager.results)
        }
    }


@app.get("/api/v1/cache/detailed-stats")
async def get_detailed_cache_stats():
    """
    Get comprehensive cache statistics with breakdown by type.
    """
    async_cache = await get_async_cache_manager()
    return await async_cache.get_detailed_stats()


@app.post("/api/v1/cache/optimize")
async def optimize_database():
    """
    Run database optimization operations (ANALYZE, VACUUM).
    """
    async_cache = await get_async_cache_manager()
    
    start_time = asyncio.get_event_loop().time()
    await async_cache.optimize_database()
    duration = asyncio.get_event_loop().time() - start_time
    
    return {
        "status": "completed",
        "optimization_time_seconds": round(duration, 2)
    }


# To run the server (from the backend directory):
# source .venv/bin/activate
# uvicorn main:app --reload
