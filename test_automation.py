#!/usr/bin/env python3
"""
Test script to verify the automation envelope extraction from RPP files.
"""
import sys
import os
import json
from backend.parsing.core import parse_rpp_file


def print_track_automation(rpp_path):
    """Print all automation lanes in tracks from the given RPP file."""
    try:
        # Parse the file
        data = parse_rpp_file(rpp_path)
        track_data = data["tracks"]

        # Print results
        print(f"Analyzed file: {os.path.basename(rpp_path)}")
        print(f"Found {len(track_data)} tracks")

        for track in track_data:
            print(f"\nTrack: {track['name']}")
            if track["automation_lanes"]:
                print("  Automation lanes:")
                for lane in track["automation_lanes"]:
                    print(
                        f"    - {lane['display_name']} (Points: {lane['effective_points']})"
                    )
            else:
                print("  No automation lanes found.")

        return True
    except Exception as e:
        print(f"Error during parsing: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


def print_fx_details(rpp_path):
    """Print detailed FX information from tracks in the given RPP file."""
    try:
        data = parse_rpp_file(rpp_path)
        tracks = data.get("tracks", [])

        print(f"\n--- FX Details Analysis ---")
        print(f"Analyzed file: {os.path.basename(rpp_path)}")
        print(f"Found {len(tracks)} tracks")

        for track in tracks:
            print(f"\nTrack: {track['name']} (GUID: {track['guid']})")
            fx_list = track.get("fx", [])
            if fx_list:
                print(f"  Found {len(fx_list)} FX plugins:")
                for fx_item in fx_list:
                    print(f"    - Name: {fx_item['name']}")
                    print(f"      Type: {fx_item['plugin_type']}")
                    if fx_item.get("plugin_guid"):
                        print(f"      GUID: {fx_item['plugin_guid']}")

                    encoded_params = fx_item.get(
                        "encoded_params"
                    )  # This is not in to_dict by default
                    # To access encoded_params, we'd need to modify FX.to_dict or parse differently for debug
                    # For now, we'll assume it's not directly available in the dict from parse_rpp_file
                    # We will print decoded_params which *are* available if populated.

                    # The FX model's to_dict includes specific decoded params if they exist
                    # Let's list them out if present
                    specific_decoded = {
                        k: v
                        for k, v in fx_item.items()
                        if k
                        not in [
                            "name",
                            "plugin_type",
                            "plugin_guid",
                            "has_oversampling",
                            "oversampling_rate",
                            "is_bypassed",
                            "is_blacklisted_general",
                            "is_blacklisted_master",
                            "is_finalizer_plugin",
                            "oversampling_recommended",
                            "oversampling_enabled",
                        ]
                    }
                    if specific_decoded:
                        print(f"      Specific Decoded Params: {specific_decoded}")

                    # To print the full decoded_params dict, it would need to be added to FX.to_dict()
                    # For example:
                    # if 'decoded_params_full' in fx_item: # Assuming we add it to to_dict with this key
                    #    print(f"      Full Decoded Params: {fx_item['decoded_params_full']}")

                    # For encoded_params length, this test script would need to access the model objects
                    # directly, not the dicts. Or, add 'encoded_params_length' to FX.to_dict().
                    # As a simple check, we can see if any specific decoded params were populated.
                    if not specific_decoded:
                        print(
                            f"      No specific decoded parameters found in API dict."
                        )

            else:
                print("  No FX plugins found.")
        return True
    except Exception as e:
        print(f"Error during FX details parsing: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Test file path
    # Adjusted path to be relative to the 'backend' dir, as the script is called from there in the chained command
    # Temporarily changed to Mid24 No1 Mix.RPP to test ProL2Decoder
    rpp_path = "../tests/fixtures/Mid24 No1 Mix.RPP"

    if not os.path.exists(rpp_path):
        print(f"Error: Test file not found at {rpp_path}")
        sys.exit(1)

    print("--- Running Track Automation Test ---")
    success_automation = print_track_automation(rpp_path)

    print("\n--- Running FX Details Test ---")
    success_fx = print_fx_details(rpp_path)

    # Exit with error if any test failed
    sys.exit(0 if (success_automation and success_fx) else 1)
