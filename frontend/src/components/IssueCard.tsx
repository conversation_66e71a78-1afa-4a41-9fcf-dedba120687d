import React from "react";
import { AlertTriangle, CheckCircle, Info, XCircle } from "lucide-react";
import { MasteringAnalysisIssue } from "../store/sessionStore"; // Assuming this type is defined in store

interface IssueCardProps {
    issue: MasteringAnalysisIssue;
}

const IssueCard: React.FC<IssueCardProps> = React.memo(({ issue }) => {
    const severityDetails = React.useMemo(() => {
        switch (issue.severity) {
            case "critical":
                return {
                    bgColor: "bg-red-900",
                    borderColor: "border-red-700",
                    textColor: "text-red-200",
                    icon: (
                        <XCircle
                            className="text-red-400 mr-3 flex-shrink-0"
                            size={22}
                        />
                    ),
                    titleColor: "text-red-300",
                };
            case "warning":
                return {
                    bgColor: "bg-yellow-900",
                    borderColor: "border-yellow-700",
                    textColor: "text-yellow-200",
                    icon: (
                        <AlertTriangle
                            className="text-yellow-400 mr-3 flex-shrink-0"
                            size={22}
                        />
                    ),
                    titleColor: "text-yellow-300",
                };
            case "info":
                return {
                    bgColor: "bg-blue-900",
                    borderColor: "border-blue-700",
                    textColor: "text-blue-200",
                    icon: (
                        <Info
                            className="text-blue-400 mr-3 flex-shrink-0"
                            size={22}
                        />
                    ),
                    titleColor: "text-blue-300",
                };
            case "pass":
                return {
                    bgColor: "bg-green-900",
                    borderColor: "border-green-700",
                    textColor: "text-green-200",
                    icon: (
                        <CheckCircle
                            className="text-green-400 mr-3 flex-shrink-0"
                            size={22}
                        />
                    ),
                    titleColor: "text-green-300",
                };
            default:
                return {
                    bgColor: "bg-gray-700",
                    borderColor: "border-gray-600",
                    textColor: "text-gray-300",
                    icon: (
                        <Info
                            className="text-gray-400 mr-3 flex-shrink-0"
                            size={22}
                        />
                    ),
                    titleColor: "text-gray-400",
                };
        }
    }, [issue.severity]);

    // Memoize affected elements rendering
    const affectedElementsContent = React.useMemo(() => {
        if (!issue.affected_elements || issue.affected_elements.length === 0) {
            return null;
        }
        
        return (
            <div className={`text-xs ${severityDetails.textColor} opacity-80 mt-2`}>
                <strong>Affected:</strong>{" "}
                {issue.affected_elements.map((element, index) => (
                    <span key={index}>
                        {element.name || element.type}
                        {index < issue.affected_elements!.length - 1 ? ", " : ""}
                    </span>
                ))}
            </div>
        );
    }, [issue.affected_elements, severityDetails.textColor]);

    return (
        <div
            className={`p-4 rounded-lg border ${severityDetails.borderColor} ${severityDetails.bgColor} shadow-md hover:shadow-lg transition-shadow duration-200`}
        >
            <div className="flex items-start">
                {severityDetails.icon}
                <div className="flex-grow">
                    <h4
                        className={`text-md font-semibold ${severityDetails.titleColor} mb-1`}
                    >
                        {issue.category}: {issue.message}
                    </h4>
                    {issue.recommendation && (
                        <p
                            className={`text-xs ${severityDetails.textColor} opacity-90 mb-2`}
                        >
                            <strong>Recommendation:</strong>{" "}
                            {issue.recommendation}
                        </p>
                    )}
                    {affectedElementsContent}
                    {issue.rule_id && (
                        <div
                            className={`text-xs ${severityDetails.textColor} opacity-60 mt-1`}
                        >
                            Rule: {issue.rule_id}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
});

export default IssueCard;
