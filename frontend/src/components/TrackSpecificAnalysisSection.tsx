import React from "react";
import AnalysisCard from "./AnalysisCard";
import IssueCard from "./IssueCard";
import { useSessionStore, MasteringAnalysisIssue } from "../store/sessionStore";

const TrackSpecificAnalysisSection: React.FC = () => {
    const { sessionData } = useSessionStore();

    if (!sessionData?.masteringAnalysis) {
        return (
            <AnalysisCard title="Track-Specific Analysis" status="pass">
                <div className="text-gray-600">
                    No mastering analysis data available.
                </div>
            </AnalysisCard>
        );
    }

    const masteringAnalysis = sessionData.masteringAnalysis;

    // Filter issues related to track settings
    const trackSpecificIssues = masteringAnalysis.issues.filter(
        (issue: MasteringAnalysisIssue) => issue.category === "Track Settings"
    );

    // Get detailed track-specific analysis data
    const trackSpecificAnalysisDetails =
        masteringAnalysis.detailed_analysis?.track_specific_issues || [];

    const getOverallStatus = () => {
        const hasCritical = trackSpecificIssues.some(
            (issue) => issue.severity === "critical"
        );
        const hasWarning = trackSpecificIssues.some(
            (issue) => issue.severity === "warning"
        );

        if (hasCritical) return "fail";
        if (hasWarning) return "warning";
        if (trackSpecificIssues.length > 0) return "info";
        return "pass";
    };

    const groupIssuesBySeverity = () => {
        const grouped = {
            critical: trackSpecificIssues.filter(
                (issue) => issue.severity === "critical"
            ),
            warning: trackSpecificIssues.filter(
                (issue) => issue.severity === "warning"
            ),
            info: trackSpecificIssues.filter(
                (issue) => issue.severity === "info"
            ),
        };
        return grouped;
    };

    const groupedIssues = groupIssuesBySeverity();
    const hasAnyIssues = trackSpecificIssues.length > 0;

    return (
        <AnalysisCard
            title="Track-Specific Analysis"
            status={getOverallStatus()}
        >
            <div className="space-y-4">
                {!hasAnyIssues && (
                    <div className="text-green-700 font-medium">
                        ✅ All tracks are properly configured for mastering
                    </div>
                )}

                {/* Critical Issues */}
                {groupedIssues.critical.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-red-800">
                            ❌ Critical Track Issues
                        </h4>
                        {groupedIssues.critical.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Warning Issues */}
                {groupedIssues.warning.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-yellow-800">
                            ⚠️ Track Configuration Warnings
                        </h4>
                        {groupedIssues.warning.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Info Issues */}
                {groupedIssues.info.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-blue-800">
                            ℹ️ Track Information
                        </h4>
                        {groupedIssues.info.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Detailed Analysis Summary */}
                {trackSpecificAnalysisDetails.length > 0 && (
                    <div className="mt-6 pt-4 border-t border-gray-200">
                        <h4 className="font-semibold text-gray-800 mb-3">
                            Track Analysis Summary
                        </h4>
                        <div className="text-sm text-gray-600 space-y-2">
                            <div>
                                <strong>Tracks with issues:</strong>{" "}
                                {trackSpecificAnalysisDetails.length}
                            </div>
                            <div>
                                <strong>Total track-specific issues:</strong>{" "}
                                {trackSpecificIssues.length}
                            </div>
                        </div>

                        {/* Track Issues Breakdown */}
                        {trackSpecificAnalysisDetails.length > 0 && (
                            <div className="mt-4">
                                <h5 className="font-medium text-gray-800 mb-2">
                                    Tracks with Issues:
                                </h5>
                                <div className="space-y-2">
                                    {trackSpecificAnalysisDetails.map(
                                        (trackDetail: any, idx: number) => (
                                            <div
                                                key={idx}
                                                className="p-2 bg-gray-50 rounded text-sm"
                                            >
                                                <div className="font-medium text-gray-800">
                                                    {trackDetail.name}
                                                </div>
                                                <ul className="text-gray-600 ml-4 mt-1">
                                                    {trackDetail.issues.map(
                                                        (
                                                            issue: string,
                                                            issueIdx: number
                                                        ) => (
                                                            <li key={issueIdx}>
                                                                • {issue}
                                                            </li>
                                                        )
                                                    )}
                                                </ul>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {/* Track Statistics */}
                <div className="bg-gray-100 border border-gray-300 rounded p-3">
                    <h4 className="font-semibold text-gray-800 mb-2">
                        📊 Track Statistics
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span className="font-medium">Total Tracks:</span>{" "}
                            {sessionData.tracks.length}
                        </div>
                        <div>
                            <span className="font-medium">Muted Tracks:</span>{" "}
                            {
                                sessionData.tracks.filter(
                                    (track) => track.muted
                                ).length
                            }
                        </div>
                        <div>
                            <span className="font-medium">
                                Record-Armed Tracks:
                            </span>{" "}
                            {
                                sessionData.tracks.filter(
                                    (track) => track.is_record_armed
                                ).length
                            }
                        </div>
                        <div>
                            <span className="font-medium">
                                Tracks with Bypassed FX:
                            </span>{" "}
                            {
                                sessionData.tracks.filter(
                                    (track) => track.has_bypassed_fx_in_chain
                                ).length
                            }
                        </div>
                    </div>
                </div>

                {/* Recommendations */}
                <div className="bg-gray-100 border border-gray-300 rounded p-3">
                    <h4 className="font-semibold text-gray-800 mb-2">
                        💡 Track Configuration Best Practices
                    </h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                        <li>
                            • Unmute all tracks that should be included in the
                            final mix
                        </li>
                        <li>• Disarm all tracks not intended for recording</li>
                        <li>
                            • Review bypassed plugins to ensure they're
                            intentionally inactive
                        </li>
                        <li>
                            • Check track routing to ensure proper signal flow
                        </li>
                        <li>
                            • Verify track names are descriptive and organized
                        </li>
                        <li>• Ensure proper gain staging across all tracks</li>
                    </ul>
                </div>
            </div>
        </AnalysisCard>
    );
};

export default TrackSpecificAnalysisSection;
