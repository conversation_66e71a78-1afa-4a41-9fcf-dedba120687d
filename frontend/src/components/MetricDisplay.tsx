import React from "react";
import { Alert<PERSON>riangle, CheckCircle, Info, XCircle } from "lucide-react";

interface MetricDisplayProps {
    label: string;
    value: number | string;
    severity?: "critical" | "warning" | "info" | "pass" | "neutral";
    icon?: React.ReactNode;
    unit?: string;
    description?: string;
}

const MetricDisplay: React.FC<MetricDisplayProps> = React.memo(({
    label,
    value,
    severity = "neutral",
    icon,
    unit = "",
    description,
}) => {
    const severityClasses = React.useMemo(() => {
        switch (severity) {
            case "critical":
                return {
                    bg: "bg-red-900",
                    text: "text-red-300",
                    border: "border-red-700",
                    iconColor: "text-red-400",
                    defaultIcon: <XCircle size={28} className="text-red-400" />,
                };
            case "warning":
                return {
                    bg: "bg-yellow-900",
                    text: "text-yellow-300",
                    border: "border-yellow-700",
                    iconColor: "text-yellow-400",
                    defaultIcon: (
                        <AlertTriangle size={28} className="text-yellow-400" />
                    ),
                };
            case "info":
                return {
                    bg: "bg-blue-900",
                    text: "text-blue-300",
                    border: "border-blue-700",
                    iconColor: "text-blue-400",
                    defaultIcon: <Info size={28} className="text-blue-400" />,
                };
            case "pass":
                return {
                    bg: "bg-green-900",
                    text: "text-green-300",
                    border: "border-green-700",
                    iconColor: "text-green-400",
                    defaultIcon: (
                        <CheckCircle size={28} className="text-green-400" />
                    ),
                };
            default: // neutral
                return {
                    bg: "bg-gray-700",
                    text: "text-gray-300",
                    border: "border-gray-600",
                    iconColor: "text-gray-400",
                    defaultIcon: <Info size={28} className="text-gray-400" />,
                };
        }
    }, [severity]);

    const displayIcon = icon || severityClasses.defaultIcon;

    return (
        <div
            className={`p-4 rounded-lg shadow-md flex flex-col justify-between h-full ${severityClasses.bg} border ${severityClasses.border}`}
        >
            <div className="flex items-start justify-between mb-2">
                <h3 className={`text-lg font-semibold ${severityClasses.text}`}>
                    {label}
                </h3>
                {displayIcon}
            </div>
            <div className="text-4xl font-bold text-white mb-1">
                {value}
                {unit && (
                    <span className="text-2xl font-normal text-gray-400 ml-1">
                        {unit}
                    </span>
                )}
            </div>
            {description && (
                <p className={`text-xs ${severityClasses.text} opacity-80 mt-auto`}>
                    {description}
                </p>
            )}
        </div>
    );
});

export default MetricDisplay;
