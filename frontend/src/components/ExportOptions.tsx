import React, { useState } from "react";

interface ExportOptionsProps {
    onExport: (type: string, selectedTracks?: number[]) => void;
    trackCount: number;
}

const ExportOptions = ({ onExport, trackCount }: ExportOptionsProps) => {
    const [isSelecting, setIsSelecting] = useState(false);
    const [selectedExportType, setSelectedExportType] = useState<string | null>(
        null
    );
    const [selectedTracks, setSelectedTracks] = useState<number[]>([]);

    // Track selection is only needed for FX Chain and Track Template exports
    const requiresTrackSelection = (type: string) => {
        return type === "fxchain" || type === "tracktemplate";
    };

    const handleExportClick = (type: string) => {
        if (requiresTrackSelection(type)) {
            setIsSelecting(true);
            setSelectedExportType(type);
            // Default to selecting all tracks
            setSelectedTracks(Array.from({ length: trackCount }, (_, i) => i));
        } else {
            // For exports that don't need track selection (markdown, pdf)
            onExport(type);
        }
    };

    const handleConfirmSelection = () => {
        if (selectedExportType) {
            onExport(selectedExportType, selectedTracks);
            resetSelection();
        }
    };

    const handleCancelSelection = () => {
        resetSelection();
    };

    const resetSelection = () => {
        setIsSelecting(false);
        setSelectedExportType(null);
        setSelectedTracks([]);
    };

    const toggleTrackSelection = (index: number) => {
        setSelectedTracks((prev) =>
            prev.includes(index)
                ? prev.filter((i) => i !== index)
                : [...prev, index]
        );
    };

    const selectAllTracks = () => {
        setSelectedTracks(Array.from({ length: trackCount }, (_, i) => i));
    };

    const deselectAllTracks = () => {
        setSelectedTracks([]);
    };

    return (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
            <h3 className="text-lg mb-2 text-gray-800 dark:text-white font-medium">
                Export Options
            </h3>

            {isSelecting ? (
                <div>
                    <p className="text-sm mb-2 text-gray-700 dark:text-gray-200">
                        {selectedExportType === "fxchain"
                            ? "Select tracks to include in FX Chain export:"
                            : "Select tracks to include in Track Template:"}
                    </p>

                    <div className="mb-2 flex gap-2">
                        <button
                            onClick={selectAllTracks}
                            className="text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 px-2 py-1 rounded"
                        >
                            Select All
                        </button>
                        <button
                            onClick={deselectAllTracks}
                            className="text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 px-2 py-1 rounded"
                        >
                            Deselect All
                        </button>
                    </div>

                    <div className="mb-4 max-h-32 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-2 rounded">
                        {Array.from({ length: trackCount }, (_, i) => (
                            <div
                                key={i}
                                className="flex items-center mb-1 last:mb-0"
                            >
                                <input
                                    type="checkbox"
                                    id={`track-${i}`}
                                    checked={selectedTracks.includes(i)}
                                    onChange={() => toggleTrackSelection(i)}
                                    className="mr-2"
                                />
                                <label
                                    htmlFor={`track-${i}`}
                                    className="text-sm text-gray-700 dark:text-gray-200"
                                >
                                    Track {i + 1}
                                </label>
                            </div>
                        ))}
                    </div>

                    <div className="flex gap-2">
                        <button
                            onClick={handleConfirmSelection}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
                            disabled={selectedTracks.length === 0}
                        >
                            Export
                        </button>
                        <button
                            onClick={handleCancelSelection}
                            className="bg-gray-300 hover:bg-gray-400 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 dark:text-gray-200 px-3 py-1 rounded"
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            ) : (
                <div className="flex flex-wrap gap-2">
                    <button
                        onClick={() => handleExportClick("fxchain")}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
                    >
                        FX Chain
                    </button>
                    <button
                        onClick={() => handleExportClick("tracktemplate")}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
                    >
                        Track Template
                    </button>
                    <button
                        onClick={() => handleExportClick("markdown")}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
                    >
                        Markdown Summary
                    </button>
                    <button
                        onClick={() => handleExportClick("pdf")}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded opacity-50 cursor-not-allowed"
                        title="PDF export coming soon"
                        disabled
                    >
                        PDF Report
                    </button>
                </div>
            )}
        </div>
    );
};

export default ExportOptions;
