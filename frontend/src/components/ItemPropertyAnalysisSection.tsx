import React from "react";
import AnalysisCard from "./AnalysisCard";
import IssueCard from "./IssueCard";
import { useSessionStore, MasteringAnalysisIssue } from "../store/sessionStore";

const ItemPropertyAnalysisSection: React.FC = () => {
    const { sessionData } = useSessionStore();

    if (!sessionData?.masteringAnalysis) {
        return (
            <AnalysisCard title="Item Property Analysis" status="pass">
                <div className="text-gray-600">
                    No mastering analysis data available.
                </div>
            </AnalysisCard>
        );
    }

    const masteringAnalysis = sessionData.masteringAnalysis;

    // Filter issues related to item properties
    const itemPropertyIssues = masteringAnalysis.issues.filter(
        (issue: MasteringAnalysisIssue) => issue.category === "Item Properties"
    );

    // Get detailed item property analysis data
    const itemPropertyAnalysisDetails =
        masteringAnalysis.detailed_analysis?.item_property_analysis || [];

    const getOverallStatus = () => {
        const hasCritical = itemPropertyIssues.some(
            (issue) => issue.severity === "critical"
        );
        const hasWarning = itemPropertyIssues.some(
            (issue) => issue.severity === "warning"
        );

        if (hasCritical) return "fail";
        if (hasWarning) return "warning";
        if (itemPropertyIssues.length > 0) return "info";
        return "pass";
    };

    const groupIssuesBySeverity = () => {
        const grouped = {
            critical: itemPropertyIssues.filter(
                (issue) => issue.severity === "critical"
            ),
            warning: itemPropertyIssues.filter(
                (issue) => issue.severity === "warning"
            ),
            info: itemPropertyIssues.filter(
                (issue) => issue.severity === "info"
            ),
        };
        return grouped;
    };

    const groupedIssues = groupIssuesBySeverity();
    const hasAnyIssues = itemPropertyIssues.length > 0;

    return (
        <AnalysisCard
            title="Item Property Analysis"
            status={getOverallStatus()}
        >
            <div className="space-y-4">
                {!hasAnyIssues && (
                    <div className="text-green-700 font-medium">
                        ✅ All audio items have appropriate properties for
                        mastering
                    </div>
                )}

                {/* Critical Issues */}
                {groupedIssues.critical.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-red-800">
                            ❌ Critical Item Issues
                        </h4>
                        {groupedIssues.critical.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Warning Issues */}
                {groupedIssues.warning.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-yellow-800">
                            ⚠️ Item Property Warnings
                        </h4>
                        {groupedIssues.warning.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Info Issues */}
                {groupedIssues.info.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-blue-800">
                            ℹ️ Item Property Information
                        </h4>
                        {groupedIssues.info.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Detailed Analysis Summary */}
                {itemPropertyAnalysisDetails.length > 0 && (
                    <div className="mt-6 pt-4 border-t border-gray-200">
                        <h4 className="font-semibold text-gray-800 mb-3">
                            Item Property Analysis Summary
                        </h4>
                        <div className="text-sm text-gray-600 space-y-2">
                            <div>
                                <strong>Items with property issues:</strong>{" "}
                                {itemPropertyAnalysisDetails.length}
                            </div>
                            <div>
                                <strong>Total item property issues:</strong>{" "}
                                {itemPropertyIssues.length}
                            </div>
                        </div>
                    </div>
                )}

                {/* Recommendations */}
                <div className="bg-gray-100 border border-gray-300 rounded p-3">
                    <h4 className="font-semibold text-gray-800 mb-2">
                        💡 Item Property Best Practices
                    </h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                        <li>
                            • Add short fade-ins (2-10ms) to prevent clicks at
                            item starts
                        </li>
                        <li>
                            • Add short fade-outs (2-10ms) to prevent abrupt
                            cuts
                        </li>
                        <li>
                            • Ensure playrate is 1.0 unless intentionally
                            altered
                        </li>
                        <li>• Verify reversed items are intentional</li>
                        <li>
                            • Check that all items have proper source material
                        </li>
                    </ul>
                </div>
            </div>
        </AnalysisCard>
    );
};

export default ItemPropertyAnalysisSection;
