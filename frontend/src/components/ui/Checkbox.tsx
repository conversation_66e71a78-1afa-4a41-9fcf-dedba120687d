import React from "react";

interface CheckboxProps {
    label: string;
    checked: boolean;
    onChange: (checked: boolean) => void;
    className?: string;
}

const Checkbox: React.FC<CheckboxProps> = ({
    label,
    checked,
    onChange,
    className,
}) => {
    return (
        <div className="flex items-center">
            <input
                id={label}
                type="checkbox"
                value=""
                className={`w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 ${className}`}
                checked={checked}
                onChange={(e) => onChange(e.target.checked)}
            />
            <label
                htmlFor={label}
                className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300"
            >
                {label}
            </label>
        </div>
    );
};

export default Checkbox;
