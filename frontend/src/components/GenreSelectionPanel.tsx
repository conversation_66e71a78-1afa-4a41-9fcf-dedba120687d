import React from "react";
import Select from "./ui/Select";
import Checkbox from "./ui/Checkbox";

interface GenreSelectionPanelProps {
    genre: string;
    setGenre: (genre: string) => void;
    strictMode: boolean;
    setStrictMode: (strictMode: boolean) => void;
}

const GenreSelectionPanel: React.FC<GenreSelectionPanelProps> = ({
    genre,
    setGenre,
    strictMode,
    setStrictMode,
}) => {
    const genreOptions = [
        { value: "general", label: "General" },
        { value: "electronic", label: "Electronic/EDM" },
        { value: "hiphop", label: "Hip-Hop/Rap" },
        { value: "rock", label: "Rock/Metal" },
        { value: "pop", label: "Pop/R&B" },
        { value: "acoustic", label: "Acoustic/Folk" },
        { value: "jazz_classical", label: "Jazz/Classical" },
        { value: "experimental", label: "Experimental" },
    ];

    return (
        <div className="bg-gray-800 p-4 rounded-lg">
            <h2 className="text-lg font-semibold mb-2">Analysis Options</h2>
            <div className="flex items-center space-x-4">
                <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                        Genre
                    </label>
                    <Select
                        options={genreOptions}
                        value={genre}
                        onChange={setGenre}
                    />
                </div>
                <div className="pt-6">
                    <Checkbox
                        label="Strict Mode"
                        checked={strictMode}
                        onChange={setStrictMode}
                    />
                </div>
            </div>
        </div>
    );
};

export default GenreSelectionPanel;
