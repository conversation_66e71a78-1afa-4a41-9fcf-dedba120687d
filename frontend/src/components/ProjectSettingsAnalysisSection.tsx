import React from "react";
import AnalysisCard from "./AnalysisCard";
import IssueCard from "./IssueCard";
import { useSessionStore, MasteringAnalysisIssue } from "../store/sessionStore";

const ProjectSettingsAnalysisSection: React.FC = () => {
    const { sessionData } = useSessionStore();

    if (!sessionData?.masteringAnalysis) {
        return (
            <AnalysisCard title="Project Settings Analysis" status="pass">
                <div className="text-gray-600">
                    No mastering analysis data available.
                </div>
            </AnalysisCard>
        );
    }

    const masteringAnalysis = sessionData.masteringAnalysis;

    // Filter issues related to project settings
    const projectSettingsIssues = masteringAnalysis.issues.filter(
        (issue: MasteringAnalysisIssue) =>
            issue.category === "Project Settings" ||
            issue.category === "Project Structure" ||
            issue.category === "Project Mix"
    );

    // Get detailed project settings analysis data
    const projectSettingsAnalysisDetails =
        masteringAnalysis.detailed_analysis?.project_settings_analysis || {};

    const getOverallStatus = () => {
        const hasCritical = projectSettingsIssues.some(
            (issue) => issue.severity === "critical"
        );
        const hasWarning = projectSettingsIssues.some(
            (issue) => issue.severity === "warning"
        );

        if (hasCritical) return "fail";
        if (hasWarning) return "warning";
        if (projectSettingsIssues.length > 0) return "info";
        return "pass";
    };

    const groupIssuesBySeverity = () => {
        const grouped = {
            critical: projectSettingsIssues.filter(
                (issue) => issue.severity === "critical"
            ),
            warning: projectSettingsIssues.filter(
                (issue) => issue.severity === "warning"
            ),
            info: projectSettingsIssues.filter(
                (issue) => issue.severity === "info"
            ),
        };
        return grouped;
    };

    const groupedIssues = groupIssuesBySeverity();
    const hasAnyIssues = projectSettingsIssues.length > 0;

    return (
        <AnalysisCard
            title="Project Settings Analysis"
            status={getOverallStatus()}
        >
            <div className="space-y-4">
                {!hasAnyIssues && (
                    <div className="text-green-700 font-medium">
                        ✅ Project settings are optimized for mastering
                    </div>
                )}

                {/* Critical Issues */}
                {groupedIssues.critical.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-red-800">
                            ❌ Critical Project Settings Issues
                        </h4>
                        {groupedIssues.critical.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Warning Issues */}
                {groupedIssues.warning.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-yellow-800">
                            ⚠️ Project Settings Warnings
                        </h4>
                        {groupedIssues.warning.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Info Issues */}
                {groupedIssues.info.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-blue-800">
                            ℹ️ Project Settings Information
                        </h4>
                        {groupedIssues.info.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Project Settings Summary */}
                <div className="mt-6 pt-4 border-t border-gray-200">
                    <h4 className="font-semibold text-gray-800 mb-3">
                        Current Project Settings
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                            <div>
                                <strong>Sample Rate:</strong>{" "}
                                {sessionData.metadata.sample_rate}Hz
                                {projectSettingsAnalysisDetails.sample_rate
                                    ?.is_nonstandard && (
                                    <span className="text-yellow-600 ml-2">
                                        (Non-standard)
                                    </span>
                                )}
                            </div>
                            <div>
                                <strong>Bit Depth:</strong>{" "}
                                {sessionData.metadata.project_bit_depth ||
                                    "Unknown"}
                                -bit
                                {projectSettingsAnalysisDetails.bit_depth
                                    ?.is_suboptimal && (
                                    <span className="text-yellow-600 ml-2">
                                        (Suboptimal)
                                    </span>
                                )}
                            </div>
                        </div>
                        <div className="space-y-2">
                            <div>
                                <strong>Tempo:</strong>{" "}
                                {sessionData.metadata.tempo} BPM
                            </div>
                            <div>
                                <strong>Time Signature:</strong>{" "}
                                {sessionData.metadata.time_signature}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Detailed Analysis Summary */}
                {Object.keys(projectSettingsAnalysisDetails).length > 0 && (
                    <div className="mt-4 p-3 bg-gray-50 rounded">
                        <h5 className="font-medium text-gray-800 mb-2">
                            Analysis Details
                        </h5>
                        <div className="text-sm text-gray-600 space-y-1">
                            {projectSettingsAnalysisDetails.has_incomplete_info && (
                                <div>• Project metadata is incomplete</div>
                            )}
                            {projectSettingsAnalysisDetails.first_item_abrupt_start_at_zero && (
                                <div>
                                    • First item starts abruptly at project
                                    beginning
                                </div>
                            )}
                            {projectSettingsAnalysisDetails.stereo_balance_issue && (
                                <div>• Stereo balance issue detected</div>
                            )}
                        </div>
                    </div>
                )}

                {/* Recommendations */}
                <div className="bg-gray-100 border border-gray-300 rounded p-3">
                    <h4 className="font-semibold text-gray-800 mb-2">
                        💡 Project Settings Best Practices
                    </h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                        <li>• Use 24-bit or higher bit depth for mastering</li>
                        <li>
                            • Standard sample rates: 44.1kHz, 48kHz, 88.2kHz,
                            96kHz
                        </li>
                        <li>
                            • Fill in project title and notes for better
                            organization
                        </li>
                        <li>• Ensure proper stereo balance across the mix</li>
                        <li>• Add pre-roll before the first musical content</li>
                        <li>
                            • Check that project boundaries align with song
                            structure
                        </li>
                    </ul>
                </div>
            </div>
        </AnalysisCard>
    );
};

export default ProjectSettingsAnalysisSection;
