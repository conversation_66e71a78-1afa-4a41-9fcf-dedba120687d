import React, { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";

interface IssueGroup {
    rule_id: string;
    category: string;
    severity: string;
    title: string;
    description: string;
    count: number;
    is_mastering_critical: boolean;
    affected_elements: any[];
    sample_issue?: {
        id: string;
        message: string;
        recommendation?: string;
    };
}

interface IssueGroupCardProps {
    group: IssueGroup;
    className?: string;
}

const IssueGroupCard: React.FC<IssueGroupCardProps> = React.memo(({ group, className = "" }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const severityColor = React.useMemo(() => {
        switch (group.severity) {
            case "critical":
                return "border-red-400 bg-red-50";
            case "warning":
                return "border-yellow-400 bg-yellow-50";
            case "info":
                return "border-blue-400 bg-blue-50";
            default:
                return "border-gray-300 bg-gray-50";
        }
    }, [group.severity]);

    const severityTextColor = React.useMemo(() => {
        switch (group.severity) {
            case "critical":
                return "text-red-800";
            case "warning":
                return "text-yellow-800";
            case "info":
                return "text-blue-800";
            default:
                return "text-gray-800";
        }
    }, [group.severity]);

    const severityBadgeColor = React.useMemo(() => {
        switch (group.severity) {
            case "critical":
                return "bg-red-100 text-red-800";
            case "warning":
                return "bg-yellow-100 text-yellow-800";
            case "info":
                return "bg-blue-100 text-blue-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    }, [group.severity]);

    return (
        <div
            className={`border rounded-lg p-4 mb-3 ${severityColor} ${className}`}
        >
            {/* Header */}
            <div
                className="flex items-center justify-between cursor-pointer"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                        {isExpanded ? (
                            <ChevronDown size={16} className="text-gray-600" />
                        ) : (
                            <ChevronRight size={16} className="text-gray-600" />
                        )}
                        <span
                            className={`px-2 py-1 text-xs font-medium rounded ${severityBadgeColor}`}
                        >
                            {group.severity.toUpperCase()}
                        </span>
                        <span
                            className={`px-2 py-1 text-xs font-medium rounded bg-gray-100 text-gray-700`}
                        >
                            {group.count} items
                        </span>
                    </div>
                    <div>
                        <h4
                            className={`font-semibold ${severityTextColor}`}
                        >
                            {group.title}
                        </h4>
                        <p className="text-sm text-gray-600 mt-1">
                            {group.description}
                        </p>
                    </div>
                </div>
            </div>

            {/* Expanded Details */}
            {isExpanded && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                    {/* Category and Rule ID */}
                    <div className="mb-3">
                        <div className="text-xs text-gray-500 mb-1">
                            <span className="font-medium">Category:</span> {group.category}
                        </div>
                        <div className="text-xs text-gray-500">
                            <span className="font-medium">Rule:</span> {group.rule_id}
                        </div>
                    </div>

                    {/* Sample Issue Details */}
                    {group.sample_issue && (
                        <div className="mb-3">
                            <div className="text-sm font-medium text-gray-700 mb-2">
                                Example Issue:
                            </div>
                            <div className="bg-white p-3 rounded border">
                                <p className="text-sm text-gray-800 mb-2">
                                    {group.sample_issue.message}
                                </p>
                                {group.sample_issue.recommendation && (
                                    <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                        <span className="font-medium">Recommendation:</span>{" "}
                                        {group.sample_issue.recommendation}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Affected Elements Summary */}
                    {group.affected_elements.length > 0 && (
                        <div>
                            <div className="text-sm font-medium text-gray-700 mb-2">
                                Affected Elements ({group.affected_elements.length}):
                            </div>
                            <div className="max-h-32 overflow-y-auto">
                                <div className="grid grid-cols-1 gap-1">
                                    {group.affected_elements.map((element, index) => (
                                        <div
                                            key={index}
                                            className="text-xs bg-white p-2 rounded border flex justify-between"
                                        >
                                            <span>
                                                <span className="font-medium capitalize">
                                                    {element.type || "Unknown"}:
                                                </span>{" "}
                                                {element.name || 
                                                 element.details?.track_name || 
                                                 "Unnamed"}
                                            </span>
                                            {element.details?.track_name && element.name !== element.details.track_name && (
                                                <span className="text-gray-500">
                                                    on {element.details.track_name}
                                                </span>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
});

export default IssueGroupCard;