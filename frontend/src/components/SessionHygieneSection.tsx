import React from "react";
import AnalysisCard from "./AnalysisCard";
import IssueCard from "./IssueCard";
import { useSessionStore, MasteringAnalysisIssue } from "../store/sessionStore";

interface SessionHygieneSectionProps {
    metadata: any;
    tracks: any[];
}

const SessionHygieneSection: React.FC<SessionHygieneSectionProps> = ({
    metadata,
    tracks,
}) => {
    const { sessionData } = useSessionStore();

    // Try to get hygiene analysis first, fallback to mastering analysis
    const analysisData = sessionData?.hygieneAnalysis || sessionData?.masteringAnalysis;
    
    if (!analysisData) {
        return (
            <AnalysisCard title="Session Hygiene" status="pass">
                <div className="text-gray-600">
                    No analysis data available.
                </div>
            </AnalysisCard>
        );
    }

    // Filter issues related to session hygiene (or use all if from hygieneAnalysis)
    const hygieneIssues = sessionData?.hygieneAnalysis 
        ? analysisData.issues.filter((issue: MasteringAnalysisIssue) => !issue.is_mastering_critical)
        : analysisData.issues.filter((issue: MasteringAnalysisIssue) => issue.category === "Session Hygiene");

    // Get detailed hygiene analysis data
    const hygieneAnalysisDetails =
        analysisData.detailed_analysis?.session_hygiene_analysis || {};

    const getOverallStatus = () => {
        const hasCritical = hygieneIssues.some(
            (issue) => issue.severity === "critical"
        );
        const hasWarning = hygieneIssues.some(
            (issue) => issue.severity === "warning"
        );

        if (hasCritical) return "fail";
        if (hasWarning) return "warning";
        if (hygieneIssues.length > 0) return "info";
        return "pass";
    };

    const groupIssuesBySeverity = () => {
        const grouped = {
            critical: hygieneIssues.filter(
                (issue) => issue.severity === "critical"
            ),
            warning: hygieneIssues.filter(
                (issue) => issue.severity === "warning"
            ),
            info: hygieneIssues.filter((issue) => issue.severity === "info"),
        };
        return grouped;
    };

    const groupedIssues = groupIssuesBySeverity();
    const hasAnyIssues = hygieneIssues.length > 0;

    return (
        <AnalysisCard title="Session Hygiene" status={getOverallStatus()}>
            <div className="space-y-4">
                {!hasAnyIssues && (
                    <div className="text-green-700 font-medium">
                        ✅ Session is well-organized and ready for mastering
                    </div>
                )}

                {/* Critical Issues */}
                {groupedIssues.critical.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-red-800">
                            ❌ Critical Hygiene Issues
                        </h4>
                        {groupedIssues.critical.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Warning Issues */}
                {groupedIssues.warning.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-yellow-800">
                            ⚠️ Hygiene Warnings
                        </h4>
                        {groupedIssues.warning.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Info Issues */}
                {groupedIssues.info.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-blue-800">
                            ℹ️ Session Organization
                        </h4>
                        {groupedIssues.info.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                        <div className="text-xs text-blue-600 mt-2">
                            These don't affect mastering but improve project
                            organization
                        </div>
                    </div>
                )}

                {/* Detailed Analysis Summary */}
                {(hygieneAnalysisDetails.project_level ||
                    hygieneAnalysisDetails.track_level) && (
                    <div className="mt-6 pt-4 border-t border-gray-200">
                        <h4 className="font-semibold text-gray-800 mb-3">
                            Hygiene Analysis Summary
                        </h4>
                        <div className="text-sm text-gray-600 space-y-2">
                            {hygieneAnalysisDetails.project_level && (
                                <div>
                                    <strong>Project-level issues:</strong>{" "}
                                    {
                                        hygieneAnalysisDetails.project_level
                                            .length
                                    }
                                </div>
                            )}
                            {hygieneAnalysisDetails.track_level && (
                                <div>
                                    <strong>Tracks with hygiene issues:</strong>{" "}
                                    {hygieneAnalysisDetails.track_level.length}
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* Session Statistics */}
                <div className="bg-gray-100 border border-gray-300 rounded p-3">
                    <h4 className="font-semibold text-gray-800 mb-2">
                        📊 Session Statistics
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span className="font-medium">Total Tracks:</span>{" "}
                            {tracks.length}
                        </div>
                        <div>
                            <span className="font-medium">Sample Rate:</span>{" "}
                            {metadata.sample_rate}Hz
                        </div>
                        <div>
                            <span className="font-medium">Tempo:</span>{" "}
                            {metadata.tempo} BPM
                        </div>
                        <div>
                            <span className="font-medium">Time Signature:</span>{" "}
                            {metadata.time_signature}
                        </div>
                    </div>
                </div>
            </div>
        </AnalysisCard>
    );
};

export default SessionHygieneSection;
