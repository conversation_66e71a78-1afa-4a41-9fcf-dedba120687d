import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useSessionStore } from "../store/sessionStore";
import { HardDrive, Sun, Moon } from "lucide-react";
import { useUIStore } from "../App";

const TopNavBar = React.memo(() => {
    const location = useLocation();
    const { sessionData } = useSessionStore();
    const { isDarkMode, toggleDarkMode } = useUIStore();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

    // Active page indicator
    const isActive = React.useCallback((path: string) => location.pathname === path, [location.pathname]);

    // Navigation items
    const navItems = React.useMemo(() => [
        {
            name: "Dashboard",
            path: "/",
            disabled: false,
        },
        {
            name: "Tracks",
            path: "/tracks",
            disabled: !sessionData,
        },
        {
            name: "Plugins",
            path: "/plugins",
            disabled: !sessionData,
        },
        {
            name: "Mastering Report",
            path: "/mastering",
            disabled: !sessionData,
        },
        {
            name: "Session Hygiene",
            path: "/hygiene",
            disabled: !sessionData,
        },
        {
            name: "Export",
            path: "/export",
            disabled: !sessionData,
        },
    ], [sessionData]);

    return (
        <header className="sticky top-0 z-50 bg-white dark:bg-gray-800 shadow-md border-b border-gray-200 dark:border-gray-700">
            <div className="container mx-auto px-4">
                <div className="flex items-center justify-between h-16">
                    {/* Logo and App Name */}
                    <div className="flex items-center">
                        <Link to="/" className="flex items-center gap-2">
                            <HardDrive className="text-blue-500" size={24} />
                            <span className="text-xl font-bold text-gray-900 dark:text-white">
                                SessionView
                            </span>
                        </Link>
                    </div>

                    {/* Navigation Links */}
                    <nav className="hidden md:flex items-center space-x-1">
                        {navItems.map((item) => (
                            <Link
                                key={item.path}
                                to={item.disabled ? "#" : item.path}
                                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                                    isActive(item.path)
                                        ? "bg-blue-600 text-white"
                                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                } ${
                                    item.disabled
                                        ? "opacity-50 cursor-not-allowed"
                                        : ""
                                }`}
                                onClick={(e) => {
                                    if (item.disabled) {
                                        e.preventDefault();
                                    }
                                }}
                                title={
                                    item.disabled && item.name !== "Dashboard"
                                        ? "Upload a file first to access this section"
                                        : ""
                                }
                            >
                                {item.name}
                            </Link>
                        ))}
                    </nav>

                    {/* Dark Mode Toggle */}
                    <button
                        onClick={toggleDarkMode}
                        className="p-2 rounded-md bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 transition-colors flex items-center"
                        aria-label={
                            isDarkMode
                                ? "Switch to light mode"
                                : "Switch to dark mode"
                        }
                    >
                        {isDarkMode ? (
                            <>
                                <Sun size={18} className="mr-1" /> Light
                            </>
                        ) : (
                            <>
                                <Moon size={18} className="mr-1" /> Dark
                            </>
                        )}
                    </button>

                    {/* Mobile Menu Button (shows on small screens) */}
                    <div className="md:hidden flex items-center">
                        <button
                            className="mobile-menu-button p-2 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
                            onClick={React.useCallback(() =>
                                setIsMobileMenuOpen(prev => !prev), []
                            )}
                        >
                            <svg
                                className="h-6 w-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M4 6h16M4 12h16M4 18h16"
                                />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            {/* Mobile Menu (hidden by default) */}
            <div
                className={`mobile-menu md:hidden ${
                    isMobileMenuOpen ? "" : "hidden"
                }`}
            >
                <div className="px-2 pt-2 pb-3 space-y-1">
                    {navItems.map((item) => (
                        <Link
                            key={`mobile-${item.path}`}
                            to={item.disabled ? "#" : item.path}
                            className={`block px-3 py-2 rounded-md text-base font-medium ${
                                isActive(item.path)
                                    ? "bg-blue-600 text-white"
                                    : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                            } ${
                                item.disabled
                                    ? "opacity-50 cursor-not-allowed"
                                    : ""
                            }`}
                            onClick={React.useCallback((e) => {
                                if (item.disabled) {
                                    e.preventDefault();
                                }
                                // Close mobile menu when a link is clicked
                                setIsMobileMenuOpen(false);
                            }, [item.disabled])}
                        >
                            {item.name}
                        </Link>
                    ))}
                </div>
            </div>
        </header>
    );
});

export default TopNavBar;
