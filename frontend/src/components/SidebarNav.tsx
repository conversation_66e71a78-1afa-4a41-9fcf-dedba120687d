import { Link, useLocation } from "react-router-dom";
import { useSessionStore } from "../store/sessionStore";
import {
    LayoutDashboard,
    Layers,
    FileJson,
    FileUp,
    HardDrive,
    Settings,
    Menu,
    X,
} from "lucide-react";
import { useState } from "react";

const SidebarNav = () => {
    const location = useLocation();
    const { sessionData } = useSessionStore();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    // Active page indicator
    const isActive = (path: string) => location.pathname === path;

    // Navigation items
    const navItems = [
        {
            name: "Dashboard",
            path: "/",
            icon: <LayoutDashboard size={20} />,
            disabled: false,
        },
        {
            name: "Tracks",
            path: "/tracks",
            icon: <Layers size={20} />,
            disabled: !sessionData,
        },
        {
            name: "Plugins",
            path: "/plugins",
            icon: <FileJson size={20} />,
            disabled: !sessionData,
        },
        {
            name: "Export",
            path: "/export",
            icon: <FileUp size={20} />,
            disabled: !sessionData,
        },
        // Future pages
        {
            name: "Settings",
            path: "/settings",
            icon: <Settings size={20} />,
            disabled: true, // Disabled for MVP
        },
    ];

    return (
        <>
            {/* Mobile menu button */}
            <div className="md:hidden fixed top-4 left-4 z-50">
                <button
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="p-2 bg-gray-800 text-white rounded-md shadow-lg"
                >
                    {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
                </button>
            </div>

            {/* Sidebar - desktop (fixed) and mobile (slide-in) */}
            <div
                className={`fixed inset-y-0 left-0 transform transition-transform duration-300 ease-in-out z-40
                    md:translate-x-0 md:static md:inset-auto md:translate-x-0
                    ${
                        isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"
                    }`}
            >
                <div className="bg-gray-800 text-white w-64 min-h-screen p-4 flex flex-col">
                    <div className="flex items-center mb-8 p-2">
                        <HardDrive className="text-blue-400 mr-2" size={24} />
                        <h1 className="text-xl font-bold">SessionView</h1>
                    </div>

                    <nav className="flex-grow">
                        <ul className="space-y-1">
                            {navItems.map((item) => (
                                <li key={item.path}>
                                    <Link
                                        to={item.disabled ? "#" : item.path}
                                        className={`flex items-center p-3 rounded-md transition-colors ${
                                            isActive(item.path)
                                                ? "bg-blue-600 text-white"
                                                : "text-gray-300 hover:bg-gray-700"
                                        } ${
                                            item.disabled
                                                ? "opacity-50 cursor-not-allowed"
                                                : ""
                                        }`}
                                        onClick={(e) => {
                                            if (item.disabled) {
                                                e.preventDefault();
                                            }
                                            // Close mobile menu when a link is clicked
                                            if (isMobileMenuOpen) {
                                                setIsMobileMenuOpen(false);
                                            }
                                        }}
                                    >
                                        <span className="mr-3">
                                            {item.icon}
                                        </span>
                                        {item.name}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </nav>

                    <div className="mt-auto pt-4 border-t border-gray-700 text-sm text-gray-500">
                        <p>SessionView v0.1.0</p>
                    </div>
                </div>
            </div>

            {/* Overlay for mobile */}
            {isMobileMenuOpen && (
                <div
                    className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
                    onClick={() => setIsMobileMenuOpen(false)}
                ></div>
            )}
        </>
    );
};

export default SidebarNav;
