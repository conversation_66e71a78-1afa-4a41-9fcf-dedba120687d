import React from "react";

interface AnalysisCardProps {
    title: string;
    status: "pass" | "warning" | "fail" | "info";
    children: React.ReactNode;
    isExpanded?: boolean;
    onToggle?: () => void;
}

const AnalysisCard: React.FC<AnalysisCardProps> = ({
    title,
    status,
    children,
    isExpanded = false,
    onToggle,
}) => {
    const getStatusColor = () => {
        switch (status) {
            case "pass":
                return "border-green-500 bg-green-50";
            case "warning":
                return "border-yellow-500 bg-yellow-50";
            case "fail":
                return "border-red-500 bg-red-50";
            case "info":
                return "border-blue-500 bg-blue-50";
            default:
                return "border-gray-300 bg-gray-50";
        }
    };

    const getStatusIcon = () => {
        switch (status) {
            case "pass":
                return "✅";
            case "warning":
                return "⚠️";
            case "fail":
                return "❌";
            case "info":
                return "ℹ️";
            default:
                return "";
        }
    };

    return (
        <div className={`border-2 rounded-lg p-4 mb-4 ${getStatusColor()}`}>
            <div
                className="flex items-center justify-between cursor-pointer"
                onClick={onToggle}
            >
                <div className="flex items-center space-x-2">
                    <span className="text-lg">{getStatusIcon()}</span>
                    <h3 className="text-lg font-semibold">{title}</h3>
                </div>
                {onToggle && (
                    <span className="text-gray-500">
                        {isExpanded ? "▼" : "▶"}
                    </span>
                )}
            </div>
            {(isExpanded || !onToggle) && (
                <div className="mt-3">{children}</div>
            )}
        </div>
    );
};

export default AnalysisCard;
