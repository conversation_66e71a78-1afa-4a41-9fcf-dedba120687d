import React from "react";
import { useSessionStore, GroupedMasteringAnalysis, IssueGroup } from "../store/sessionStore";
import IssueGroupCard from "./IssueGroupCard";
import IssueCard from "./IssueCard";

interface GroupedMasteringDashboardProps {
    groupedAnalysis: GroupedMasteringAnalysis;
}

const GroupedMasteringDashboard: React.FC<GroupedMasteringDashboardProps> = React.memo(({ 
    groupedAnalysis 
}) => {
    const { sessionData } = useSessionStore();
    
    // Pagination state for performance with large lists
    const [groupsToShow, setGroupsToShow] = React.useState(10);
    const [ungroupedToShow, setUngroupedToShow] = React.useState(20);

    if (!groupedAnalysis) {
        return (
            <div className="bg-gray-800 rounded-lg p-6 mb-6">
                <h2 className="text-xl font-bold text-white mb-4">
                    Mastering Analysis (Grouped View)
                </h2>
                <p className="text-gray-400">No grouped analysis data available.</p>
            </div>
        );
    }

    const { issue_groups, ungrouped_issues, summary_metrics, overall_health_score } = groupedAnalysis;

    // Memoize displayed items for performance
    const displayedGroups = React.useMemo(() => 
        issue_groups.slice(0, groupsToShow), 
        [issue_groups, groupsToShow]
    );
    
    const displayedUngrouped = React.useMemo(() => 
        ungrouped_issues.slice(0, ungroupedToShow), 
        [ungrouped_issues, ungroupedToShow]
    );

    // Load more handlers
    const handleLoadMoreGroups = React.useCallback(() => {
        setGroupsToShow(prev => Math.min(prev + 10, issue_groups.length));
    }, [issue_groups.length]);

    const handleLoadMoreUngrouped = React.useCallback(() => {
        setUngroupedToShow(prev => Math.min(prev + 20, ungrouped_issues.length));
    }, [ungrouped_issues.length]);

    // Calculate totals
    const totalGroupedIssues = issue_groups.reduce((sum, group) => sum + group.count, 0);
    const totalIssues = totalGroupedIssues + ungrouped_issues.length;

    // Get health status
    const getHealthStatus = () => {
        if (overall_health_score >= 80) return { status: "Good", color: "text-green-400" };
        if (overall_health_score >= 60) return { status: "Fair", color: "text-yellow-400" };
        return { status: "Needs Work", color: "text-red-400" };
    };

    const healthStatus = getHealthStatus();

    // Calculate clutter reduction
    const originalIssueCount = totalIssues;
    const displayedItems = issue_groups.length + ungrouped_issues.length;
    const clutterReduction = originalIssueCount > 0 
        ? Math.round(((originalIssueCount - displayedItems) / originalIssueCount) * 100)
        : 0;

    return (
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
            {/* Header */}
            <div className="flex justify-between items-start mb-6">
                <div>
                    <h2 className="text-xl font-bold text-white mb-2">
                        Mastering Analysis (Grouped View)
                    </h2>
                    <p className="text-gray-400">
                        Issues grouped to reduce visual clutter by {clutterReduction}%
                    </p>
                </div>
                <div className="text-right">
                    <div className="text-2xl font-bold text-white">
                        {Math.round(overall_health_score)}%
                    </div>
                    <div className={`text-sm ${healthStatus.color}`}>
                        {healthStatus.status}
                    </div>
                </div>
            </div>

            {/* Summary Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-red-900 bg-opacity-50 rounded p-3">
                    <div className="text-red-300 text-sm">Critical</div>
                    <div className="text-white text-xl font-semibold">
                        {summary_metrics.critical_issues_count}
                    </div>
                </div>
                <div className="bg-yellow-900 bg-opacity-50 rounded p-3">
                    <div className="text-yellow-300 text-sm">Warning</div>
                    <div className="text-white text-xl font-semibold">
                        {summary_metrics.warning_issues_count}
                    </div>
                </div>
                <div className="bg-blue-900 bg-opacity-50 rounded p-3">
                    <div className="text-blue-300 text-sm">Info</div>
                    <div className="text-white text-xl font-semibold">
                        {summary_metrics.info_issues_count}
                    </div>
                </div>
                <div className="bg-green-900 bg-opacity-50 rounded p-3">
                    <div className="text-green-300 text-sm">Passed</div>
                    <div className="text-white text-xl font-semibold">
                        {summary_metrics.passed_checks_count}
                    </div>
                </div>
            </div>

            {/* Grouping Statistics */}
            <div className="bg-gray-700 rounded p-4 mb-6">
                <h3 className="text-white font-semibold mb-2">Grouping Summary</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <div className="text-gray-400">Total Issues</div>
                        <div className="text-white font-medium">{totalIssues}</div>
                    </div>
                    <div>
                        <div className="text-gray-400">Issue Groups</div>
                        <div className="text-white font-medium">{issue_groups.length}</div>
                    </div>
                    <div>
                        <div className="text-gray-400">Individual Issues</div>
                        <div className="text-white font-medium">{ungrouped_issues.length}</div>
                    </div>
                    <div>
                        <div className="text-gray-400">Display Items</div>
                        <div className="text-white font-medium">{displayedItems}</div>
                    </div>
                </div>
            </div>

            {/* Issues Display */}
            <div className="space-y-4">
                {/* Issue Groups */}
                {issue_groups.length > 0 && (
                    <div>
                        <h3 className="text-white font-semibold mb-3">
                            Grouped Issues ({issue_groups.length} groups, {totalGroupedIssues} total issues)
                        </h3>
                        <div className="space-y-3">
                            {displayedGroups.map((group, index) => (
                                <IssueGroupCard key={index} group={group} />
                            ))}
                        </div>
                        {groupsToShow < issue_groups.length && (
                            <div className="mt-4 text-center">
                                <button 
                                    onClick={handleLoadMoreGroups}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
                                >
                                    Load More Groups ({groupsToShow} of {issue_groups.length})
                                </button>
                            </div>
                        )}
                    </div>
                )}

                {/* Individual Issues */}
                {ungrouped_issues.length > 0 && (
                    <div>
                        <h3 className="text-white font-semibold mb-3">
                            Individual Issues ({ungrouped_issues.length})
                        </h3>
                        <div className="space-y-3">
                            {displayedUngrouped.map((issue, index) => (
                                <IssueCard
                                    key={issue.id || index}
                                    issue={issue}
                                />
                            ))}
                        </div>
                        {ungroupedToShow < ungrouped_issues.length && (
                            <div className="mt-4 text-center">
                                <button 
                                    onClick={handleLoadMoreUngrouped}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
                                >
                                    Load More Issues ({ungroupedToShow} of {ungrouped_issues.length})
                                </button>
                            </div>
                        )}
                    </div>
                )}

                {/* No Issues */}
                {issue_groups.length === 0 && ungrouped_issues.length === 0 && (
                    <div className="text-center py-8">
                        <div className="text-green-400 text-4xl mb-4">✓</div>
                        <h3 className="text-xl font-semibold text-white mb-2">
                            No Issues Found
                        </h3>
                        <p className="text-gray-400">
                            Your project appears to be ready for mastering!
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
});

export default GroupedMasteringDashboard;