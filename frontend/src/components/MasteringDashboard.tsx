import React from "react";
import { useSessionStore } from "../store/sessionStore";
import HealthIndicator from "./HealthIndicator";
import MetricDisplay from "./MetricDisplay";
import IssueCard from "./IssueCard";
import { MasteringAnalysisIssue } from "../store/sessionStore";

const MasteringDashboard: React.FC = React.memo(() => {
    const { sessionData } = useSessionStore();

    if (!sessionData || !sessionData.masteringAnalysis) {
        return (
            <div className="bg-gray-800 text-white p-4 rounded-lg shadow-lg">
                <h2 className="text-xl font-semibold mb-2">
                    Mastering Dashboard
                </h2>
                <p>Loading analysis data or no data available...</p>
            </div>
        );
    }

    // Memoize expensive calculations
    const analysisData = React.useMemo(() => {
        const overallHealthScore = sessionData.masteringAnalysis?.overall_health_score || 0;
        const summaryMetrics = sessionData.masteringAnalysis?.summary_metrics || {
            critical_issues_count: 0,
            warning_issues_count: 0,
            info_issues_count: 0,
            passed_checks_count: 0,
        };
        
        return {
            overallHealthScore,
            criticalIssuesCount: summaryMetrics.critical_issues_count,
            warningIssuesCount: summaryMetrics.warning_issues_count,
            infoIssuesCount: summaryMetrics.info_issues_count,
            passedChecksCount: summaryMetrics.passed_checks_count,
        };
    }, [sessionData.masteringAnalysis]);

    // Memoize filtered priority issues
    const priorityIssues = React.useMemo(() => {
        if (!sessionData.masteringAnalysis?.issues) return [];
        
        return sessionData.masteringAnalysis.issues
            .filter((issue: MasteringAnalysisIssue) => 
                issue.severity === "critical" || issue.severity === "warning"
            )
            .slice(0, 3); // Show top 3 critical/warning issues
    }, [sessionData.masteringAnalysis?.issues]);

    return (
        <div className="bg-gray-800 p-6 rounded-xl shadow-2xl mb-8">
            <h2 className="text-3xl font-bold text-white mb-6 border-b border-gray-700 pb-3">
                Mastering Readiness Dashboard
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                {/* HealthIndicator will go here */}
                <div className="bg-gray-700 p-4 rounded-lg shadow-md">
                    <h3 className="text-lg font-semibold text-blue-400 mb-2">
                        Overall Score
                    </h3>
                    <p className="text-4xl font-bold text-white mb-2">
                        {analysisData.overallHealthScore}%
                    </p>
                    <HealthIndicator score={analysisData.overallHealthScore} />
                </div>

                <MetricDisplay
                    label="Critical Issues"
                    value={analysisData.criticalIssuesCount}
                    severity="critical"
                />
                <MetricDisplay
                    label="Warnings"
                    value={analysisData.warningIssuesCount}
                    severity="warning"
                />
                <MetricDisplay
                    label="Passed Checks"
                    value={analysisData.passedChecksCount}
                    severity="pass"
                />
            </div>

            {/* Placeholder for top priority issues list */}
            <div className="mt-6">
                <h3 className="text-xl font-semibold text-white mb-3">
                    Top Priority Issues
                </h3>
                <div className="space-y-3">
                    {priorityIssues.length > 0 ? (
                        priorityIssues.map((issue: MasteringAnalysisIssue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))
                    ) : (
                        <p className="text-green-400">
                            No critical or warning issues found. Great job!
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
});

export default MasteringDashboard;
