import React, { useMemo, useState } from "react";
import TrackRoutingDiagram from "./TrackRoutingDiagram";
import AutomationLaneDisplay from "./AutomationLaneDisplay";
import {
    VolumeX,
    Activity,
    Sliders,
    Music,
    Package,
    Gauge,
} from "lucide-react";
import {
    Track as TrackType,
    RoutingConnection,
    ProjectMetadata,
    PluginAnalysisResult,
} from "../store/sessionStore";

interface TrackListProps {
    tracks: TrackType[];
    routing: RoutingConnection[];
    selectedTrackName: string | null;
    onTrackSelect: (trackName: string | null) => void;
    metadata: ProjectMetadata;
}

const TrackList = ({
    tracks,
    routing,
    selectedTrackName,
    onTrackSelect,
    metadata,
}: TrackListProps) => {
    const trackCount = useMemo(() => tracks.length, [tracks]);
    // Create unique identifiers for tracks (combining name and GUID)
    const getTrackUniqueId = (name: string, guid: string) => `${name}-${guid}`;

    // Store open/closed state to enable animations - use unique ID instead of just name
    const [expandedTrackId, setExpandedTrackId] = useState<string | null>(
        selectedTrackName
            ? (() => {
                  const selectedTrack = tracks.find(
                      (t) => t.name === selectedTrackName
                  );
                  return selectedTrack
                      ? getTrackUniqueId(selectedTrack.name, selectedTrack.guid)
                      : null;
              })()
            : null
    );
    // Compute a unique ID for the selected track
    const { selectedTrackUniqueId } = useMemo(() => {
        if (!selectedTrackName) return { selectedTrackUniqueId: null };

        // There could be multiple tracks with the same name, but only one will be truly selected
        // We'll use the one that matches our expanded track ID if available
        const matchingTracks = tracks.filter(
            (track) => track.name === selectedTrackName
        );

        if (matchingTracks.length === 0) {
            return { selectedTrackUniqueId: null };
        }

        // If we have a single match, use it
        if (matchingTracks.length === 1) {
            const track = matchingTracks[0];
            return {
                selectedTrackUniqueId: getTrackUniqueId(track.name, track.guid),
            };
        }

        // If we have multiple matches but an expanded track, prioritize the expanded one
        if (expandedTrackId) {
            const expandedTrack = matchingTracks.find(
                (track) =>
                    getTrackUniqueId(track.name, track.guid) === expandedTrackId
            );

            if (expandedTrack) {
                return {
                    selectedTrackUniqueId: expandedTrackId,
                };
            }
        }

        // Default to the first matching track
        const track = matchingTracks[0];
        return {
            selectedTrackUniqueId: getTrackUniqueId(track.name, track.guid),
        };
    }, [selectedTrackName, tracks, expandedTrackId, getTrackUniqueId]);

    // Track what's being displayed in the expanded section (routing or automation)
    const [expandedMode, setExpandedMode] = useState<"routing" | "automation">(
        "routing"
    );

    // Handle track selection with animation timing
    const handleTrackSelect = (track: TrackType) => {
        const trackUniqueId = getTrackUniqueId(track.name, track.guid);

        if (selectedTrackName === track.name) {
            // Track is being deselected
            setExpandedTrackId(null);
            // Wait for animation to complete before updating parent state
            setTimeout(() => {
                onTrackSelect(track.name);
            }, 300); // Match transition duration
        } else {
            // Track is being selected
            onTrackSelect(track.name);
            setExpandedTrackId(trackUniqueId);
        }
    };

    // Calculate which tracks have routing information
    const tracksWithRouting = useMemo(() => {
        const trackNameSet = new Set<string>();
        const trackGuidSet = new Set<string>();

        routing.forEach((route) => {
            trackNameSet.add(route.source);
            trackNameSet.add(route.destination);
            trackGuidSet.add(route.source_guid);
            trackGuidSet.add(route.destination_guid);
        });

        return { trackNameSet, trackGuidSet };
    }, [routing]);

    // Function to determine if a track has routing information
    const hasRouting = (trackName: string, trackGuid: string) => {
        // A track has routing if either its name or GUID appears in routing connections
        return (
            tracksWithRouting.trackNameSet.has(trackName) ||
            tracksWithRouting.trackGuidSet.has(trackGuid)
        );
    };

    // Function to determine if a track has automation lanes
    const hasAutomation = (track: TrackType) => {
        // console.log( // DEBUG REMOVED
        //     `Checking automation for track ${track.name}:`, // DEBUG REMOVED
        //     track.automation_lanes // DEBUG REMOVED
        // ); // DEBUG REMOVED
        return track.automation_lanes && track.automation_lanes.length > 0;
    };

    // Function to determine how many effective automation points exist (more than default)
    const getEffectiveAutomationCount = (track: TrackType) =>
        track.automation_lanes
            ? track.automation_lanes.filter((lane) => lane.effective_points > 0)
                  .length
            : 0;

    // Function to check if a track has active pan automation
    const hasPanAutomation = (track: TrackType): boolean => {
        if (!track.automation_lanes) return false;

        return track.automation_lanes.some(
            (lane) =>
                // Look for lanes with display_name containing "Pan" and having effective points
                lane.display_name.toLowerCase().includes("pan") &&
                lane.effective_points > 0
        );
    };

    // Handler for clicking on the pan value when it has automation
    const handlePanClick = (e: React.MouseEvent, track: TrackType) => {
        e.stopPropagation();

        // Find the pan automation lane
        const panLane = track.automation_lanes.find((lane) =>
            lane.display_name.toLowerCase().includes("pan")
        );

        if (panLane) {
            // Set this track as selected
            onTrackSelect(track.name);
            // Set expanded mode to automation
            setExpandedMode("automation");
            // Use the unique track ID
            const trackUniqueId = getTrackUniqueId(track.name, track.guid);
            setExpandedTrackId(trackUniqueId);
        }
    };

    // Unified handler for both routing and automation toggles
    const handleToggleExpand = (
        track: TrackType,
        mode: "routing" | "automation",
        e: React.MouseEvent
    ) => {
        e.stopPropagation();

        const trackUniqueId = getTrackUniqueId(track.name, track.guid);

        if (expandedTrackId === trackUniqueId && expandedMode === mode) {
            // Already showing this mode for this track, collapse
            setExpandedTrackId(null);
            // Wait for animation to complete before updating parent state
            setTimeout(() => {
                onTrackSelect(null);
            }, 300);
        } else {
            // Switch to the requested mode for this track
            setExpandedMode(mode);
            setExpandedTrackId(trackUniqueId);
            onTrackSelect(track.name);
        }
    };

    // Calculate summary statistics for tracks
    const trackStats = useMemo(() => {
        const folderTracks = tracks.filter((t) => t.is_folder).length;
        const tracksWithFX = tracks.filter((t) => t.fx.length > 0).length;
        const tracksWithAutomation = tracks.filter(
            (t) => t.automation_lanes && t.automation_lanes.length > 0
        ).length;
        const tracksWithRoutingCount = tracksWithRouting.trackNameSet.size;

        return {
            total: trackCount,
            folders: folderTracks,
            regular: trackCount - folderTracks,
            withFX: tracksWithFX,
            withAutomation: tracksWithAutomation,
            withRouting: tracksWithRoutingCount,
        };
    }, [trackCount, tracks, tracksWithRouting]);

    return (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
            {/* Project Name Heading */}
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                {metadata.title || metadata.filename}
            </h2>
            {/* Track Summary Panel */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {/* Track Statistics */}
                <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 shadow-sm">
                    <h4 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-3 flex items-center">
                        <Music className="mr-2 h-5 w-5" />
                        Track Statistics
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                Total Tracks:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {trackStats.total}
                            </span>
                        </div>

                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                Regular Tracks:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {trackStats.regular}
                            </span>
                        </div>

                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                Folder Tracks:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {trackStats.folders}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Track Features */}
                <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-4 shadow-sm">
                    <h4 className="text-lg font-semibold text-purple-800 dark:text-purple-300 mb-3 flex items-center">
                        <Package className="mr-2 h-5 w-5" />
                        Track Features
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                With FX:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {trackStats.withFX}
                            </span>
                        </div>

                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                With Automation:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {trackStats.withAutomation}
                            </span>
                        </div>

                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                With Routing:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {trackStats.withRouting}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Project Info */}
                <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-4 shadow-sm">
                    <h4 className="text-lg font-semibold text-green-800 dark:text-green-300 mb-3 flex items-center">
                        <Gauge className="mr-2 h-5 w-5" />
                        Project Info
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                Sample Rate:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {metadata.sample_rate} Hz
                            </span>
                        </div>

                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                Tempo:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {metadata.tempo} BPM
                            </span>
                        </div>

                        <div className="flex items-center">
                            <span className="text-gray-700 dark:text-gray-300 text-sm">
                                Time Signature:
                            </span>
                        </div>
                        <div className="text-right">
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                                {metadata.time_signature}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead>
                        <tr className="border-b border-gray-300 dark:border-gray-700">
                            <th className="text-left p-2">Name</th>
                            <th className="text-center p-2 w-12">Routing</th>
                            <th className="text-center p-2 w-12">Auto</th>
                            <th className="text-left p-2 w-20">Type</th>
                            <th className="text-center p-2 w-20">Volume</th>
                            <th className="text-center p-2 w-16">Pan</th>
                            <th className="text-left p-2">FX</th>
                            <th className="text-left p-2 w-16">Color</th>
                        </tr>
                    </thead>
                    <tbody>
                        {tracks.map((track, i) => (
                            <React.Fragment key={`track-${track.name}-${i}`}>
                                <tr
                                    className={`border-b border-gray-200 dark:border-gray-700 
                                        ${
                                            getTrackUniqueId(
                                                track.name,
                                                track.guid
                                            ) === selectedTrackUniqueId
                                                ? "bg-blue-100 dark:bg-blue-900"
                                                : i % 2 === 0
                                                ? "bg-gray-50 dark:bg-gray-800"
                                                : "bg-white dark:bg-gray-900"
                                        } hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-150`}
                                    onClick={() =>
                                        hasRouting(track.name, track.guid) &&
                                        handleTrackSelect(track)
                                    }
                                >
                                    <td className="p-2 max-w-[250px]">
                                        <div className="flex items-center">
                                            <span
                                                style={{
                                                    paddingLeft:
                                                        track.is_in_folder
                                                            ? "1.5rem"
                                                            : "0",
                                                }}
                                                className="truncate text-gray-900 dark:text-gray-100 font-medium flex items-center"
                                                title={
                                                    track.is_in_folder
                                                        ? `${track.name} (in folder: ${track.parent_folder_name})`
                                                        : track.name
                                                }
                                            >
                                                {/* Folder hierarchy indicator */}
                                                {track.is_in_folder && (
                                                    <span className="mr-2 text-gray-400 dark:text-gray-500">
                                                        ├─
                                                    </span>
                                                )}

                                                {/* Folder icon for folder tracks */}
                                                {track.is_folder && (
                                                    <span className="mr-1.5 text-yellow-600 dark:text-yellow-500">
                                                        📁
                                                    </span>
                                                )}

                                                {/* Regular track icon for tracks in folders */}
                                                {track.is_in_folder &&
                                                    !track.is_folder && (
                                                        <span className="mr-1.5 text-blue-500 dark:text-blue-400">
                                                            📄
                                                        </span>
                                                    )}

                                                {/* Mute indicator */}
                                                {track.muted && (
                                                    <span
                                                        className="mr-1.5 text-red-500 dark:text-red-400"
                                                        title="Track is muted"
                                                    >
                                                        <VolumeX className="inline-block w-4 h-4" />
                                                    </span>
                                                )}

                                                <span
                                                    className={
                                                        track.muted
                                                            ? "text-gray-400 dark:text-gray-500"
                                                            : track.is_folder
                                                            ? "font-semibold text-yellow-800 dark:text-yellow-300"
                                                            : ""
                                                    }
                                                >
                                                    {track.name}
                                                </span>

                                                {/* Show parent folder name for tracks in folders */}
                                                {track.is_in_folder &&
                                                    track.parent_folder_name && (
                                                        <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 italic">
                                                            in{" "}
                                                            {
                                                                track.parent_folder_name
                                                            }
                                                        </span>
                                                    )}
                                            </span>
                                        </div>
                                    </td>

                                    {/* Routing indicator column */}
                                    <td className="p-2 text-center">
                                        {hasRouting(track.name, track.guid) ? (
                                            <button
                                                className={`inline-flex items-center justify-center w-6 h-6 rounded-full transition-colors
                                                    ${
                                                        expandedTrackId ===
                                                            getTrackUniqueId(
                                                                track.name,
                                                                track.guid
                                                            ) &&
                                                        expandedMode ===
                                                            "routing"
                                                            ? "bg-blue-500 text-white"
                                                            : "bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
                                                    }`}
                                                onClick={(e) =>
                                                    handleToggleExpand(
                                                        track,
                                                        "routing",
                                                        e
                                                    )
                                                }
                                                title={
                                                    expandedTrackId ===
                                                        getTrackUniqueId(
                                                            track.name,
                                                            track.guid
                                                        ) &&
                                                    expandedMode === "routing"
                                                        ? "Hide routing"
                                                        : "Show routing"
                                                }
                                                aria-label="Toggle routing"
                                            >
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className={`w-3.5 h-3.5 transition-transform duration-200 ${
                                                        expandedTrackId ===
                                                            getTrackUniqueId(
                                                                track.name,
                                                                track.guid
                                                            ) &&
                                                        expandedMode ===
                                                            "routing"
                                                            ? "rotate-180"
                                                            : ""
                                                    }`}
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M19 9l-7 7-7-7"
                                                    />
                                                </svg>
                                            </button>
                                        ) : (
                                            <span className="text-gray-400 dark:text-gray-600 text-sm">
                                                -
                                            </span>
                                        )}
                                    </td>

                                    {/* Automation indicator column */}
                                    <td className="p-2 text-center">
                                        {hasAutomation(track) ? (
                                            <button
                                                className={`inline-flex items-center justify-center w-6 h-6 rounded-full transition-colors
                                                    ${
                                                        expandedTrackId ===
                                                            getTrackUniqueId(
                                                                track.name,
                                                                track.guid
                                                            ) &&
                                                        expandedMode ===
                                                            "automation"
                                                            ? "bg-green-500 text-white"
                                                            : "bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
                                                    }`}
                                                onClick={(e) =>
                                                    handleToggleExpand(
                                                        track,
                                                        "automation",
                                                        e
                                                    )
                                                }
                                                title={
                                                    expandedTrackId ===
                                                        getTrackUniqueId(
                                                            track.name,
                                                            track.guid
                                                        ) &&
                                                    expandedMode ===
                                                        "automation"
                                                        ? "Hide automation"
                                                        : `Show automation (${getEffectiveAutomationCount(
                                                              track
                                                          )} active lanes)`
                                                }
                                                aria-label="Toggle automation"
                                            >
                                                <Activity className="w-3.5 h-3.5" />
                                            </button>
                                        ) : (
                                            <span className="text-gray-400 dark:text-gray-600 text-sm">
                                                -
                                            </span>
                                        )}
                                    </td>
                                    <td className="p-2 text-gray-700 dark:text-gray-300">
                                        {track.type}
                                    </td>
                                    <td className="p-2 text-center text-gray-700 dark:text-gray-300">
                                        {track.volume === 0 ? (
                                            <span title="Volume: -∞ dB">
                                                -∞ dB
                                            </span>
                                        ) : (
                                            <span
                                                title={`Volume: ${(
                                                    20 *
                                                    Math.log10(track.volume)
                                                ).toFixed(1)} dB`}
                                            >
                                                {(
                                                    20 *
                                                    Math.log10(track.volume)
                                                ).toFixed(1)}{" "}
                                                dB
                                            </span>
                                        )}
                                    </td>
                                    <td className="p-2 text-center">
                                        {hasPanAutomation(track) ? (
                                            <div
                                                className="inline-flex items-center px-2 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded text-sm font-medium cursor-pointer hover:bg-purple-200 dark:hover:bg-purple-800/50"
                                                title="This track's pan position is automated. Click to view automation."
                                                onClick={(e) =>
                                                    handlePanClick(e, track)
                                                }
                                            >
                                                {track.pan === 0
                                                    ? "C"
                                                    : `${
                                                          track.pan > 0
                                                              ? "R"
                                                              : "L"
                                                      } ${Math.abs(
                                                          Math.round(
                                                              track.pan * 100
                                                          )
                                                      )}%`}
                                                <Sliders className="ml-1 h-3 w-3" />
                                            </div>
                                        ) : (
                                            <span className="text-gray-700 dark:text-gray-300">
                                                {track.pan === 0
                                                    ? "C"
                                                    : `${
                                                          track.pan > 0
                                                              ? "R"
                                                              : "L"
                                                      } ${Math.abs(
                                                          Math.round(
                                                              track.pan * 100
                                                          )
                                                      )}%`}
                                            </span>
                                        )}
                                    </td>
                                    <td className="p-2">
                                        <div className="flex flex-wrap gap-1">
                                            {/* Chain oversampling indicator */}
                                            {track.chain_oversampling_rate !==
                                                undefined &&
                                                track.chain_oversampling_rate >
                                                    0 && (
                                                    <span
                                                        className="inline-block bg-cyan-100 dark:bg-cyan-900/30 text-cyan-700 dark:text-cyan-400 rounded px-2 py-0.5 text-xs font-medium"
                                                        title={`Chain Oversampling: ${
                                                            track.chain_oversampling_rate ===
                                                            1
                                                                ? "2x (88.2k/96k)"
                                                                : track.chain_oversampling_rate ===
                                                                  2
                                                                ? "4x (176.4k/192k)"
                                                                : track.chain_oversampling_rate ===
                                                                  3
                                                                ? "8x (352.8k/384k)"
                                                                : "16x (705.6k/768k)"
                                                        }`}
                                                    >
                                                        Chain OS:{" "}
                                                        {track.chain_oversampling_rate ===
                                                        1
                                                            ? "2x"
                                                            : track.chain_oversampling_rate ===
                                                              2
                                                            ? "4x"
                                                            : track.chain_oversampling_rate ===
                                                              3
                                                            ? "8x"
                                                            : "16x"}
                                                    </span>
                                                )}

                                            {track.fx.length > 0 ? (
                                                track.fx.map(
                                                    (
                                                        plugin: PluginAnalysisResult,
                                                        index: number
                                                    ) => {
                                                        // Helper function to get plugin display styling
                                                        const getPluginStyling =
                                                            () => {
                                                                if (
                                                                    plugin.is_bypassed
                                                                ) {
                                                                    return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300";
                                                                }
                                                                if (
                                                                    plugin.is_blacklisted_general ||
                                                                    plugin.is_blacklisted_master
                                                                ) {
                                                                    return "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300";
                                                                }
                                                                if (
                                                                    plugin.has_oversampling
                                                                ) {
                                                                    return "bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300";
                                                                }
                                                                return "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300";
                                                            };

                                                        // Helper function to get plugin tooltip
                                                        const getPluginTooltip =
                                                            () => {
                                                                const parts =
                                                                    [];

                                                                if (
                                                                    plugin.is_bypassed
                                                                ) {
                                                                    parts.push(
                                                                        "Bypassed"
                                                                    );
                                                                }

                                                                if (
                                                                    plugin.is_blacklisted_general
                                                                ) {
                                                                    parts.push(
                                                                        "Flagged for general use"
                                                                    );
                                                                }

                                                                if (
                                                                    plugin.is_blacklisted_master
                                                                ) {
                                                                    parts.push(
                                                                        "Flagged for master bus"
                                                                    );
                                                                }

                                                                if (
                                                                    plugin.category
                                                                ) {
                                                                    parts.push(
                                                                        `Category: ${plugin.category}`
                                                                    );
                                                                }

                                                                if (
                                                                    plugin.has_oversampling
                                                                ) {
                                                                    const osRate =
                                                                        plugin.oversampling_rate ===
                                                                        1
                                                                            ? "2x (88.2k/96k)"
                                                                            : plugin.oversampling_rate ===
                                                                              2
                                                                            ? "4x (176.4k/192k)"
                                                                            : plugin.oversampling_rate ===
                                                                              3
                                                                            ? "8x (352.8k/384k)"
                                                                            : "16x (705.6k/768k)";
                                                                    parts.push(
                                                                        `Oversampling: ${osRate}`
                                                                    );
                                                                } else {
                                                                    parts.push(
                                                                        "No oversampling"
                                                                    );
                                                                }

                                                                if (
                                                                    plugin.type
                                                                ) {
                                                                    parts.push(
                                                                        `Type: ${plugin.type}`
                                                                    );
                                                                }

                                                                return parts.join(
                                                                    " | "
                                                                );
                                                            };

                                                        return (
                                                            <span
                                                                key={index}
                                                                className={`inline-block ${getPluginStyling()} rounded px-2 py-0.5 text-xs font-medium`}
                                                                title={getPluginTooltip()}
                                                            >
                                                                {plugin.name ||
                                                                    "Unknown Plugin"}
                                                                {plugin.is_bypassed && (
                                                                    <span className="ml-1 text-red-500 dark:text-red-300">
                                                                        (bypassed)
                                                                    </span>
                                                                )}
                                                                {(plugin.is_blacklisted_general ||
                                                                    plugin.is_blacklisted_master) && (
                                                                    <span className="ml-1 text-orange-500 dark:text-orange-300">
                                                                        ⚠
                                                                    </span>
                                                                )}
                                                                {!plugin.is_bypassed &&
                                                                    plugin.has_oversampling && (
                                                                        <span className="ml-1 text-amber-500 dark:text-amber-300">
                                                                            {
                                                                                plugin.oversampling_rate
                                                                            }
                                                                            x
                                                                        </span>
                                                                    )}
                                                            </span>
                                                        );
                                                    }
                                                )
                                            ) : (
                                                <span className="text-gray-400 dark:text-gray-500">
                                                    None
                                                </span>
                                            )}
                                        </div>
                                    </td>
                                    <td className="p-2">
                                        <div
                                            className="w-6 h-6 rounded-md shadow-sm border border-gray-300 dark:border-gray-600"
                                            style={{
                                                backgroundColor: `#${track.color}`,
                                            }}
                                            title={`#${track.color}`}
                                        />
                                    </td>
                                </tr>

                                {/* Expandable section for routing diagram or automation display */}
                                <tr className="w-full">
                                    <td colSpan={8} className="p-0">
                                        <div
                                            className={`transition-all duration-300 ease-in-out overflow-hidden ${
                                                expandedTrackId ===
                                                getTrackUniqueId(
                                                    track.name,
                                                    track.guid
                                                )
                                                    ? "max-h-[300px] opacity-100 border-t border-gray-200 dark:border-gray-700"
                                                    : "max-h-0 opacity-0"
                                            }`}
                                            style={{
                                                minHeight:
                                                    expandedTrackId ===
                                                    getTrackUniqueId(
                                                        track.name,
                                                        track.guid
                                                    )
                                                        ? undefined
                                                        : "0px",
                                                visibility:
                                                    expandedTrackId ===
                                                    getTrackUniqueId(
                                                        track.name,
                                                        track.guid
                                                    )
                                                        ? "visible"
                                                        : "hidden",
                                            }}
                                        >
                                            {expandedMode === "routing" ? (
                                                <TrackRoutingDiagram
                                                    trackName={track.name}
                                                    trackGuid={track.guid}
                                                    routing={routing}
                                                />
                                            ) : (
                                                <AutomationLaneDisplay
                                                    lanes={
                                                        track.automation_lanes ||
                                                        []
                                                    }
                                                />
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            </React.Fragment>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default TrackList;
