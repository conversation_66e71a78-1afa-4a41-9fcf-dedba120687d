import React from "react";
import AnalysisCard from "./AnalysisCard";
import IssueCard from "./IssueCard";
import { useSessionStore, MasteringAnalysisIssue } from "../store/sessionStore";

interface PluginAnalysisSectionProps {
    genre: string;
    strictMode: boolean;
}

const PluginAnalysisSection: React.FC<PluginAnalysisSectionProps> = ({
    genre,
    strictMode,
}) => {
    const { sessionData } = useSessionStore();

    if (!sessionData?.masteringAnalysis) {
        return (
            <AnalysisCard title="Plugin Analysis" status="pass">
                <div className="text-gray-600">
                    No mastering analysis data available.
                </div>
            </AnalysisCard>
        );
    }

    const masteringAnalysis = sessionData.masteringAnalysis;

    // Filter issues related to plugin analysis
    const pluginIssues = masteringAnalysis.issues.filter(
        (issue: MasteringAnalysisIssue) =>
            issue.category === "Plugin Analysis" ||
            issue.category === "Plugin Analysis - Master Bus"
    );

    // Get detailed plugin analysis data
    const pluginAnalysisDetails =
        masteringAnalysis.detailed_analysis?.plugin_analysis || [];
    const masterBusAnalysisDetails =
        masteringAnalysis.detailed_analysis?.master_bus_analysis || {};

    const getOverallStatus = () => {
        const hasCritical = pluginIssues.some(
            (issue) => issue.severity === "critical"
        );
        const hasWarning = pluginIssues.some(
            (issue) => issue.severity === "warning"
        );

        if (hasCritical) return "fail";
        if (hasWarning) return "warning";
        return "pass";
    };

    const groupIssuesBySeverity = () => {
        const grouped = {
            critical: pluginIssues.filter(
                (issue) => issue.severity === "critical"
            ),
            warning: pluginIssues.filter(
                (issue) => issue.severity === "warning"
            ),
            info: pluginIssues.filter((issue) => issue.severity === "info"),
        };
        return grouped;
    };

    const groupedIssues = groupIssuesBySeverity();
    const hasAnyIssues = pluginIssues.length > 0;

    return (
        <AnalysisCard title="Plugin Analysis" status={getOverallStatus()}>
            <div className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                    Analysis for <strong>{genre}</strong> genre
                    {strictMode && " (Strict Mode)"}
                </div>

                {!hasAnyIssues && (
                    <div className="text-green-700 font-medium">
                        ✅ No problematic plugins detected for mastering
                        preparation
                    </div>
                )}

                {/* Critical Issues */}
                {groupedIssues.critical.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-red-800">
                            ❌ Critical Plugin Issues
                        </h4>
                        {groupedIssues.critical.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Warning Issues */}
                {groupedIssues.warning.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-yellow-800">
                            ⚠️ Plugin Warnings
                        </h4>
                        {groupedIssues.warning.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Info Issues */}
                {groupedIssues.info.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-blue-800">
                            ℹ️ Plugin Information
                        </h4>
                        {groupedIssues.info.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Detailed Analysis Summary */}
                {pluginAnalysisDetails.length > 0 && (
                    <div className="mt-6 pt-4 border-t border-gray-200">
                        <h4 className="font-semibold text-gray-800 mb-3">
                            Plugin Analysis Summary
                        </h4>
                        <div className="text-sm text-gray-600 space-y-2">
                            <div>
                                <strong>Tracks with plugin issues:</strong>{" "}
                                {pluginAnalysisDetails.length}
                            </div>
                            <div>
                                <strong>Total plugin-related issues:</strong>{" "}
                                {pluginIssues.length}
                            </div>
                        </div>
                    </div>
                )}

                {/* Master Bus Analysis Summary */}
                {masterBusAnalysisDetails.issues_found &&
                    masterBusAnalysisDetails.issues_found.length > 0 && (
                        <div className="mt-4 p-3 bg-gray-50 rounded">
                            <h5 className="font-medium text-gray-800 mb-2">
                                Master Bus Chain Analysis
                            </h5>
                            <div className="text-sm text-gray-600">
                                {masterBusAnalysisDetails.issues_found.length}{" "}
                                issue(s) detected in the master bus chain.
                            </div>
                        </div>
                    )}
            </div>
        </AnalysisCard>
    );
};

export default PluginAnalysisSection;
