import React from "react";

interface HealthIndicatorProps {
    score: number; // Score from 0 to 100
}

const HealthIndicator: React.FC<HealthIndicatorProps> = React.memo(({ score }) => {
    const statusColor = React.useMemo(() => {
        if (score >= 80) return "bg-green-500"; // Green for good scores
        if (score >= 50) return "bg-yellow-500"; // Yellow for medium scores
        return "bg-red-500"; // Red for low scores
    }, [score]);

    const normalizedScore = React.useMemo(() => Math.max(0, Math.min(100, score)), [score]);

    return (
        <div className="w-full bg-gray-600 rounded-full h-2.5 dark:bg-gray-700 overflow-hidden">
            <div
                className={`h-2.5 rounded-full ${statusColor}`}
                style={{ width: `${normalizedScore}%` }}
                role="progressbar"
                aria-valuenow={normalizedScore}
                aria-valuemin={0}
                aria-valuemax={100}
                aria-label={`Health score: ${normalizedScore}%`}
            ></div>
        </div>
    );
});

export default HealthIndicator;
