import { useState, useRef, DragEvent, ChangeEvent } from "react";
import { useSessionStore } from "../store/sessionStore";

interface FileUploadProps {
    onFileUpload: (file: File) => void;
    acceptedFileTypes?: string;
    maxSizeMB?: number;
}

const FileUpload = ({
    onFileUpload,
    acceptedFileTypes = ".rpp",
    maxSizeMB = 50, // Default max size: 50MB
}: FileUploadProps) => {
    const [isDragging, setIsDragging] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { isLoading } = useSessionStore();

    const handleDragEnter = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };

    const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isDragging) setIsDragging(true);
    };

    const validateFile = (file: File): boolean => {
        setError(null);

        // Check file type
        if (
            acceptedFileTypes &&
            !file.name.toLowerCase().endsWith(acceptedFileTypes.toLowerCase())
        ) {
            setError(
                `Invalid file type. Please upload a ${acceptedFileTypes} file.`
            );
            return false;
        }

        // Check file size
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            setError(`File is too large. Maximum size is ${maxSizeMB}MB.`);
            return false;
        }

        return true;
    };

    const handleDrop = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);

        const file = e.dataTransfer.files[0];
        if (file) {
            if (validateFile(file)) {
                onFileUpload(file);
            }
        }
    };

    const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (validateFile(file)) {
                onFileUpload(file);
            }
        }
    };

    const handleButtonClick = () => {
        fileInputRef.current?.click();
    };

    // Show loading state when file is being analyzed
    if (isLoading) {
        return (
            <div className="p-8 rounded-lg border-2 border-gray-300 dark:border-gray-700 w-full max-w-xl transition-colors duration-200 bg-gray-50 dark:bg-gray-800/50">
                <h2 className="text-xl mb-6 text-center text-gray-700 dark:text-gray-200">
                    Analyzing RPP File...
                </h2>
                <div className="flex justify-center mb-6">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                </div>
                <p className="text-center text-sm text-gray-500 dark:text-gray-400">
                    Extracting metadata, tracks, plugins, and routing
                    information
                </p>
            </div>
        );
    }

    // File upload UI when not loading
    return (
        <div
            className={`p-8 rounded-lg border-2 border-dashed 
                ${
                    isDragging
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-400 dark:border-gray-600"
                } 
                w-full max-w-xl transition-colors duration-200`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
        >
            <h2 className="text-xl mb-4 text-center">
                Upload a Reaper Project (.rpp/.RPP) File
            </h2>
            <div className="flex justify-center">
                <button
                    onClick={handleButtonClick}
                    className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                    type="button"
                >
                    Select File
                </button>
                <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept={acceptedFileTypes}
                    onChange={handleFileInputChange}
                />
            </div>
            <p className="text-center mt-4 text-sm text-gray-500 dark:text-gray-400">
                Or drag and drop a file here
            </p>
            {error && (
                <p className="text-center mt-4 text-sm text-red-500">{error}</p>
            )}
        </div>
    );
};

export default FileUpload;
