import React from "react";
import { AutomationLane } from "../store/sessionStore";

interface AutomationLaneDisplayProps {
    lanes: AutomationLane[];
}

const AutomationLaneDisplay: React.FC<AutomationLaneDisplayProps> = ({
    lanes,
}) => {
    // If no lanes, show a message
    if (lanes.length === 0) {
        return (
            <div className="p-2 bg-gray-50 dark:bg-gray-900 text-center text-gray-500 dark:text-gray-400 py-4">
                No automation lanes found for this track.
            </div>
        );
    }

    // Count lanes with effective automation (more than 1 point)
    const lanesWithAutomation = lanes.filter(
        (lane) => lane.effective_points > 0
    );

    return (
        <div
            className="p-2 bg-gray-50 dark:bg-gray-900 overflow-x-auto"
            style={{ maxHeight: "300px" }}
        >
            <div className="p-4" style={{ paddingLeft: 0 }}>
                <h3 className="font-bold text-gray-900 dark:text-gray-100 mb-3 text-center">
                    Automation Lanes ({lanesWithAutomation.length} active)
                </h3>

                {/* Header Row */}
                <div className="flex mb-2">
                    <div
                        className="w-1/3 text-base font-medium text-gray-700 dark:text-gray-300 text-left m-0"
                        style={{ paddingLeft: 0, marginLeft: 0 }}
                    >
                        Parameter
                    </div>
                    <div className="w-1/3 text-center text-base font-medium text-gray-700 dark:text-gray-300">
                        Status
                    </div>
                    <div className="w-1/3 text-right text-base font-medium text-gray-700 dark:text-gray-300">
                        Points
                    </div>
                </div>

                {/* List of automation lanes */}
                <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                    {lanes.map((lane, index) => (
                        <li
                            key={`lane-${index}`}
                            className="flex items-center text-gray-700 dark:text-gray-300 p-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-150"
                        >
                            {/* Parameter name/display name - Complete restructuring for proper left alignment */}
                            <div className="w-1/3 flex pl-0 items-start">
                                {/* Fixed position for blue dot */}
                                <div className="flex-shrink-0 w-6 text-left">
                                    <span
                                        className={`inline-block w-3 h-3 rounded-full ${
                                            lane.effective_points > 0
                                                ? "bg-blue-500"
                                                : "bg-gray-400 dark:bg-gray-600"
                                        }`}
                                    ></span>
                                </div>
                                {/* Parameter text with explicit left alignment */}
                                <div className="text-left">
                                    <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                                        {lane.display_name}
                                    </span>
                                    {lane.plugin_name && (
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                                            ({lane.plugin_name})
                                        </span>
                                    )}
                                </div>
                            </div>

                            {/* Status indicator */}
                            <div className="w-1/3 text-center">
                                <span
                                    className={`inline-block rounded px-2 py-0.5 text-xs font-medium
                                        ${
                                            lane.is_active
                                                ? "bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300"
                                                : "bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300"
                                        }`}
                                >
                                    {lane.is_active ? "Active" : "Inactive"}
                                </span>
                            </div>

                            {/* Points count */}
                            <div className="w-1/3 text-right">
                                <span className="text-xs font-medium px-2 py-0.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">
                                    {lane.effective_points} points
                                </span>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
};

export default AutomationLaneDisplay;
