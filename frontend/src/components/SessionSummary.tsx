import { useSessionStore } from "../store/sessionStore";
import {
    HardDrive,
    Layers,
    FileJson,
    Users,
    Music,
    Settings,
} from "lucide-react";
import { Link } from "react-router-dom";

const SessionSummary = () => {
    const { sessionData } = useSessionStore();

    if (!sessionData) {
        return null; // Don't render anything if no session data
    }

    // Calculate stats
    const totalTracks = sessionData.tracks.length;

    // Count unique plugins
    const uniquePlugins = new Set();
    sessionData.tracks.forEach((track) => {
        track.fx.forEach((plugin) => uniquePlugins.add(plugin));
    });
    const pluginCount = uniquePlugins.size;

    // Count tracks with routing
    const tracksWithRouting = new Set();
    sessionData.routing.forEach((route) => {
        tracksWithRouting.add(route.source);
        tracksWithRouting.add(route.destination);
    });
    const routingCount = tracksWithRouting.size;

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mt-6">
            <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
                Session Summary: {sessionData.filename}
            </h2>

            {/* Project Metadata Section - styled with cards similar to the statistics */}
            <div className="mb-8">
                <div className="flex items-center mb-4">
                    <Settings className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                        Project Settings
                    </h3>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    {/* REAPER Version */}
                    <div className="bg-indigo-50 dark:bg-indigo-900/30 p-5 rounded-lg">
                        <p className="text-sm text-indigo-700 dark:text-indigo-300 uppercase font-medium mb-1">
                            REAPER Version
                        </p>
                        <p className="text-lg font-bold text-indigo-800 dark:text-indigo-200">
                            {sessionData.metadata.reaper_version}
                        </p>
                    </div>

                    {/* Sample Rate */}
                    <div className="bg-teal-50 dark:bg-teal-900/30 p-5 rounded-lg">
                        <p className="text-sm text-teal-700 dark:text-teal-300 uppercase font-medium mb-1">
                            Sample Rate
                        </p>
                        <p className="text-lg font-bold text-teal-800 dark:text-teal-200">
                            {sessionData.metadata.sample_rate}{" "}
                            <span className="text-sm">Hz</span>
                        </p>
                    </div>

                    {/* Tempo */}
                    <div className="bg-amber-50 dark:bg-amber-900/30 p-5 rounded-lg">
                        <p className="text-sm text-amber-700 dark:text-amber-300 uppercase font-medium mb-1">
                            Tempo
                        </p>
                        <p className="text-lg font-bold text-amber-800 dark:text-amber-200">
                            {sessionData.metadata.tempo}{" "}
                            <span className="text-sm">BPM</span>
                        </p>
                    </div>

                    {/* Time Signature */}
                    <div className="bg-rose-50 dark:bg-rose-900/30 p-5 rounded-lg">
                        <p className="text-sm text-rose-700 dark:text-rose-300 uppercase font-medium mb-1">
                            Time Signature
                        </p>
                        <p className="text-lg font-bold text-rose-800 dark:text-rose-200">
                            {sessionData.metadata.time_signature}
                        </p>
                    </div>
                </div>

                {/* Project Title - if available */}
                {sessionData.metadata.title && (
                    <div className="bg-gray-50 dark:bg-gray-800 p-5 rounded-lg">
                        <p className="text-sm text-gray-500 dark:text-gray-400 uppercase font-medium mb-1">
                            Project Title
                        </p>
                        <p className="text-lg font-bold text-gray-900 dark:text-gray-100">
                            {sessionData.metadata.title}
                        </p>
                    </div>
                )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Track Count */}
                <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg flex items-center">
                    <div className="bg-blue-100 dark:bg-blue-800 p-3 rounded-full mr-4">
                        <Layers className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                            Tracks
                        </p>
                        <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">
                            {totalTracks}
                        </p>
                        <Link
                            to="/tracks"
                            className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                        >
                            View details →
                        </Link>
                    </div>
                </div>

                {/* Plugin Count */}
                <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg flex items-center">
                    <div className="bg-purple-100 dark:bg-purple-800 p-3 rounded-full mr-4">
                        <FileJson className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                        <p className="text-sm text-purple-700 dark:text-purple-300">
                            Unique Plugins
                        </p>
                        <p className="text-2xl font-bold text-purple-800 dark:text-purple-200">
                            {pluginCount}
                        </p>
                        <Link
                            to="/plugins"
                            className="text-xs text-purple-600 dark:text-purple-400 hover:underline"
                        >
                            View details →
                        </Link>
                    </div>
                </div>

                {/* Routing Count */}
                <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg flex items-center">
                    <div className="bg-green-100 dark:bg-green-800 p-3 rounded-full mr-4">
                        <Users className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                        <p className="text-sm text-green-700 dark:text-green-300">
                            Tracks with Routing
                        </p>
                        <p className="text-2xl font-bold text-green-800 dark:text-green-200">
                            {routingCount}
                        </p>
                        <Link
                            to="/tracks"
                            className="text-xs text-green-600 dark:text-green-400 hover:underline"
                        >
                            View routing →
                        </Link>
                    </div>
                </div>
            </div>

            <div className="mt-4 text-center">
                <Link
                    to="/export"
                    className="inline-block px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                    Export Session Data
                </Link>
            </div>
        </div>
    );
};

export default SessionSummary;
