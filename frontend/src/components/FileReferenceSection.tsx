import React from "react";
import AnalysisCard from "./AnalysisCard";
import IssueCard from "./IssueCard";
import { useSessionStore, MasteringAnalysisIssue } from "../store/sessionStore";

interface FileReferenceSectionProps {
    metadata: any;
    tracks: any[];
}

const FileReferenceSection: React.FC<FileReferenceSectionProps> = ({
    metadata,
    tracks,
}) => {
    const { sessionData } = useSessionStore();

    if (!sessionData?.masteringAnalysis) {
        return (
            <AnalysisCard title="File Reference Analysis" status="pass">
                <div className="text-gray-600">
                    No mastering analysis data available.
                </div>
            </AnalysisCard>
        );
    }

    const masteringAnalysis = sessionData.masteringAnalysis;

    // Filter issues related to file references
    const fileRefIssues = masteringAnalysis.issues.filter(
        (issue: MasteringAnalysisIssue) => issue.category === "File References"
    );

    // Get detailed file reference analysis data
    const fileRefAnalysisDetails =
        masteringAnalysis.detailed_analysis?.file_reference_analysis || {};

    const getOverallStatus = () => {
        const hasCritical = fileRefIssues.some(
            (issue) => issue.severity === "critical"
        );
        const hasWarning = fileRefIssues.some(
            (issue) => issue.severity === "warning"
        );

        if (hasCritical) return "fail";
        if (hasWarning) return "warning";
        if (fileRefIssues.length > 0) return "info";
        return "pass";
    };

    const groupIssuesBySeverity = () => {
        const grouped = {
            critical: fileRefIssues.filter(
                (issue) => issue.severity === "critical"
            ),
            warning: fileRefIssues.filter(
                (issue) => issue.severity === "warning"
            ),
            info: fileRefIssues.filter((issue) => issue.severity === "info"),
        };
        return grouped;
    };

    const groupedIssues = groupIssuesBySeverity();
    const hasAnyIssues = fileRefIssues.length > 0;

    const getSummaryMessages = () => {
        return (
            fileRefAnalysisDetails.summary_messages ||
            metadata.file_reference_analysis?.summary_messages ||
            []
        );
    };

    return (
        <AnalysisCard
            title="File Reference Analysis"
            status={getOverallStatus()}
        >
            <div className="space-y-4">
                {!hasAnyIssues && (
                    <div className="text-green-700 font-medium">
                        ✅ All file references are properly organized and
                        portable
                    </div>
                )}

                {getSummaryMessages().length > 0 && (
                    <div className="bg-gray-100 border border-gray-300 rounded p-3">
                        <h4 className="font-semibold text-gray-800 mb-2">
                            📋 Analysis Summary
                        </h4>
                        <ul className="text-sm text-gray-700 space-y-1">
                            {getSummaryMessages().map(
                                (message: string, idx: number) => (
                                    <li key={idx}>{message}</li>
                                )
                            )}
                        </ul>
                    </div>
                )}

                {/* Critical Issues */}
                {groupedIssues.critical.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-red-800">
                            ❌ Critical File Reference Issues
                        </h4>
                        {groupedIssues.critical.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Warning Issues */}
                {groupedIssues.warning.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-yellow-800">
                            ⚠️ File Reference Warnings
                        </h4>
                        {groupedIssues.warning.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Info Issues */}
                {groupedIssues.info.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="font-semibold text-blue-800">
                            ℹ️ File Reference Information
                        </h4>
                        {groupedIssues.info.map((issue) => (
                            <IssueCard key={issue.id} issue={issue} />
                        ))}
                    </div>
                )}

                {/* Detailed Analysis Summary */}
                {fileRefAnalysisDetails.problematic_files && (
                    <div className="mt-6 pt-4 border-t border-gray-200">
                        <h4 className="font-semibold text-gray-800 mb-3">
                            File Reference Analysis Summary
                        </h4>
                        <div className="text-sm text-gray-600 space-y-2">
                            <div>
                                <strong>Files with issues:</strong>{" "}
                                {
                                    fileRefAnalysisDetails.problematic_files
                                        .length
                                }
                            </div>
                            <div>
                                <strong>Total file reference issues:</strong>{" "}
                                {fileRefIssues.length}
                            </div>
                        </div>
                    </div>
                )}

                {/* Recommendations */}
                <div className="bg-gray-100 border border-gray-300 rounded p-3">
                    <h4 className="font-semibold text-gray-800 mb-2">
                        💡 Recommendations
                    </h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                        <li>• Use relative paths within your project folder</li>
                        <li>
                            • Keep all media files in "Media/" or "Audio/"
                            subfolders
                        </li>
                        <li>
                            • Ensure all files match the project sample rate (
                            {metadata.sample_rate}Hz)
                        </li>
                        {metadata.project_bit_depth && (
                            <li>
                                • Ensure all files match the project bit depth (
                                {metadata.project_bit_depth}bit)
                            </li>
                        )}
                        <li>
                            • Use "Project &gt; Clean current project directory"
                            in REAPER
                        </li>
                    </ul>
                </div>
            </div>
        </AnalysisCard>
    );
};

export default FileReferenceSection;
