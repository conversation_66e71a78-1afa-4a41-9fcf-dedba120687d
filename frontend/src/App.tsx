import { useEffect } from "react";
import "./App.css";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import TopNavBar from "./components/TopNavBar";
import DashboardPage from "./pages/DashboardPage";
import TracksPage from "./pages/TracksPage";
import PluginsPage from "./pages/PluginsPage";
import ExportPage from "./pages/ExportPage";
import MasteringReportPage from "./pages/MasteringReportPage";
import SessionHygienePage from "./pages/SessionHygienePage";

// Import global store for dark mode
import { create } from "zustand";

// Define a store for UI preferences
interface UIPreferencesState {
    isDarkMode: boolean;
    toggleDarkMode: () => void;
}

export const useUIStore = create<UIPreferencesState>((set) => ({
    isDarkMode: true, // Default to dark mode
    toggleDarkMode: () =>
        set((state) => {
            const newDarkMode = !state.isDarkMode;

            // Apply dark mode to HTML element (required for Tailwind dark mode)
            if (newDarkMode) {
                document.documentElement.classList.add("dark");
            } else {
                document.documentElement.classList.remove("dark");
            }

            return { isDarkMode: newDarkMode };
        }),
}));

function App() {
    const { isDarkMode } = useUIStore();

    // Initialize dark mode on component mount
    useEffect(() => {
        // Apply dark mode to HTML element on initial load if isDarkMode is true
        if (isDarkMode) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }
    }, []);

    return (
        <Router>
            <div className="min-h-screen w-full bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-200">
                {/* Top Navigation with light/dark toggle */}
                <TopNavBar />

                {/* Main Content */}
                <div className="pt-4 flex justify-center">
                    <div className="w-full max-w-7xl px-4">
                        <Routes>
                            <Route path="/" element={<DashboardPage />} />
                            <Route path="/tracks" element={<TracksPage />} />
                            <Route path="/plugins" element={<PluginsPage />} />
                            <Route
                                path="/mastering"
                                element={<MasteringReportPage />}
                            />
                            <Route 
                                path="/hygiene" 
                                element={<SessionHygienePage />} 
                            />
                            <Route path="/export" element={<ExportPage />} />
                            <Route path="*" element={<DashboardPage />} />
                        </Routes>
                    </div>
                </div>

                <footer className="border-t border-gray-300 dark:border-gray-700 p-4 mt-8 bg-white dark:bg-gray-800">
                    <div className="container mx-auto text-center text-gray-500 dark:text-gray-400 text-sm">
                        SessionView - Analyze and document Reaper projects
                    </div>
                </footer>
            </div>
        </Router>
    );
}

export default App;
