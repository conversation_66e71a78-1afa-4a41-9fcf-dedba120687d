import { create } from "zustand";

// Define type for automation point
export interface AutomationPoint {
    time: number; // Position in seconds
    value: number; // Value at this point
    shape: number; // Curve shape (0=linear, 1=square, etc.)
    tension?: number; // Bezier tension if applicable
}

// Define type for automation lane
export interface AutomationLane {
    display_name: string;
    guid: string;
    is_active: boolean;
    is_visible: boolean;
    is_armed: boolean;
    lane_height: number;
    default_shape: number;
    parameter_name?: string;
    parameter_index?: number;
    plugin_name?: string;
    min_value?: number;
    max_value?: number;
    default_value?: number;
    points: AutomationPoint[];
    effective_points: number; // Points beyond the default one
}

// Define type for plugin analysis result (matches backend PluginAnalysisResult)
export interface PluginAnalysisResult {
    name?: string;
    type?: string; // VST, JS, AU, etc. (changed from plugin_type)
    guid?: string;
    raw_text?: string;
    encoded_params?: string;
    program_chunk?: string;
    is_bypassed: boolean;
    has_oversampling: boolean;
    oversampling_rate: number;
    is_blacklisted_general: boolean;
    is_blacklisted_master: boolean;
    would_be_blacklisted_general_if_active: boolean;
    would_be_blacklisted_master_if_active: boolean;
    category: string;
    parameters?: { [key: string]: any };
    automation?: AutomationLane[];
    track_guid: string;
    track_name: string;
    // Legacy compatibility fields (optional)
    plugin_type?: string; // For backward compatibility, maps to 'type'
    is_finalizer_plugin?: boolean;
    oversampling_recommended?: boolean;
    oversampling_enabled?: boolean;
    is_limiter?: boolean;
    is_metering_plugin?: boolean;
    is_mixing_plugin_on_master_candidate?: boolean;
}

// Legacy FX interface for backward compatibility
export interface FX extends PluginAnalysisResult {
    plugin_type: string; // Required for legacy components
}

// Define type for the track
export interface Track {
    guid: string;
    name: string;
    type: string;
    fx: PluginAnalysisResult[]; // Updated to use PluginAnalysisResult
    color: string;
    folder_depth: number;
    is_folder: boolean;
    parent_folder_name?: string; // Name of the parent folder if track is in a folder
    is_in_folder?: boolean; // True if track is inside a folder
    muted: boolean;
    soloed: boolean;
    volume: number; // Added volume
    pan: number; // Added pan
    chain_oversampling_rate?: number; // 0=None, 1=2x, 2=4x, 3=8x, 4=16x
    automation_lanes: AutomationLane[]; // Automation envelopes
    items?: any[]; // Items on the track
    volume_automated?: boolean; // Flag indicating if the track volume is automated
    pan_automated?: boolean; // Flag indicating if the track pan is automated
    // Track-specific analysis flags
    is_record_armed?: boolean; // Track is record-armed
    has_bypassed_fx_in_chain?: boolean; // Track has bypassed FX plugins
    // Mastering analysis flags
    has_blacklisted_plugins_general?: boolean;
    has_blacklisted_plugins_master?: boolean;
    has_finalizer_plugins?: boolean;
    has_oversampling_issues?: boolean;
    is_hygiene_issue?: boolean; // For generic names, not in folder, empty items, etc.
    gain_staging_warning?: string; // e.g., "Too Hot", "Too Low"
    has_missing_media?: boolean;
    has_sample_rate_mismatch?: boolean;
    has_unused_media?: boolean; // Post-MVP
    has_master_bus_automation?: boolean; // For master track volume/pan automation
    has_limiter_on_master?: boolean;
    has_clipper_on_master?: boolean;
    // Master Bus Chain Analysis Flags
    has_multiple_limiters_on_master?: boolean;
    limiter_not_last_on_master?: boolean;
    has_mixing_plugins_on_master?: boolean;
    has_problematic_master_chain_order?: boolean;
    // Hygiene flags
    has_duplicate_name?: boolean;
    has_empty_items?: boolean;
    has_generic_name?: boolean;
    not_in_folder_structure?: boolean;
    hygiene_details?: { [key: string]: string };
}

// Optimized ensure defaults with memoization
const trackDefaults = new WeakMap();

export const ensureDefaults = (data: any): SessionData => {
    // Early return if no data
    if (!data || !data.tracks) return data;
    
    // Check if we've already processed this data
    if (trackDefaults.has(data)) {
        return trackDefaults.get(data);
    }
    
    // Process tracks to ensure they have automation_lanes
    const tracks = data.tracks.map((track: any) => ({
        ...track,
        // Ensure automation_lanes always exists
        automation_lanes: track.automation_lanes || [],
    }));

    const result = {
        ...data,
        tracks,
    };
    
    // Cache the result
    trackDefaults.set(data, result);
    return result;
};

// Define type for routing connections
export interface RoutingConnection {
    source: string;
    destination: string;
    type: string;
    source_guid: string;
    destination_guid: string;
    volume?: number;
    pan?: number;
    muted?: boolean;
    src_channel?: number;
    dest_channel?: number;
    channels?: number;
    volume_automated?: boolean;
    pan_automated?: boolean;
    mute_automated?: boolean;
}

// Define type for metadata
export interface ProjectMetadata {
    filename: string;
    reaper_version: string;
    save_timestamp: string;
    title?: string;
    notes?: string;
    has_master_track: boolean;
    sample_rate: number;
    tempo: number;
    time_signature: string;
    // Hygiene flags
    has_missing_markers?: boolean;
    has_missing_regions?: boolean;
    has_missing_notes?: boolean;
    project_bit_depth?: number;
    file_reference_analysis?: {
        absolute_paths_detected?: boolean;
        system_temp_paths_detected?: boolean;
        external_paths_detected?: boolean;
        sample_rate_mismatches_detected?: boolean;
        bit_depth_mismatches_detected?: boolean;
        problematic_file_references?: any[];
        summary_messages?: string[];
    };
}

// Define type for the session data
export interface SessionData {
    filename: string;
    size?: number;
    metadata: ProjectMetadata;
    tracks: Track[];
    routing: RoutingConnection[];
    masteringAnalysis: MasteringAnalysis | null;
    hygieneAnalysis: MasteringAnalysis | null;
}

export interface MasteringAnalysisIssue {
    id: string; // A unique identifier for the issue
    rule_id: string; // The rule that triggered this issue
    category: string; // e.g., "Plugin Analysis", "Master Bus Analysis", "Session Hygiene", "File References"
    severity: "critical" | "warning" | "info" | "pass";
    message: string;
    recommendation?: string;
    is_mastering_critical: boolean; // True for mastering readiness issues, False for general hygiene
    details_key?: string; // Optional key to link to more detailed data
    affected_elements?: Array<{
        type: string; // e.g., "track", "item", "plugin", "project"
        guid?: string;
        name?: string;
        details?: any;
    }>;
}

export interface IssueGroup {
    rule_id: string;
    category: string;
    severity: string;
    title: string;
    description: string;
    count: number;
    is_mastering_critical: boolean;
    affected_elements: Array<{
        type: string;
        guid?: string;
        name?: string;
        details?: any;
    }>;
    sample_issue?: MasteringAnalysisIssue;
}

export interface GroupedMasteringAnalysis {
    overall_health_score: number;
    genre: string;
    strict_mode: boolean;
    summary_metrics: {
        critical_issues_count: number;
        warning_issues_count: number;
        info_issues_count: number;
        passed_checks_count: number;
    };
    issue_groups: IssueGroup[];
    ungrouped_issues: MasteringAnalysisIssue[];
    detailed_analysis: {
        plugin_analysis?: any;
        master_bus_analysis?: any;
        session_hygiene_analysis?: any;
        file_reference_analysis?: any;
        item_property_analysis?: any;
        project_settings_analysis?: any;
        track_specific_issues?: any;
    };
}

interface MasteringAnalysis {
    overall_health_score: number; // 0-100
    genre: string;
    strict_mode: boolean;
    summary_metrics: {
        critical_issues_count: number;
        warning_issues_count: number;
        info_issues_count: number;
        passed_checks_count: number;
    };
    issues: MasteringAnalysisIssue[]; // Consolidated list of all issues
    detailed_analysis: {
        plugin_analysis?: any;
        master_bus_analysis?: any;
        session_hygiene_analysis?: any;
        file_reference_analysis?: any;
        item_property_analysis?: any;
        project_settings_analysis?: any;
        track_specific_issues?: any;
    };
}

// Define the store state
interface SessionState {
    // Session data
    sessionData: SessionData | null;
    masteringAnalysis: MasteringAnalysis | null;
    // Loading state
    isLoading: boolean;
    // Error state
    error: string | null;
    // Selected track for routing display, etc.
    selectedTrackName: string | null;

    // Actions
    setSessionData: (
        data:
            | SessionData
            | null
            | ((prev: SessionData | null) => SessionData | null)
    ) => void;
    setIsLoading: (isLoading: boolean) => void;
    setError: (error: string | null) => void;
    setSelectedTrackName: (trackName: string | null) => void;
    toggleTrackSelection: (trackName: string) => void;
    clearSession: () => void;
}

// Create the session store
export const useSessionStore = create<SessionState>((set) => ({
    // Initial state
    sessionData: null,
    masteringAnalysis: null,
    isLoading: false,
    error: null,
    selectedTrackName: null,

    // Actions with optimized state updates
    setSessionData: (data) =>
        set((state) => {
            const newData = typeof data === "function" ? data(state.sessionData) : data;
            // Only update if data actually changed
            if (state.sessionData === newData) return state;
            return { sessionData: newData };
        }),
    setIsLoading: (isLoading) =>
        set((state) => {
            // Only update if loading state changed
            if (state.isLoading === isLoading) return state;
            return { isLoading };
        }),
    setError: (error) =>
        set((state) => {
            // Only update if error changed
            if (state.error === error) return state;
            return { error };
        }),
    setSelectedTrackName: (trackName) =>
        set((state) => {
            // Only update if track name changed
            if (state.selectedTrackName === trackName) return state;
            return { selectedTrackName: trackName };
        }),
    toggleTrackSelection: (trackName) =>
        set((state) => {
            const newSelection = state.selectedTrackName === trackName ? null : trackName;
            // Only update if selection actually changed
            if (state.selectedTrackName === newSelection) return state;
            return { selectedTrackName: newSelection };
        }),
    clearSession: () =>
        set((state) => {
            // Only clear if there's actually data to clear
            if (!state.sessionData && !state.error && !state.selectedTrackName) return state;
            return {
                sessionData: null,
                error: null,
                selectedTrackName: null,
            };
        }),
}));
