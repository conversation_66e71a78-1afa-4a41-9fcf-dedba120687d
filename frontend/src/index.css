@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles below */
:root {
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;

    color-scheme: light dark;
    color: rgba(255, 255, 255, 0.87);
    background-color: #242424;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* The styles below will be gradually replaced with Tailwind classes */
@layer components {
    /* Example of extracting component styles into Tailwind */
    .btn-primary {
        @apply rounded-lg border border-transparent px-4 py-2 text-base font-medium 
    bg-[#1a1a1a] cursor-pointer transition-colors hover:border-[#646cff] 
    focus:outline-[#646cff] dark:bg-[#1a1a1a] dark:hover:border-[#646cff];
    }
}

/* Legacy styles that will be migrated to Tailwind classes */
a {
    font-weight: 500;
    color: #646cff;
    text-decoration: inherit;
}
a:hover {
    color: #535bf2;
}

body {
    margin: 0;
    min-width: 320px;
    min-height: 100vh;
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
}

@media (prefers-color-scheme: light) {
    :root {
        color: #213547;
        background-color: #ffffff;
    }
    a:hover {
        color: #747bff;
    }
    button {
        background-color: #f9f9f9;
    }
}
