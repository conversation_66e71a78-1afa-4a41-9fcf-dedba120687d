import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import GenreSelectionPanel from "../components/GenreSelectionPanel";
import SessionHygieneSection from "../components/SessionHygieneSection";
import axios from "axios";
import { useSessionStore, SessionData } from "../store/sessionStore";
import Button from "../components/ui/Button";

interface HygieneIssue {
    id: string;
    rule_id: string;
    category: string;
    severity: string;
    message: string;
    recommendation?: string;
    is_mastering_critical: boolean;
    affected_elements: any[];
}

const SessionHygienePage = () => {
    const [genre, setGenre] = useState("general");
    const [strictMode, setStrictMode] = useState(false);
    const [hygieneIssues, setHygieneIssues] = useState<HygieneIssue[]>([]);
    const { sessionData, setSessionData, setIsLoading, setError } =
        useSessionStore();

    const fetchHygieneAnalysis = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const response = await axios.get(
                `/api/v1/mastering-analysis?genre=${genre}&strict_mode=${strictMode}`
            );
            
            // Filter only hygiene issues (non-mastering-critical)
            const allIssues = response.data.issues || [];
            const hygieneOnly = allIssues.filter((issue: HygieneIssue) => 
                !issue.is_mastering_critical
            );
            
            setHygieneIssues(hygieneOnly);
            
            console.log("Session Hygiene Analysis:", {
                totalIssues: allIssues.length,
                hygieneIssues: hygieneOnly.length,
                issues: hygieneOnly
            });
            
            // Update session store with filtered hygiene data
            setSessionData((prevSessionData: SessionData | null) => {
                if (prevSessionData) {
                    return {
                        ...prevSessionData,
                        hygieneAnalysis: {
                            ...response.data,
                            issues: hygieneOnly
                        },
                    };
                } else {
                    return {
                        filename: "",
                        metadata: {
                            filename: "",
                            reaper_version: "",
                            save_timestamp: "",
                            has_master_track: false,
                            sample_rate: 0,
                            tempo: 0,
                            time_signature: "",
                        },
                        tracks: [],
                        routing: [],
                        hygieneAnalysis: {
                            ...response.data,
                            issues: hygieneOnly
                        },
                    };
                }
            });
        } catch (error) {
            console.error("Error fetching hygiene analysis:", error);
            setError("Failed to fetch session hygiene analysis");
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (sessionData?.filename) {
            fetchHygieneAnalysis();
        }
    }, [genre, strictMode, sessionData?.filename]);

    const hygieneAnalysisData = sessionData?.hygieneAnalysis;

    return (
        <div className="min-h-screen bg-slate-50">
            <div className="max-w-6xl mx-auto p-6">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-slate-900 mb-2">
                        Session Hygiene Analysis
                    </h1>
                    <p className="text-slate-600">
                        Review organizational and workflow improvements for your REAPER project
                    </p>
                    <Link 
                        to="/mastering"
                        className="inline-flex items-center mt-2 text-blue-600 hover:text-blue-500 text-sm"
                    >
                        → View Mastering Readiness Report
                    </Link>
                </div>

                {/* Genre Selection Panel */}
                <div className="mb-6">
                    <GenreSelectionPanel
                        genre={genre}
                        setGenre={setGenre}
                        strictMode={strictMode}
                        setStrictMode={setStrictMode}
                    />
                </div>

                {/* Refresh Button */}
                <div className="mb-6">
                    <Button
                        onClick={fetchHygieneAnalysis}
                        disabled={!sessionData?.filename}
                        className="bg-blue-600 hover:bg-blue-700"
                    >
                        Refresh Hygiene Analysis
                    </Button>
                </div>

                {/* Session File Info */}
                {sessionData?.filename && (
                    <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-4 mb-6">
                        <h2 className="text-lg font-semibold text-slate-900 mb-2">
                            Project Information
                        </h2>
                        <div className="text-sm text-slate-600 space-y-1">
                            <p><strong>File:</strong> {sessionData.filename}</p>
                            <p><strong>Sample Rate:</strong> {sessionData.metadata.sample_rate} Hz</p>
                            <p><strong>Tempo:</strong> {sessionData.metadata.tempo} BPM</p>
                            <p><strong>Hygiene Issues Found:</strong> {hygieneIssues.length}</p>
                        </div>
                    </div>
                )}

                {/* Hygiene Issues Summary */}
                {hygieneIssues.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <h3 className="text-lg font-semibold text-blue-900 mb-2">
                            Hygiene Issues Summary
                        </h3>
                        <p className="text-blue-800">
                            Found {hygieneIssues.length} organizational improvements. 
                            These issues don't affect mastering readiness but can improve workflow and project management.
                        </p>
                    </div>
                )}

                {/* Session Hygiene Analysis */}
                {hygieneAnalysisData && sessionData && (
                    <SessionHygieneSection
                        metadata={sessionData.metadata}
                        tracks={sessionData.tracks}
                    />
                )}

                {/* No Issues State */}
                {hygieneAnalysisData && hygieneIssues.length === 0 && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                        <div className="text-green-600 text-4xl mb-4">✓</div>
                        <h3 className="text-xl font-semibold text-green-900 mb-2">
                            Excellent Project Organization
                        </h3>
                        <p className="text-green-800">
                            No hygiene issues found. Your project is well-organized!
                        </p>
                    </div>
                )}

                {/* No Session Data State */}
                {!sessionData?.filename && (
                    <div className="bg-slate-100 border border-slate-300 rounded-lg p-6 text-center">
                        <h3 className="text-xl font-semibold text-slate-700 mb-2">
                            No Project Loaded
                        </h3>
                        <p className="text-slate-600">
                            Please upload a REAPER project file first to analyze session hygiene.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default SessionHygienePage;