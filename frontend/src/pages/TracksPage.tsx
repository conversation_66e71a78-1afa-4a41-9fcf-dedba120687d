import { useSessionStore } from "../store/sessionStore";
import TrackList from "../components/TrackList";
import { Navigate } from "react-router-dom";

const TracksPage = () => {
    const { sessionData, selectedTrackName, toggleTrackSelection } =
        useSessionStore();

    // If no session data is loaded, redirect to dashboard
    if (!sessionData) {
        return <Navigate to="/" />;
    }

    return (
        <div className="container mx-auto p-4">
            <h1 className="text-4xl font-bold mb-6 text-gray-800 dark:text-gray-100 border-b pb-3 border-blue-200 dark:border-blue-800">
                <span className="inline-block">
                    <svg
                        className="inline-block w-8 h-8 mr-3 mb-1 text-blue-600 dark:text-blue-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                        />
                    </svg>
                    Tracks
                </span>
            </h1>
            <TrackList
                tracks={sessionData.tracks}
                routing={sessionData.routing}
                selectedTrackName={selectedTrackName}
                onTrackSelect={(trackName) =>
                    toggleTrackSelection(trackName as string)
                }
                metadata={sessionData.metadata}
            />
        </div>
    );
};

export default TracksPage;
