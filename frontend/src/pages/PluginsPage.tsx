import { useSessionStore } from "../store/sessionStore";
import PluginPanel from "../components/PluginPanel";
import { Navigate } from "react-router-dom";
import { Package } from "lucide-react";

const PluginsPage = () => {
    const { sessionData } = useSessionStore();

    // If no session data is loaded, redirect to dashboard
    if (!sessionData) {
        return <Navigate to="/" />;
    }

    return (
        <div className="container mx-auto p-4">
            <h1 className="text-4xl font-bold mb-6 text-gray-800 dark:text-gray-100 border-b pb-3 border-blue-200 dark:border-blue-800">
                <span className="inline-block">
                    <Package className="inline-block w-8 h-8 mr-3 mb-1 text-blue-600 dark:text-blue-400" />
                    Plugins
                </span>
            </h1>
            <PluginPanel
                tracks={sessionData.tracks}
                metadata={sessionData.metadata}
            />
        </div>
    );
};

export default PluginsPage;
