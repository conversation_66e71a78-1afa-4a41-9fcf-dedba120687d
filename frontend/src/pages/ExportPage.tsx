import { useSessionStore } from "../store/sessionStore";
import ExportOptions from "../components/ExportOptions";
import { Navigate } from "react-router-dom";
import { useState } from "react";

const ExportPage = () => {
    const { sessionData } = useSessionStore();

    // If no session data is loaded, redirect to dashboard
    if (!sessionData) {
        return <Navigate to="/" />;
    }

    // State to track loading status for exports
    const [isExporting, setIsExporting] = useState(false);

    // Handle export actions
    const handleExport = async (type: string, selectedTracks?: number[]) => {
        console.log(`Exporting ${type}...`);
        setIsExporting(true);

        try {
            // Handle different export types
            switch (type) {
                case "markdown":
                    await handleMarkdownExport();
                    break;

                case "fxchain":
                    if (selectedTracks && selectedTracks.length > 0) {
                        await handleFxChainExport(selectedTracks);
                    } else {
                        alert("Please select at least one track.");
                    }
                    break;

                case "tracktemplate":
                    if (selectedTracks && selectedTracks.length > 0) {
                        await handleTrackTemplateExport(selectedTracks);
                    } else {
                        alert("Please select at least one track.");
                    }
                    break;

                default:
                    alert(
                        `Export ${type} requested. This would download a file in a real implementation.`
                    );
            }
        } catch (error) {
            console.error("Export failed:", error);
            alert(
                `Export failed: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        } finally {
            setIsExporting(false);
        }
    };

    // Handle FX Chain export
    const handleFxChainExport = async (trackIndices: number[]) => {
        try {
            // Call the backend API
            const response = await fetch("/api/v1/export/fxchain", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(trackIndices),
            });

            if (!response.ok) {
                throw new Error(
                    `Server returned ${response.status}: ${response.statusText}`
                );
            }

            // Get the file content
            const blob = await response.blob();

            // Create a download link and trigger it
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "exported.RfxChain";
            document.body.appendChild(a);
            a.click();

            // Clean up
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            throw error;
        }
    };

    // Handle Track Template export
    const handleTrackTemplateExport = async (trackIndices: number[]) => {
        try {
            // Call the backend API
            const response = await fetch("/api/v1/export/tracktemplate", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(trackIndices),
            });

            if (!response.ok) {
                throw new Error(
                    `Server returned ${response.status}: ${response.statusText}`
                );
            }

            // Get the file content
            const blob = await response.blob();

            // Create a download link and trigger it
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "exported.TrackTemplate";
            document.body.appendChild(a);
            a.click();

            // Clean up
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            throw error;
        }
    };

    // Handle Markdown export
    const handleMarkdownExport = async () => {
        try {
            // Call the backend API
            const response = await fetch("/api/v1/export/markdown", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            if (!response.ok) {
                throw new Error(
                    `Server returned ${response.status}: ${response.statusText}`
                );
            }

            // Get the file content
            const blob = await response.blob();

            // Create a download link and trigger it
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = "session_summary.md";
            document.body.appendChild(a);
            a.click();

            // Clean up
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            throw error;
        }
    };

    return (
        <div className="container mx-auto p-4">
            <h1 className="text-2xl font-bold mb-4">Export Options</h1>
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <p className="mb-4 text-gray-700 dark:text-gray-300">
                    Select an export format below to generate files based on
                    your session data.
                </p>
                {isExporting ? (
                    <div className="text-center py-4">
                        <p className="text-gray-700 dark:text-gray-300 mb-2">
                            Generating export...
                        </p>
                        <div className="inline-block w-8 h-8 border-4 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
                    </div>
                ) : (
                    <ExportOptions
                        trackCount={sessionData.tracks.length}
                        onExport={handleExport}
                    />
                )}
            </div>
        </div>
    );
};

export default ExportPage;
