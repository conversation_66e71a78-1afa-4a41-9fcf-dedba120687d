import { useSessionStore, ensureDefaults } from "../store/sessionStore";
import SessionSummary from "../components/SessionSummary";
import FileUpload from "../components/FileUpload";
import axios from "axios";
import { HardDrive, Music, FileJson, Activity } from "lucide-react";

const DashboardPage = () => {
    // Get state and actions from the session store
    const {
        sessionData,
        isLoading,
        error,
        setSessionData,
        setIsLoading,
        setError,
    } = useSessionStore();

    // Handle file upload
    const handleFileUpload = async (file: File) => {
        console.log("File uploaded:", file.name);
        setIsLoading(true);
        setError(null);

        try {
            // Create form data for the file upload
            const formData = new FormData();
            formData.append("file", file);

            // Make API call to the backend (using Vite proxy)
            const response = await axios.post("/api/v1/upload", formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            });

            // Log the API response for debugging
            console.log("API Response:", response.data);

            // Update state with the parsed data from the backend
            const processedData = ensureDefaults(response.data);
            // console.log("Processed data being set to store:", processedData); // DEBUG REMOVED
            setSessionData(processedData);

            // Log state immediately after setting (Zustand updates might be async)
            // setTimeout(() => { // DEBUG REMOVED
            //     console.log( // DEBUG REMOVED
            //         "Session data in store after set:", // DEBUG REMOVED
            //         useSessionStore.getState().sessionData // DEBUG REMOVED
            //     ); // DEBUG REMOVED
            // }, 0); // DEBUG REMOVED
        } catch (error) {
            console.error("Error uploading file:", error);

            // Set error message for user display
            if (axios.isAxiosError(error)) {
                if (error.response) {
                    // Server responded with an error status
                    setError(
                        `Upload failed: ${
                            error.response.data.detail || "Server error"
                        }`
                    );
                } else if (error.request) {
                    // Request was made but no response received
                    setError(
                        "Upload failed: No response from server. Is the backend running?"
                    );
                } else {
                    // Error in setting up the request
                    setError(`Upload failed: ${error.message}`);
                }
            } else {
                setError(`Upload failed: ${String(error)}`);
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen">
            <main className="container mx-auto p-4">
                {/* Error display */}
                {error && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {error}
                    </div>
                )}

                {/* Loading indicator removed - now handled inside FileUpload component */}

                {/* Hero Section */}
                <section
                    className="relative overflow-hidden text-white rounded-lg mt-4 mb-10 shadow-lg"
                    style={{
                        backgroundImage: "url('/hero-banner.png')",
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        height: "400px",
                    }}
                >
                    {/* Dark overlay to improve text readability */}
                    <div className="absolute inset-0 bg-black bg-opacity-60"></div>

                    <div className="relative z-10 h-full flex flex-col justify-center">
                        {/* Offset container that pushes content to the left */}
                        <div className="ml-12 md:ml-24 lg:ml-36 max-w-3xl">
                            {/* Title with left alignment */}
                            <h1 className="text-5xl md:text-7xl font-black mb-6 tracking-tight leading-tight text-left">
                                <span className="relative inline-block">
                                    Session
                                    <span className="text-blue-300">View</span>
                                    {/* Decorative underline element */}
                                    <span className="absolute -bottom-2 left-0 w-1/2 h-1 bg-blue-400 rounded-full"></span>
                                </span>
                            </h1>

                            {/* Subtitle with slight offset */}
                            <p className="text-2xl md:text-3xl mb-8 text-blue-100 font-semibold text-left ml-4 max-w-2xl">
                                Visualize, analyze and export your Reaper DAW
                                sessions with ease.
                            </p>

                            {/* Description text slightly narrower */}
                            <p className="text-lg md:text-xl mb-8 max-w-lg font-normal text-gray-100 text-left ml-8">
                                Upload your .rpp files to get detailed insights,
                                extract FX chains, create track templates, and
                                better understand your project structure.
                            </p>
                        </div>
                    </div>
                </section>

                {/* About Section */}
                <section className="mb-12">
                    <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
                        What You Can Do
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                            <div className="text-blue-500 mb-4">
                                <HardDrive size={32} />
                            </div>
                            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
                                Analyze Sessions
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                Get a complete overview of your Reaper project:
                                tracks, plugins, routing, and more.
                            </p>
                        </div>

                        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                            <div className="text-green-500 mb-4">
                                <Activity size={32} />
                            </div>
                            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
                                Track Analysis
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                See detailed information about each track,
                                including FX chains, routing, and automation.
                            </p>
                        </div>

                        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                            <div className="text-purple-500 mb-4">
                                <FileJson size={32} />
                            </div>
                            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
                                Plugin Insights
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                Track plugin usage across your session and
                                identify opportunities for optimization.
                            </p>
                        </div>

                        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                            <div className="text-orange-500 mb-4">
                                <Music size={32} />
                            </div>
                            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
                                Export Tools
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300">
                                Export FX chains, track templates, and detailed
                                session summaries for documentation.
                            </p>
                        </div>
                    </div>
                </section>

                {/* File Upload Section - only shown when no session is loaded */}
                {!sessionData && (
                    <section className="mb-10">
                        <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
                            Analyze Your Session
                        </h2>

                        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md">
                            <div className="max-w-xl mx-auto">
                                <FileUpload onFileUpload={handleFileUpload} />
                            </div>
                        </div>
                    </section>
                )}

                {/* Session Summary (shown only when data is loaded) */}
                {sessionData && !isLoading && <SessionSummary />}
            </main>
        </div>
    );
};

export default DashboardPage;
