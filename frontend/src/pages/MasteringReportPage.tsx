import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import GenreSelectionPanel from "../components/GenreSelectionPanel";
import PluginAnalysisSection from "../components/PluginAnalysisSection";
import FileReferenceSection from "../components/FileReferenceSection";
import ItemPropertyAnalysisSection from "../components/ItemPropertyAnalysisSection";
import ProjectSettingsAnalysisSection from "../components/ProjectSettingsAnalysisSection";
import TrackSpecificAnalysisSection from "../components/TrackSpecificAnalysisSection";
import MasteringDashboard from "../components/MasteringDashboard"; // Import the new dashboard
import GroupedMasteringDashboard from "../components/GroupedMasteringDashboard";
import axios from "axios";
import { useSessionStore, SessionData, GroupedMasteringAnalysis } from "../store/sessionStore";
import Button from "../components/ui/Button";

const MasteringReportPage = () => {
    const [genre, setGenre] = useState("general");
    const [strictMode, setStrictMode] = useState(false);
    const [useGroupedView, setUseGroupedView] = useState(true);
    const [groupedAnalysis, setGroupedAnalysis] = useState<GroupedMasteringAnalysis | null>(null);
    const { sessionData, setSessionData, setIsLoading, setError } =
        useSessionStore();

    const fetchMasteringAnalysis = async () => {
        setIsLoading(true);
        setError(null);
        try {
            if (useGroupedView) {
                // Fetch grouped analysis
                const response = await axios.get(
                    `/api/v1/mastering-analysis-grouped?genre=${genre}&strict_mode=${strictMode}`
                );
                console.log("Grouped Mastering Analysis Response:", response.data);
                
                // Filter only mastering-critical issues from both groups and ungrouped
                const filteredGroups = response.data.issue_groups.filter((group: any) => 
                    group.is_mastering_critical !== false
                );
                const filteredUngrouped = response.data.ungrouped_issues.filter((issue: any) => 
                    issue.is_mastering_critical !== false
                );
                
                const filteredGroupedData = {
                    ...response.data,
                    issue_groups: filteredGroups,
                    ungrouped_issues: filteredUngrouped
                };
                
                setGroupedAnalysis(filteredGroupedData);
                
                // Also update session store for compatibility
                setSessionData((prevSessionData: SessionData | null) => {
                    const allFilteredIssues = [
                        ...filteredGroups.flatMap((group: any) => 
                            Array(group.count).fill(group.sample_issue || {})
                        ),
                        ...filteredUngrouped
                    ];
                    
                    const compatData = {
                        ...response.data,
                        issues: allFilteredIssues
                    };
                    
                    if (prevSessionData) {
                        return {
                            ...prevSessionData,
                            masteringAnalysis: compatData,
                        };
                    } else {
                        return {
                            filename: "",
                            metadata: {
                                filename: "",
                                reaper_version: "",
                                save_timestamp: "",
                                has_master_track: false,
                                sample_rate: 0,
                                tempo: 0,
                                time_signature: "",
                            },
                            tracks: [],
                            routing: [],
                            masteringAnalysis: compatData,
                        };
                    }
                });
            } else {
                // Fetch regular analysis
                const response = await axios.get(
                    `/api/v1/mastering-analysis?genre=${genre}&strict_mode=${strictMode}`
                );
                console.log("Regular Mastering Analysis Response:", response.data);
                
                // Filter only mastering-critical issues for this page
                const allIssues = response.data.issues || [];
                const masteringCriticalIssues = allIssues.filter((issue: any) => 
                    issue.is_mastering_critical !== false
                );
                
                const filteredMasteringData = {
                    ...response.data,
                    issues: masteringCriticalIssues
                };
                
                setGroupedAnalysis(null);
                
                // Update session store with filtered mastering analysis data
                setSessionData((prevSessionData: SessionData | null) => {
                    if (prevSessionData) {
                        return {
                            ...prevSessionData,
                            masteringAnalysis: filteredMasteringData,
                        };
                    } else {
                        return {
                            filename: "",
                            metadata: {
                                filename: "",
                                reaper_version: "",
                                save_timestamp: "",
                                has_master_track: false,
                                sample_rate: 0,
                                tempo: 0,
                                time_signature: "",
                            },
                            tracks: [],
                            routing: [],
                            masteringAnalysis: filteredMasteringData,
                        };
                    }
                });
            }
        } catch (error) {
            console.error("Error fetching mastering analysis:", error);
            setError("Failed to fetch mastering analysis.");
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchMasteringAnalysis();
    }, [genre, strictMode, useGroupedView]);

    const { isLoading, error } = useSessionStore();

    const handleExportJson = () => {
        if (sessionData && sessionData.masteringAnalysis) {
            const jsonString = `data:text/json;charset=utf-8,${encodeURIComponent(
                JSON.stringify(sessionData.masteringAnalysis, null, 2)
            )}`;
            const link = document.createElement("a");
            link.href = jsonString;
            link.download = "mastering-report.json";
            link.click();
        }
    };

    // The fetchMasteringAnalysis useEffect will run on mount and when genre/strictMode change.
    // The sessionData.masteringAnalysis will be populated by this.

    if (!sessionData) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-800 mb-4">
                        Mastering Report
                    </h1>
                    <p className="text-gray-600">
                        Please upload an RPP file first to generate a mastering
                        report.
                    </p>
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-800 mb-4">
                        Mastering Report
                    </h1>
                    <p className="text-gray-600">Analyzing session...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-800 mb-4">
                        Mastering Report
                    </h1>
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        Error: {error}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
                <div className="flex justify-between items-center mb-4">
                    <div>
                        <h1 className="text-3xl font-bold text-white mb-2">
                            Mastering Report
                        </h1>
                        <p className="text-gray-400">
                            Analysis for{" "}
                            <strong>{sessionData.metadata.filename}</strong>
                        </p>
                        <Link 
                            to="/hygiene"
                            className="inline-flex items-center mt-2 text-blue-400 hover:text-blue-300 text-sm"
                        >
                            → View Session Hygiene Analysis
                        </Link>
                    </div>
                    <Button
                        onClick={handleExportJson}
                        className="bg-green-600 hover:bg-green-700"
                    >
                        Export JSON
                    </Button>
                </div>

                <GenreSelectionPanel
                    genre={genre}
                    setGenre={setGenre}
                    strictMode={strictMode}
                    setStrictMode={setStrictMode}
                />

                {/* View Toggle */}
                <div className="mt-4 flex items-center space-x-4">
                    <span className="text-gray-400 text-sm">View Mode:</span>
                    <button
                        onClick={() => setUseGroupedView(true)}
                        className={`px-3 py-1 text-sm rounded ${
                            useGroupedView
                                ? "bg-blue-600 text-white"
                                : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                        }`}
                    >
                        Grouped (Clean)
                    </button>
                    <button
                        onClick={() => setUseGroupedView(false)}
                        className={`px-3 py-1 text-sm rounded ${
                            !useGroupedView
                                ? "bg-blue-600 text-white"
                                : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                        }`}
                    >
                        Detailed (All Issues)
                    </button>
                </div>
            </div>

            {/* Render the appropriate dashboard based on view mode */}
            {useGroupedView && groupedAnalysis && (
                <GroupedMasteringDashboard groupedAnalysis={groupedAnalysis} />
            )}
            {!useGroupedView && sessionData.masteringAnalysis && <MasteringDashboard />}

            {/* The individual sections will provide more detailed breakdowns */}
            <div className="space-y-6 mt-8">
                {" "}
                {/* Added mt-8 for spacing */}
                <PluginAnalysisSection genre={genre} strictMode={strictMode} />
                <FileReferenceSection
                    metadata={sessionData.metadata}
                    tracks={sessionData.tracks}
                />
                <ItemPropertyAnalysisSection />
                <ProjectSettingsAnalysisSection />
                <TrackSpecificAnalysisSection />
            </div>
        </div>
    );
};

export default MasteringReportPage;
