import json
import sys
import os

# Add project root to sys.path to allow imports from backend
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "."))
sys.path.insert(0, project_root)
# Add backend directory to sys.path as well
backend_dir = os.path.join(project_root, "backend")
sys.path.insert(0, backend_dir)

from backend.parser_wrapper import parse_rpp_content
from typing import Dict, Any  # Import Dict and Any for type hinting

# Add more RPP file paths here as needed
RPP_FILES_TO_TEST = [
    "tests/fixtures/Mid24 No1 Mix.RPP",
    "tests/fixtures/Qotsa2 - Mix.RPP",
    "tests/fixtures/parameter_test/parameter_test.RPP",
]


def print_phase1_analysis(rpp_file_path: str):
    """
    Parses an RPP file and prints the Phase 1 mastering enhancement data.
    """
    print(f"--- Analyzing RPP file: {rpp_file_path} ---")
    try:
        # The parse_rpp_file function in main.py expects a file-like object.
        # For simplicity here, we'll assume it can also take a path or adapt it.
        # For now, let's assume it's adapted to take a path or we use a helper.
        # Directly using parse_rpp_content which returns SessionAnalysis
        with open(rpp_file_path, "rb") as f:
            contents = f.read()
            # Pass the filename for context within the parser if needed
            analysis_result: Dict[str, Any] = parse_rpp_content(  # Return type is Dict
                contents, filename=rpp_file_path
            )

        if not analysis_result:
            print("Failed to parse RPP file.")
            return

        print("\n=== Metadata Analysis ===")
        metadata = analysis_result.get("metadata")
        if metadata:
            print(
                f"  Project Name: {metadata.get('project_name')}"
            )  # Changed from filename to project_name
            print(
                f"  Is Suboptimal Bit Depth: {metadata.get('is_suboptimal_bit_depth')}"
            )
            print(
                f"  Is Non-standard Sample Rate: {metadata.get('is_nonstandard_sample_rate')}"
            )
            print(
                f"  First Item Has Abrupt Start at Zero: {metadata.get('first_item_has_abrupt_start_at_zero')}"
            )
            print(
                f"  Stereo Balance Issue Description: {metadata.get('stereo_balance_issue_description')}"
            )
            print(
                f"  Has Incomplete Project Info: {metadata.get('has_incomplete_project_info')}"
            )
        else:
            print("  No metadata found.")

        print("\n=== Master Track Analysis ===")
        # The master track is the first track in the 'tracks' list if it exists
        tracks_list = analysis_result.get("tracks", [])
        master_track_dict = None
        if tracks_list and tracks_list[0].get("type") == "MASTER":
            master_track_dict = tracks_list[0]

        if master_track_dict:
            print(
                f"  Has Limiter on Master: {master_track_dict.get('has_limiter_on_master')}"
            )
            print(
                f"  Has Master Bus Automation: {master_track_dict.get('has_master_bus_automation')}"
            )
        else:
            # It's possible the 'master_track' key might exist separately in some older versions
            # For now, we assume it's part of the tracks list.
            # If not found as the first track, we can check a dedicated key if that was ever the case.
            # master_track_alt = analysis_result.get('master_track')
            # if master_track_alt:
            #     print(f"  (Alt) Has Limiter on Master: {master_track_alt.get('has_limiter_on_master')}")
            #     print(f"  (Alt) Has Master Bus Automation: {master_track_alt.get('has_master_bus_automation')}")
            # else:
            print("  No master track data found or not identified as first track.")

        print("\n=== Track Analysis ===")
        # Iterate through all tracks, skipping the master if it was handled above
        non_master_tracks_list = (
            [t for t in tracks_list if t.get("type") != "MASTER"] if tracks_list else []
        )

        if non_master_tracks_list:
            for track_dict in non_master_tracks_list:
                print(
                    f"\n  --- Track: {track_dict.get('name')} (ID: {track_dict.get('guid')}) ---"
                )  # Changed track_id to guid
                print(f"    Is Record Armed: {track_dict.get('is_record_armed')}")
                print(
                    f"    Has Bypassed FX in Chain: {track_dict.get('has_bypassed_fx_in_chain')}"
                )

                items_list = track_dict.get("items", [])
                if items_list:
                    print("    --- Items ---")
                    for i, item_dict in enumerate(items_list):
                        # In models.py, Item.to_dict() does not have 'source_filename' directly.
                        # 'takes' is a list, and each take has a 'source' which is a dict with 'file_path'.
                        # For simplicity, we'll just use item name or a generic placeholder.
                        item_name_display = item_dict.get("name", f"Item {i+1}")
                        active_take = (
                            item_dict.get("takes", [{}])[0]
                            if item_dict.get("takes")
                            else {}
                        )
                        source_info = active_take.get("source")
                        if source_info and source_info.get("file_path"):
                            item_name_display = f"{item_dict.get('name', f'Item {i+1}')} ({source_info.get('file_path')})"

                        print(f"      --- Item: {item_name_display} ---")
                        print(
                            f"        Fade In Length: {item_dict.get('fade_in_length')}, Shape: {item_dict.get('fade_in_shape')}"
                        )
                        print(
                            f"        Fade Out Length: {item_dict.get('fade_out_length')}, Shape: {item_dict.get('fade_out_shape')}"
                        )
                        print(f"        Is Reversed: {item_dict.get('is_reversed')}")
                        print(f"        Playrate: {item_dict.get('playrate')}")
                        print(
                            f"        Has Abrupt Start: {item_dict.get('has_abrupt_start')}"
                        )
                        print(
                            f"        Has Abrupt End: {item_dict.get('has_abrupt_end')}"
                        )
                        print(
                            f"        Has Unusual Properties Warning: {item_dict.get('has_unusual_properties_warning')}"
                        )
                else:
                    print("    No items on this track.")
        elif not master_track_dict and not tracks_list:  # If no tracks at all
            print("  No tracks found in the project.")
        elif not non_master_tracks_list and master_track_dict:  # Only master track
            print("  Only master track found in the project.")

        print(f"\n--- Finished analyzing: {rpp_file_path} ---\n")

    except FileNotFoundError:
        print(f"Error: RPP file not found at {rpp_file_path}")
    except Exception as e:
        print(f"An error occurred while processing {rpp_file_path}: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    for rpp_file in RPP_FILES_TO_TEST:
        print_phase1_analysis(rpp_file)
