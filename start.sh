#!/bin/bash

# SessionView Development Startup Script
# Starts both frontend and backend services

echo "🚀 Starting SessionView Development Environment..."

# Function to cleanup background processes on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    jobs -p | xargs -r kill
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Start backend in background
echo "📡 Starting backend server..."
cd backend

echo "🔧 Activating virtual environment..."
source venv/bin/activate

echo "🐍 Starting FastAPI backend on http://localhost:8000"
uvicorn main:app --reload &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start frontend
echo "⚛️  Starting frontend server..."
cd ../frontend

echo "🌐 Starting React frontend on http://localhost:5173"
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ Both services are starting..."
echo "📡 Backend: http://localhost:8000"
echo "🌐 Frontend: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop both services"

# Wait for both processes
wait
