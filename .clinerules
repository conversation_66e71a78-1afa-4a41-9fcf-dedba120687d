# .clinerules - Project Intelligence for SessionView

## Cline's Learning Journal for the SessionView Project

This file captures important patterns, preferences, and project-specific
intelligence to help <PERSON><PERSON> work more effectively on the SessionView project.

---

## "Ready for Mastering" Feature Rules & Heuristics

**Note:** The following rules and heuristics serve as a baseline. The
application will allow users to select a genre, which will then apply specific
adjustments and tolerances to these general guidelines. The `genre_rules.py`
module will contain the detailed logic for these genre-specific modifications.

### 1. Plugin State Intelligence

-   **Blacklisted Plugin Names (General - suggest bypass/removal before
    mastering):**
    -   Demo
    -   Trial
    -   Limiter (if on individual tracks and not for creative effect)
    -   Finalizer (if on individual tracks)
-   **Blacklisted Plugin Names (Master Bus - strong warning, subject to genre
    adjustment):**
    -   Ozone (especially Maximizer; other modules depend on genre and settings)
    -   Pro-L (or similar mastering limiters)
    -   Elevate (or similar mastering limiters)
    -   Gullfoss (if used aggressively; depends on genre)
    -   Soothe (if used aggressively; depends on genre)
-   **Suggest Plugin Substitution:**
    -   (Example) If "Ozone Elements" found, suggest "Consider bypassing Ozone
        Elements on the master bus before sending to mastering."
-   **Missing Plugin Detection:**
    -   Logic to be based on VST lines with no matching plugin data.

### 2. Gain Staging Sanity Check

-   **Track Level Warnings (Item/Take Gain - default thresholds, adjusted by
    genre):**
    -   Default Max Gain Threshold: +6dB (e.g., Rock/Pop)
    -   Default Min Gain Threshold: -20dB (flag for review, context-dependent)
    -   _Genre examples: Electronic might tolerate +8dB, Acoustic might prefer
        +4dB max._
-   **Internal Clipping:**
    -   Heuristic: If a plugin's output parameter is at max or input is very
        high, flag potential. (Requires more detailed RPP parsing of plugin
        states).

### 3. Session Hygiene Scanner

-   **Default/Generic Track Names to Flag:**
    -   Track
    -   Audio
    -   Untitled
    -   MIDI
    -   Inst
    -   Aux
-   **Folder/Group Checks:**
    -   Flag tracks not part of any folder structure if project has folders.
-   **Empty Items/Takes:**
    -   Flag items with `LENGTH 0.0` or empty MIDI items.
-   **Missing Session Elements (Reminders):**
    -   No markers
    -   No regions
    -   No tempo map changes (if project is not a single tempo)
    -   No notes in RPP `NOTES` chunk.

### 4. File Reference Consistency Analysis

-   **Problematic File References:**
    -   Detects absolute paths (may break portability when project moves).
    -   Identifies references to common system/temp directories (non-portable).
    -   Flags relative paths outside common media folders (e.g., not "Media/", "Audio/").
    -   **Note:** This analysis is based on file paths _referenced within the RPP_ and _does not check for actual file existence_ on the user's local system, as SessionView is an online tool.
-   **Sample Rate/Bit Depth Mismatches (Metadata):**
    -   Compares `SAMPLERATE` and `BITDEPTH` metadata extracted from `<SOURCE>` tags (if available) against the project's overall sample rate and bit depth settings.
    -   **Note:** This checks for metadata consistency within the RPP, not the actual audio file properties.

### 5. Master Bus Chain Checker

-   **Plugins to Flag on Master (suggest removal/bypass):**
    -   Limiters (unless for specific pre-mastering goal and user is aware)
    -   Clippers
    -   Aggressive EQs or compressors not intended for pre-master.
-   **Metering-Only Plugins (Good to see on Master):**
    -   Insight
    -   SPAN
    -   Youlean Loudness Meter
    -   (Define a list of common metering plugins)
-   **Peak Level Automation on Master:**
    -   Flag if master fader has volume automation (mastering usually prefers
        this to be static or handled by them).

### 6. Pre-Export Readiness Assessment (Stem Export Profile)

-   **Bus Routing:**
    -   Ensure buses are routed only to master (unless intentionally parallel
        processing).
-   **Unique Track Names:**
    -   Check for duplicate track names.
-   **Master Bus Automation:**
    -   As above, flag automation on master fader.

---

## General Project Patterns & Preferences

### Plugin Detection & Analysis

-   **RPP Library Compatibility**: RPP library Elements use `.attrib` and `.children`, NOT `.text` attribute
-   **Plugin Name Extraction**: Use `element.attrib[0]` for plugin names, not text parsing
-   **Plugin Type Detection**: Use `element.tag` directly (VST, AU, JS, etc.), not text parsing
-   **Pattern Matching**: Implement fallback pattern matching for plugin name variations (e.g., "pro-l" matches FF Pro-L2, Pro-L2, etc.)
-   **Category Mapping**: Map plugin categories to blacklist rule keys (limiter → limiters, clipper → clippers)

### Backend Architecture Patterns

-   **Proven Code Preservation**: When refactoring, preserve working patterns from old code rather than reimplementing
-   **Systematic Error Resolution**: Find and fix all instances of the same pattern (e.g., all `.text` access issues)
-   **Library-Specific Adaptation**: Understand and adapt to library-specific data structures (RPP vs XML ElementTree)

## Tool Usage Patterns

-   **Memory Bank Updates**: Always update memory bank after major fixes or achievements
-   **Incremental Fixes**: Apply fixes systematically, one issue at a time, with verification
-   **Pattern Analysis**: Compare working vs broken code to identify exact differences
-   **Data Management**: Use standalone scripts for data gathering and validation (e.g., `scrape_plugin_boutique.py`, `check_duplicates.py`). These scripts should be isolated from the main application's environment to avoid import conflicts.
-   **Duplicate Detection**: When checking for duplicate plugins, consider that names can have variations (e.g., "Class A", "Mk II"). Use normalization techniques to identify these as duplicates.
