# SessionView - Reaper Project Analyzer

SessionView is a web-based application for analyzing, documenting, and exporting
configurations from Reaper DAW (`.rpp`) session files. It enables music
producers, mixers, and engineers to gain insight into their projects, streamline
hand-offs, and reuse configuration elements like FX chains, track templates, and
color settings.

## Features

-   Upload and parse `.rpp` files
-   Visual track overview (type, FX, automation, color)
-   Plugin/FX summary and usage per track
-   Track color mapping
-   Routing matrix viewer
-   Export options:
    -   FX chains (`.RfxChain` files)
    -   Track templates (`.TrackTemplate` files)
    -   Markdown session summary

## Tech Stack

### Frontend

-   React with TypeScript
-   Tailwind CSS for styling
-   Framer Motion for animations

### Backend

-   FastAPI (Python)
-   Python `rpp` library (with potential fork/extensions)

## Project Structure

```
rpp-analyser/
├── backend/            # FastAPI application
│   ├── main.py         # Main API routes and application entry point
│   └── parser_wrapper.py  # RPP file parser logic
├── frontend/           # React application
│   ├── public/         # Static assets
│   └── src/
│       ├── components/ # React components
│       │   ├── FileUpload.tsx      # File upload component
│       │   ├── TrackList.tsx       # Track list component
│       │   ├── PluginPanel.tsx     # Plugin analysis component
│       │   ├── RoutingPanel.tsx    # Routing matrix component
│       │   └── ExportOptions.tsx   # Export options component
│       ├── App.tsx     # Main application component
│       └── main.tsx    # Application entry point
└── README.md           # Project documentation
```

## Getting Started

### Prerequisites

-   Node.js (v18+)
-   Python 3.11+
-   npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd rpp-analyser
```

2. Set up the backend:

```bash
cd backend
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install fastapi "uvicorn[standard]" python-dotenv aiofiles rpp
```

3. Set up the frontend:

```bash
cd ../frontend
npm install
```

### Running the Application

1. Start the backend:

```bash
cd backend
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
uvicorn main:app --reload
```

2. In a new terminal, start the frontend:

```bash
cd frontend
npm run dev
```

3. Open your browser and navigate to
   [http://localhost:5173](http://localhost:5173)

## Development

### Backend API Routes

-   `POST /api/v1/upload` - Upload and parse RPP file
-   `GET /api/v1/summary` - Get summary of last uploaded session
-   `POST /api/v1/export/fxchain` - Export FX chain for selected track(s)
-   `POST /api/v1/export/tracktemplate` - Export selected tracks as track
    template
-   `POST /api/v1/export/markdown` - Generate Markdown session report

### Frontend Components

-   `FileUpload.tsx` - Handles file uploads with drag and drop support
-   `TrackList.tsx` - Displays track information in a table
-   `PluginPanel.tsx` - Shows plugin usage analysis
-   `RoutingPanel.tsx` - Displays routing connections between tracks
-   `ExportOptions.tsx` - Provides export functionality with track selection

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for
details.
