import requests
from bs4 import BeautifulSoup
import csv
import time
import argparse
import json


def scrape_pluginboutique_category(
    category_slug, pages=None, delay=1.0, output_file="plugins.csv"
):
    """
    Scrapes plugin listings from Plugin Boutique for a given category.

    Args:
        category_slug (str): Category path, e.g. '8-Compressor'
        pages (int, optional): Max pages to scrape. None = scrape all pages.
        delay (float): Delay (in seconds) between page requests.
        output_file (str): CSV filename to save the results.
    """
    base_url = "https://www.pluginboutique.com/categories"
    results = []
    seen_plugins = set()
    duplicates_found = 0
    page = 1
    total_pages = 1

    while page <= total_pages:
        url = f"{base_url}/{category_slug}?page={page}"
        print(f"Fetching: {url}")
        resp = requests.get(url)
        if resp.status_code != 200:
            print(f"Stopped: HTTP {resp.status_code}")
            break

        soup = BeautifulSoup(resp.text, "html.parser")
        items = soup.select("div.producttile")  # container for each plugin entry

        if page == 1:
            pager_element = soup.select_one('div[data-react-class="Pager"]')
            if pager_element:
                try:
                    pager_props = json.loads(pager_element["data-react-props"])
                    total_pages = pager_props.get("pages", {}).get("pages_count", 1)
                    print(f"Total pages found: {total_pages}")
                except (json.JSONDecodeError, KeyError) as e:
                    print(f"Could not parse pager data: {e}")

        if not items and page == 1:
            print(
                "No products found on page. This might indicate a change in website structure or end of pages."
            )
            break

        for item in items:
            name_el = item.select_one("h3.producttile-title a")
            vendor_el = item.select_one(
                "h4.producttile-meta a[href*='/manufacturers/']"
            )

            name = name_el.get_text(strip=True) if name_el else "N/A"
            vendor = vendor_el.get_text(strip=True) if vendor_el else "N/A"

            # Normalize for duplicate checking
            plugin_id = (name.lower(), vendor.lower())

            if plugin_id not in seen_plugins:
                seen_plugins.add(plugin_id)
                results.append({"Plugin Name": name, "Manufacturer": vendor})
            else:
                duplicates_found += 1

        page += 1
        if pages and page > pages:
            break
        time.sleep(delay)

    # Write results to CSV
    with open(output_file, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["Plugin Name", "Manufacturer"])
        writer.writeheader()
        writer.writerows(results)

    print(
        f"Scraped {len(results)} unique plugins. "
        f"Found and skipped {duplicates_found} duplicates. "
        f"Saved to '{output_file}'"
    )


def debug_save_raw_html(category_slug, output_file="debug.html"):
    """Fetches a category page and saves its raw HTML for debugging."""
    base_url = "https://www.pluginboutique.com/categories"
    url = f"{base_url}/{category_slug}"
    print(f"Fetching for debug: {url}")
    resp = requests.get(url)
    if resp.status_code == 200:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(resp.text)
        print(f"Successfully saved raw HTML to '{output_file}'")
    else:
        print(f"Failed to fetch page. Status code: {resp.status_code}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Scrape Plugin Boutique categories for plugins"
    )
    parser.add_argument("category_slug", help="Category slug, e.g. '8-Compressor'")
    parser.add_argument(
        "--pages", type=int, default=None, help="Max number of pages to scrape"
    )
    parser.add_argument(
        "--delay", type=float, default=1.0, help="Delay between requests (seconds)"
    )
    parser.add_argument("--output", default="plugins.csv", help="Output CSV filename")
    parser.add_argument(
        "--debug_html",
        action="store_true",
        help="Save the raw HTML of the first page for debugging.",
    )
    args = parser.parse_args()

    if args.debug_html:
        debug_save_raw_html(
            args.category_slug,
            output_file=f"{args.category_slug.replace('/', '_')}_debug.html",
        )
    else:
        scrape_pluginboutique_category(
            args.category_slug,
            pages=args.pages,
            delay=args.delay,
            output_file=args.output,
        )

# Example usage:
#   python scrape_plugins.py "8-Compressor" --pages 5 --delay 1.5 --output compressors.csv
#   python scrape_plugins.py "52-Mastering-Suite" --debug_html
