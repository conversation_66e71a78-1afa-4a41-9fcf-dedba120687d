# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SessionView is a web-based REAPER project (.rpp) file analyzer that provides comprehensive analysis for music production workflows. The application features a React/TypeScript frontend with FastAPI/Python backend, specializing in "Ready for Mastering" analysis, plugin intelligence, and project export capabilities.

## Development Commands

### Quick Start
```bash
./start.sh              # Start both frontend and backend services
```

### Frontend Development
```bash
cd frontend
npm install              # Install dependencies
npm run dev             # Start development server (http://localhost:5173)
npm run build           # Production build
npm run lint            # Run ESLint
npm run start:backend   # Start backend from frontend directory
```

### Backend Development
```bash
cd backend
source venv/bin/activate                      # Activate venv (Linux/Mac)
# venv\Scripts\activate                       # Activate venv (Windows)
pip install -r requirements.txt               # Install dependencies (includes aiosqlite for database)
uvicorn main:app --reload                     # Start development server (http://localhost:8000)
```

### Testing
Tests are currently run as standalone scripts:
```bash
python test_gain_staging.py         # Test gain staging analysis
python test_mastering_phase1.py     # Test mastering analysis features
python test_automation.py           # Test automation extraction
```

## Architecture Overview

### Backend-Centric Analysis Architecture

The project implements a **backend-centric analysis architecture** where all complex analysis logic is centralized in the backend, with the frontend serving as a presentation layer.

**Core Backend Structure:**
```
backend/parsing/
├── engines/                    # Analysis engines
│   ├── mastering_analysis_engine.py  # Main "Ready for Mastering" analysis
│   ├── plugin_analysis_engine.py     # Unified plugin analysis
│   └── plugin_data/                  # In-code plugin database
├── extractors/                 # Data extraction modules
│   ├── track_extractor.py     # Track hierarchy and properties
│   ├── item_extractor.py      # Audio items and takes
│   ├── automation_utils.py    # Automation envelope extraction
│   └── routing_extractor.py   # Track routing analysis
├── infrastructure/            # Base classes and utilities
│   ├── parsing_context.py    # Dependency injection container
│   ├── base_extractor.py     # Abstract base for extractors
│   └── rpp_tree_cache.py     # Performance caching layer
└── models.py                  # Pydantic data models
```

**Key Design Patterns:**
- **Dependency Injection**: All extractors receive `ParsingContext` with shared state
- **Engine Pattern**: Complex analysis consolidated in specialized engines
- **Caching Layer**: `RPPTreeCache` optimizes RPP tree traversal
- **Structured Error Handling**: Custom exception hierarchy with context preservation

### Frontend Architecture

**Technology Stack:**
- React 19.0.0 with TypeScript
- Tailwind CSS 3.4.17 for styling
- Zustand 5.0.3 for state management
- Framer Motion 12.6.5 for animations

**Component Organization:**
```
frontend/src/
├── components/
│   ├── ui/                    # Shared UI components (Button, Select, Checkbox)
│   ├── MasteringDashboard.tsx # Main analysis dashboard
│   ├── *Section.tsx          # Analysis section components
│   └── IssueCard.tsx         # Reusable issue display
├── pages/                    # Route-based pages
├── store/sessionStore.ts     # Zustand global state
└── utils/                    # Utility functions
```

## Critical Development Patterns

### RPP Library Integration
- **Element Access**: Use `element.attrib` and `element.children`, NOT `.text`
- **Plugin Detection**: Use `element.attrib[0]` for plugin names
- **Type Detection**: Use `element.tag` directly (VST, AU, JS)
- **Pattern Matching**: Implement fallback matching for plugin name variations

### Plugin Analysis System
The project includes a comprehensive plugin intelligence system:
- **Plugin Database**: In-code database in `backend/parsing/engines/plugin_data/`
- **Categorization**: Automatic plugin type detection (limiters, compressors, EQs)
- **Blacklist Rules**: Genre-aware rules for mastering readiness analysis
- **Parameter Extraction**: Detailed plugin parameter analysis

### Genre-Aware Analysis
Analysis rules are customizable based on music genre via `genre_rules.py`:
- Different gain staging thresholds per genre
- Genre-specific plugin blacklist rules
- Customizable analysis tolerances

## API Endpoints

### Core Endpoints
- `POST /api/v1/upload` - Upload and parse RPP files
- `GET /api/v1/summary` - Get session summary
- `POST /api/v1/mastering-analysis` - "Ready for Mastering" analysis
- `POST /api/v1/export/fxchain` - Export FX chains
- `POST /api/v1/export/tracktemplate` - Export track templates
- `POST /api/v1/export/markdown` - Generate markdown reports

### Streaming & Background Processing (Phase 4)
- `GET /api/v1/stream/mastering-analysis` - Real-time analysis with Server-Sent Events
- `GET /api/v1/stream/grouped-analysis` - Real-time grouped analysis
- `POST /api/v1/background/mastering-analysis` - Start background analysis task
- `GET /api/v1/background/task/{task_id}` - Check background task status

### Performance & Monitoring
- `GET /api/v1/cache/stats` - Basic cache statistics
- `GET /api/v1/cache/detailed-stats` - Comprehensive cache analytics  
- `POST /api/v1/cache/cleanup` - Clean up old cache entries
- `POST /api/v1/cache/optimize` - Run database optimization (ANALYZE, VACUUM)
- `GET /api/v1/performance/stats` - Database and task performance metrics

### Response Structure
The API returns comprehensive structured data including:
- `issues`: Consolidated list of all analysis issues
- `overall_health_score`: Numeric readiness score
- `summary_metrics`: Key metrics summary
- `detailed_analysis`: Section-specific analysis results

## Data Models

Key Pydantic models in `backend/parsing/models.py`:
- `ParsedSession`: Main session container
- `Track`: Track information with FX chains and routing
- `Item`: Audio items with fade, playrate, and reverse properties
- `MasteringAnalysisIssue`: Structured issue reporting
- `AffectedElement`: Issue context and location data

## Mastering Analysis Rules

The project implements sophisticated "Ready for Mastering" analysis based on industry best practices:

### Plugin Blacklist Rules
- **Individual Tracks**: Demo/trial plugins, inappropriate limiters
- **Master Bus**: Mastering limiters (Pro-L, Ozone), aggressive processing
- **Metering Plugins**: Allowed and encouraged (Insight, SPAN, Youlean)

### Gain Staging Analysis
- **Default Thresholds**: +6dB max gain (adjustable by genre)
- **Genre Variations**: Electronic (+8dB), Acoustic (+4dB)
- **Clipping Detection**: High input/output parameter detection

### Session Hygiene
- **Track Naming**: Flags generic names (Track, Audio, Untitled)
- **Empty Content**: Detects empty items and MIDI tracks
- **Organization**: Checks for proper folder structure

## Testing Approach

### Current Testing
- **Integration Tests**: Real RPP files as fixtures
- **Manual Execution**: Standalone Python scripts
- **Comprehensive Fixtures**: Complex real-world projects in `tests/fixtures/`

### Test Files
- `test_gain_staging.py`: Genre-specific gain analysis
- `test_mastering_phase1.py`: Mastering analysis features
- `test_automation.py`: Automation extraction
- Real RPP fixtures: `Mid24 No1 Mix.RPP`, `Qotsa2 - Mix.RPP`

## Memory Bank System

The project uses a `memory-bank/` directory for documentation and context:
- `activeContext.md`: Current project status and recent changes
- `progress.md`: Completed work and remaining tasks
- `systemPatterns.md`: Architecture decisions and design patterns
- Implementation plans and project briefs

## Performance Architecture

The application implements a **4-phase performance optimization architecture** completed in 2025-06-29:

### Phase 1: Critical Performance Fixes ✅
- **GZip Compression**: 60-80% reduction in API response sizes
- **Pagination**: Configurable limit/offset parameters for large datasets  
- **Plugin Registry Caching**: LRU cache (maxsize=1000) for plugin lookups
- **React.memo()**: Initial component memoization for critical UI components

### Phase 2: Backend Optimizations ✅
- **Method Refactoring**: Split 174-line analysis methods into focused functions
- **Bounded Error Collection**: Prevents memory leaks with configurable limits (1000 warnings, 500 errors)
- **Session-Based Caching**: MD5-hashed caching with fallback strategies
- **Analysis Engine Caching**: In-memory LRU caching for expensive operations

### Phase 3: Frontend Performance ✅
- **Component Memoization**: React.memo() on all critical components (MetricDisplay, HealthIndicator, IssueGroupCard, TopNavBar)
- **Calculation Memoization**: useMemo() for expensive severity class calculations
- **Callback Optimization**: useCallback() for event handlers to prevent child re-renders
- **State Management**: Optimized Zustand store updates with early returns
- **Virtual Scrolling**: Pagination state for large issue lists with "Load More" pattern

### Phase 4: Architecture Improvements ✅
- **SQLite Database Caching**: Persistent analysis result caching across sessions
- **Streaming Responses**: Server-Sent Events for real-time analysis progress
- **Background Task Processing**: Async background analysis with UUID tracking
- **Connection Pooling**: High-performance async database operations with aiosqlite

### Current Performance Characteristics:
- **API Response Times**: Sub-second for cached results, 2-5s for fresh analysis
- **Memory Usage**: Bounded error collections prevent memory leaks
- **Database Performance**: Connection pooling supports high-concurrency operations  
- **Frontend Rendering**: Minimized re-renders through comprehensive memoization
- **Real-time Updates**: Streaming progress for large analysis operations

## Development Status

The project has completed core functionality with comprehensive performance optimizations:

### Core Features ✅
- Backend mastering analysis engine (✅ Complete)
- Frontend Phase B refactoring (✅ Complete)
- Shared UI component library (✅ Complete)
- Export functionality (✅ Complete)

### Advanced Features ✅
- **Plugin database restructure (✅ Complete - 2025-06-27)**
- **Fuzzy plugin matching system (✅ Complete - 2025-06-28)**
- **Phase D implementation plan (✅ Complete - 2025-06-28)**
- **Phase D1: Session Hygiene Separation (✅ Complete - 2025-06-28)**
- **Phase D2: Critical Technical Checks (✅ Complete - 2025-06-29)**

### Performance Optimization Campaign ✅
- **4-Phase Performance Architecture (✅ Complete - 2025-06-29)**
  - Phase 1: Critical Performance Fixes (API compression, caching, initial React.memo())
  - Phase 2: Backend Optimizations (method refactoring, bounded collections, analysis caching)
  - Phase 3: Frontend Performance (comprehensive memoization, virtual scrolling, state optimization)
  - Phase 4: Architecture Improvements (database caching, streaming, background tasks, connection pooling)

### Phase D2: Critical Technical Checks (Complete - 2025-06-29)

**Objective**: Add robust technical validation for mastering readiness.

**Features Implemented:**
- ✅ **Master Output Analysis**: Validates master volume levels, mute state, and automation
- ✅ **Offline Media Detection**: Detects REAPER's offline media flags (RPP-only, no filesystem access)  
- ✅ **Sample Rate Consistency**: Analyzes sample rate mismatches with smart severity assignment
- ✅ **Time-Stretch Detection**: Enhanced playrate analysis with pitch change calculations
- ✅ **Record Arm Safety**: Critical safety checks for armed tracks, especially master

**Key Design Principle**: All analysis is performed **solely on RPP file content** - no filesystem access required or attempted.

## Web App Architecture Constraints

**Critical**: This is a web application that analyzes uploaded RPP files. The backend has **no access to the user's local file system**.

### Analysis Limitations:
- ✅ **RPP Content Only**: All analysis based solely on RPP file structure and data
- ✅ **No File Validation**: Cannot verify actual media file existence or properties
- ✅ **REAPER Flags Only**: Offline media detection uses REAPER's internal offline markers
- ✅ **No Path Resolution**: Cannot resolve relative paths or check file system locations

### Implementation Guidelines:
- **Never use filesystem operations** (`os.path.exists()`, file I/O, etc.) in analysis code
- **Trust RPP metadata** for sample rates, bit depths, file properties
- **Use REAPER's internal flags** for offline/missing media detection
- **Focus on project structure** rather than external file validation